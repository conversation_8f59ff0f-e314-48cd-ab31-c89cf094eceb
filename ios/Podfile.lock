PODS:
  - awesome_notifications (0.10.0):
    - Flutter
    - IosAwnCore (~> 0.10.0)
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase/Auth (11.8.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.8.0)
  - Firebase/CoreOnly (11.8.0):
    - FirebaseCore (~> 11.8.0)
  - Firebase/Messaging (11.8.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.8.0)
  - firebase_auth (5.5.0):
    - Firebase/Auth (= 11.8.0)
    - firebase_core
    - Flutter
  - firebase_core (3.12.0):
    - Firebase/CoreOnly (= 11.8.0)
    - Flutter
  - firebase_messaging (15.2.0):
    - Firebase/Messaging (= 11.8.0)
    - firebase_core
    - Flutter
  - FirebaseAppCheckInterop (11.9.0)
  - FirebaseAuth (11.8.1):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.8.0)
    - FirebaseCoreExtension (~> 11.8.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 100.0)
  - FirebaseAuthInterop (11.9.0)
  - FirebaseCore (11.8.1):
    - FirebaseCoreInternal (~> 11.8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.8.0):
    - FirebaseCore (~> 11.8.0)
  - FirebaseCoreInternal (11.8.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseInstallations (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - Flutter (1.0.0)
  - flutter_image_compress_common (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_native_contact_picker (0.0.1):
    - Flutter
  - flutter_native_splash (0.0.1):
    - Flutter
  - flutter_restart_plus (0.0.1):
    - Flutter
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMSessionFetcher/Core (4.4.0)
  - IosAwnCore (0.10.0)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - open_filex (0.0.2):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - RecaptchaInterop (100.0.0)
  - restart_app (0.0.1):
    - Flutter
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - SwiftyGif (5.4.5)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_compress (0.3.0):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - video_thumbnail (0.0.1):
    - Flutter
    - libwebp

DEPENDENCIES:
  - awesome_notifications (from `.symlinks/plugins/awesome_notifications/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_image_compress_common (from `.symlinks/plugins/flutter_image_compress_common/ios`)
  - flutter_native_contact_picker (from `.symlinks/plugins/flutter_native_contact_picker/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_restart_plus (from `.symlinks/plugins/flutter_restart_plus/ios`)
  - open_filex (from `.symlinks/plugins/open_filex/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - restart_app (from `.symlinks/plugins/restart_app/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_compress (from `.symlinks/plugins/video_compress/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - video_thumbnail (from `.symlinks/plugins/video_thumbnail/ios`)

SPEC REPOS:
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - Firebase
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleUtilities
    - GTMSessionFetcher
    - IosAwnCore
    - libwebp
    - Mantle
    - nanopb
    - PromisesObjC
    - RecaptchaInterop
    - SDWebImage
    - SDWebImageWebPCoder
    - SwiftyGif

EXTERNAL SOURCES:
  awesome_notifications:
    :path: ".symlinks/plugins/awesome_notifications/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_image_compress_common:
    :path: ".symlinks/plugins/flutter_image_compress_common/ios"
  flutter_native_contact_picker:
    :path: ".symlinks/plugins/flutter_native_contact_picker/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_restart_plus:
    :path: ".symlinks/plugins/flutter_restart_plus/ios"
  open_filex:
    :path: ".symlinks/plugins/open_filex/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  restart_app:
    :path: ".symlinks/plugins/restart_app/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_compress:
    :path: ".symlinks/plugins/video_compress/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  video_thumbnail:
    :path: ".symlinks/plugins/video_thumbnail/ios"

SPEC CHECKSUMS:
  awesome_notifications: dd5518ff1c80be03d4f1c40f04da9d9cc2a37af5
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: b159e0c068aef54932bb15dc9fd1571818edaf49
  Firebase: d80354ed7f6df5f9aca55e9eb47cc4b634735eaf
  firebase_auth: cecfcb5b0655e1cb66a5c45a88ee77811ca93514
  firebase_core: 6e223dfa350b2edceb729cea505eaaef59330682
  firebase_messaging: 16c969babeb6257a7632b823e568b00070605d55
  FirebaseAppCheckInterop: 9226f7217b43e99dfa0bc9f674ad8108cef89feb
  FirebaseAuth: ad59a1a7b161e75f74c39f70179d2482d40e2737
  FirebaseAuthInterop: 2a26ee1bea6d47df8048683cfa071e7da657798f
  FirebaseCore: 99fe0c4b44a39f37d99e6404e02009d2db5d718d
  FirebaseCoreExtension: 3d3f2017a00d06e09ab4ebe065391b0bb642565e
  FirebaseCoreInternal: df24ce5af28864660ecbd13596fc8dd3a8c34629
  FirebaseInstallations: 6c963bd2a86aca0481eef4f48f5a4df783ae5917
  FirebaseMessaging: 487b634ccdf6f7b7ff180fdcb2a9935490f764e8
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_image_compress_common: ec1d45c362c9d30a3f6a0426c297f47c52007e3e
  flutter_native_contact_picker: 390f08cc44058386ee91499f05360d647dddfc66
  flutter_native_splash: edf599c81f74d093a4daf8e17bd7a018854bc778
  flutter_restart_plus: 4876e715ed2a09cf6cb4a73b22fe9a2ca3d682e1
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  GTMSessionFetcher: 75b671f9e551e4c49153d4c4f8659ef4f559b970
  IosAwnCore: 653786a911089012092ce831f2945cd339855a89
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  open_filex: 6e26e659846ec990262224a12ef1c528bb4edbe4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21
  restart_app: 806659942bf932f6ce51c5372f91ce5e81c8c14a
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  video_compress: fce97e4fb1dfd88175aa07d2ffc8a2f297f87fbe
  video_player_avfoundation: 7c6c11d8470e1675df7397027218274b6d2360b3
  video_thumbnail: c4e2a3c539e247d4de13cd545344fd2d26ffafd1

PODFILE CHECKSUM: 46ea3d33d14cb825690d391780ca0be6c3c68f36

COCOAPODS: 1.16.2
