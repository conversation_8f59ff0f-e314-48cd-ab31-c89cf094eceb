name: connectify_app
description: "Connectify."

publish_to: 'none'

version: 1.0.50+50

environment:
  sdk: '>=3.5.3 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  flutter_localizations:
    sdk: flutter

  # * Best Package *
  xr_helper:
    path: packages/xr_helper

  cupertino_icons: ^1.0.6

  #? UI
  intl: ^0.20.2
  #? State Management
  flutter_riverpod: ^2.4.9
  riverpod: ^2.4.9
  hooks_riverpod: ^2.4.9
  flutter_hooks: ^0.20.4
  flutter_native_contact_picker: ^0.0.10

  #? Equatable (Compare objects)
  equatable: ^2.0.5

  #? Dots Indicator
  dots_indicator: ^3.0.0

  #? Widgets
  fast_cached_network_image: ^1.3.2+4
  shimmer: ^3.0.0
  timeline_tile: ^2.0.0
  dotted_border: ^2.0.0+3
  flutter_screenutil: ^5.9.0
  file_picker: ^9.0.0
  permission_handler: ^11.4.0
  lottie: ^2.6.0
  google_fonts: ^6.1.0
  fl_chart: ^0.65.0
  auto_height_grid_view: ^1.0.0
  flutter_expandable_fab: ^2.0.0
  table_calendar:
  syncfusion_flutter_charts: ^28.2.5+1
  path_provider: ^2.1.5
#  syncfusion_flutter_charts:
#    path: packages/syncfusion_flutter_charts
  url_launcher: ^6.3.1
  flutter_rating_bar: ^4.0.1
  awesome_notifications: ^0.10.1

  #? Images
  flutter_svg: ^2.2.0

  #? OTP
  pin_code_fields: ^8.0.1
  circular_countdown_timer: ^0.2.3
  video_thumbnail: ^0.5.6

  #? Generate Assets
  flutter_gen: ^5.4.0

  #? Helpers
  collection: ^1.15.0
  flutter_image_compress: ^2.4.0
  video_compress: ^3.1.4
  video_player: ^2.10.0
  restart_app: ^1.2.1
  flutter_restart_plus: ^0.0.1

  #? Firebase
  firebase_auth: ^5.5.0
  firebase_core: ^3.10.0
  firebase_messaging: ^15.2.0
  googleapis: ^13.2.0
  googleapis_auth: ^1.6.0

  flutter_staggered_grid_view: ^0.7.0

  #? Email Service
  mailer: ^6.2.0

  quickalert: ^1.1.0
  in_app_update: ^4.2.3
  open_filex: ^4.7.0
  pdf: ^3.11.1

dependency_overrides:
  archive: ^3.6.1
  win32: ^5.5.4
  url_launcher: ^6.3.1


dev_dependencies:

  flutter_test:
    sdk: flutter

  build_runner:
  flutter_gen_runner:
  flutter_lints: ^3.0.1
  intl_utils: ^2.8.10

#  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.3.9

#? dart run flutter_launcher_icons:main
#flutter_launcher_icons:
#  android: true
#  ios: true
#  remove_alpha_ios: true
#  image_path: "assets/images/logo.jpeg"
##
## ? dart run flutter_native_splash:create
flutter_native_splash:
  android: true
  ios: true
  web: false
  fullscreen: false
  color: '#ffffff'
  image: 'assets/images/logo.jpeg'
  android_12:
    color: '#ffffff'
    image: 'assets/images/logo.jpeg'

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/animated/
    - assets/svg/

  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/cairo/Cairo-Regular.ttf
        - asset: assets/fonts/cairo/Cairo-Bold.ttf
        - asset: assets/fonts/cairo/Cairo-Black.ttf
        - asset: assets/fonts/cairo/Cairo-ExtraLight.ttf
        - asset: assets/fonts/cairo/Cairo-Light.ttf
        - asset: assets/fonts/cairo/Cairo-SemiBold.ttf


flutter_intl:
  enabled: true

