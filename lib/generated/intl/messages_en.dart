// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(name) => "Are you sure you want to delete ${name}?";

  static String m1(date) =>
      "Are you sure to make this subscription paid for ${date}?";

  static String m2(days) => "${days} days";

  static String m3(months) => "${months} months old";

  static String m4(months) => "${months}m";

  static String m5(name) => "Send a new message to ${name}";

  static String m6(studentName) =>
      "${studentName} subscription payment is due today. Please collect the payment.";

  static String m7(weeks) => "${weeks} weeks old";

  static String m8(weeks) => "${weeks}w";

  static String m9(years, months) => "${years}y ${months}m";

  static String m10(years) => "${years} years old";

  static String m11(years) => "${years}y";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "IfYouContinueThisWillAffectParentsAppAndTheyCanSeeAllChildrenUnderTheirNumber":
        MessageLookupByLibrary.simpleMessage(
          "If you continue this will affect parents app and they can see all children under their number",
        ),
    "Of": MessageLookupByLibrary.simpleMessage("Of"),
    "SignupAsa": MessageLookupByLibrary.simpleMessage("Sign up as a"),
    "SkipForNow": MessageLookupByLibrary.simpleMessage("Skip for now"),
    "absent": MessageLookupByLibrary.simpleMessage("absent"),
    "accessPermissions": MessageLookupByLibrary.simpleMessage(
      "Access Permissions",
    ),
    "accountant": MessageLookupByLibrary.simpleMessage("Accountant"),
    "accountantRoleDescription": MessageLookupByLibrary.simpleMessage(
      "Handles financial records and student payments",
    ),
    "active": MessageLookupByLibrary.simpleMessage("Active"),
    "activeStudents": MessageLookupByLibrary.simpleMessage("Active Students"),
    "activities": MessageLookupByLibrary.simpleMessage("Activities"),
    "activitiesCompleted": MessageLookupByLibrary.simpleMessage(
      "Activities completed",
    ),
    "activity": MessageLookupByLibrary.simpleMessage("Activity"),
    "activityChart": MessageLookupByLibrary.simpleMessage("Activity chart"),
    "activityDescription": MessageLookupByLibrary.simpleMessage(
      "Activity Description",
    ),
    "activityLevel": MessageLookupByLibrary.simpleMessage("Activity Level"),
    "activityName": MessageLookupByLibrary.simpleMessage("Activity Name"),
    "add": MessageLookupByLibrary.simpleMessage("Add"),
    "addANewStaffMember": MessageLookupByLibrary.simpleMessage(
      "Add a new staff member",
    ),
    "addActivity": MessageLookupByLibrary.simpleMessage("Add Activity"),
    "addActivityLevel": MessageLookupByLibrary.simpleMessage(
      "Add Activity Level",
    ),
    "addAnnouncement": MessageLookupByLibrary.simpleMessage("Add Announcement"),
    "addAnnouncements": MessageLookupByLibrary.simpleMessage(
      "Add Announcements",
    ),
    "addAttendance": MessageLookupByLibrary.simpleMessage("Add Attendance"),
    "addEvaluation": MessageLookupByLibrary.simpleMessage("Add Evaluation"),
    "addEvents": MessageLookupByLibrary.simpleMessage("Add Events"),
    "addExams": MessageLookupByLibrary.simpleMessage("Add Exams"),
    "addFood": MessageLookupByLibrary.simpleMessage("Add Food"),
    "addImage": MessageLookupByLibrary.simpleMessage("Add Image"),
    "addMedia": MessageLookupByLibrary.simpleMessage("Add Media"),
    "addMood": MessageLookupByLibrary.simpleMessage("Add Mood"),
    "addNewBill": MessageLookupByLibrary.simpleMessage("Add New Bill"),
    "addNewClass": MessageLookupByLibrary.simpleMessage("Add New Class"),
    "addNewEvent": MessageLookupByLibrary.simpleMessage("Add New Event"),
    "addNewInvoice": MessageLookupByLibrary.simpleMessage("Add New Income"),
    "addNewQuestion": MessageLookupByLibrary.simpleMessage("Add New Question"),
    "addNewSection": MessageLookupByLibrary.simpleMessage("Add New Section"),
    "addNewStudents": MessageLookupByLibrary.simpleMessage("Add New Students"),
    "addNote": MessageLookupByLibrary.simpleMessage("Add Note"),
    "addNurseryActivities": MessageLookupByLibrary.simpleMessage(
      "Add New Activity ",
    ),
    "addNurseryTeam": MessageLookupByLibrary.simpleMessage("Add Nursery Team"),
    "addNurseryTeamMember": MessageLookupByLibrary.simpleMessage(
      "Add New Team Member",
    ),
    "addParentsPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Add Parents Phone number",
    ),
    "addPickupPerson": MessageLookupByLibrary.simpleMessage(
      "Add Pickup Person",
    ),
    "addPickups": MessageLookupByLibrary.simpleMessage("Add Pickups"),
    "addPlan": MessageLookupByLibrary.simpleMessage("Add Plan"),
    "addPlans": MessageLookupByLibrary.simpleMessage("Add Plans"),
    "addQuestion": MessageLookupByLibrary.simpleMessage("Add Question"),
    "addResults": MessageLookupByLibrary.simpleMessage("Add Results"),
    "addSleep": MessageLookupByLibrary.simpleMessage("Add Sleep"),
    "addStaffMember": MessageLookupByLibrary.simpleMessage("Add Staff Member"),
    "addStudents": MessageLookupByLibrary.simpleMessage("Add Students"),
    "addSupply": MessageLookupByLibrary.simpleMessage("Add Supply"),
    "addToilet": MessageLookupByLibrary.simpleMessage("Add Toilet"),
    "addVideo": MessageLookupByLibrary.simpleMessage("Add Video"),
    "addedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Added successfully",
    ),
    "address": MessageLookupByLibrary.simpleMessage("Address"),
    "adminAlreadyRegistered": MessageLookupByLibrary.simpleMessage(
      "Admin already registered",
    ),
    "adminSignUp": MessageLookupByLibrary.simpleMessage(
      "Administrator Sign up",
    ),
    "administrator": MessageLookupByLibrary.simpleMessage("Administrator"),
    "all": MessageLookupByLibrary.simpleMessage("All"),
    "allMonths": MessageLookupByLibrary.simpleMessage("All Months"),
    "allParents": MessageLookupByLibrary.simpleMessage("All Parents"),
    "allStudents": MessageLookupByLibrary.simpleMessage("All Students"),
    "alreadyHaveAnAccount": MessageLookupByLibrary.simpleMessage(
      "Already have an account?",
    ),
    "amount": MessageLookupByLibrary.simpleMessage("Amount"),
    "angry": MessageLookupByLibrary.simpleMessage("Angry"),
    "announcements": MessageLookupByLibrary.simpleMessage("Announcements"),
    "answers": MessageLookupByLibrary.simpleMessage("Answers"),
    "april": MessageLookupByLibrary.simpleMessage("April"),
    "arabic": MessageLookupByLibrary.simpleMessage("Arabic"),
    "areYouSureDeleteStaffMember": m0,
    "areYouSureToDeleteThisActivity": MessageLookupByLibrary.simpleMessage(
      "Are you sure to delete this activity ?",
    ),
    "areYouSureToDeleteThisBill": MessageLookupByLibrary.simpleMessage(
      "Are you sure to delete this bill ?",
    ),
    "areYouSureToDeleteThisClass": MessageLookupByLibrary.simpleMessage(
      "Are you sure to delete this class ?",
    ),
    "areYouSureToDeleteThisEvent": MessageLookupByLibrary.simpleMessage(
      "Are you sure to delete this event ?",
    ),
    "areYouSureToDeleteThisInvoice": MessageLookupByLibrary.simpleMessage(
      "Are you sure to delete this Income ?",
    ),
    "areYouSureToDeleteThisQuestion": MessageLookupByLibrary.simpleMessage(
      "Are you sure to delete this question ?",
    ),
    "areYouSureToDeleteThisStudent": MessageLookupByLibrary.simpleMessage(
      "Are you sure to delete this student ?",
    ),
    "areYouSureToDeleteThisSupply": MessageLookupByLibrary.simpleMessage(
      "Are you sure to delete this supply ?",
    ),
    "areYouSureToDeleteThisTeacher": MessageLookupByLibrary.simpleMessage(
      "Are you sure to delete this teacher ?",
    ),
    "areYouSureToDeleteYourAccount": MessageLookupByLibrary.simpleMessage(
      "Are you sure to delete your account ?",
    ),
    "areYouSureToMakeThisSubscriptionPaid":
        MessageLookupByLibrary.simpleMessage(
          "Are you sure to make this subscription paid ?",
        ),
    "areYouSureToMakeThisSubscriptionPaidFor": m1,
    "areYouSureToSendSubscriptionRemind": MessageLookupByLibrary.simpleMessage(
      "Are you sure to send subscription remind ?",
    ),
    "areYouSureYouWantToDeleteThisAnnouncement":
        MessageLookupByLibrary.simpleMessage(
          "Are you sure you want to delete this announcement?",
        ),
    "areYouSureYouWantToDeleteThisPlan": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this plan?",
    ),
    "assign": MessageLookupByLibrary.simpleMessage("Assign"),
    "assignActivity": MessageLookupByLibrary.simpleMessage("Assign Activity"),
    "assignActivityToClass": MessageLookupByLibrary.simpleMessage(
      "Assign Activity To Class",
    ),
    "assignSupplyToStudent": MessageLookupByLibrary.simpleMessage(
      "Assign Supply to Student",
    ),
    "assignToClass": MessageLookupByLibrary.simpleMessage("Assign to class"),
    "assigned": MessageLookupByLibrary.simpleMessage("Assigned"),
    "attendance": MessageLookupByLibrary.simpleMessage("Attendance"),
    "attendanceChart": MessageLookupByLibrary.simpleMessage("Attendance chart"),
    "attendanceTracking": MessageLookupByLibrary.simpleMessage(
      "Attendance Tracking",
    ),
    "attended": MessageLookupByLibrary.simpleMessage("attended"),
    "attendeesOfToday": MessageLookupByLibrary.simpleMessage(
      "Attendees of Today",
    ),
    "august": MessageLookupByLibrary.simpleMessage("August"),
    "back": MessageLookupByLibrary.simpleMessage("Back"),
    "billAmount": MessageLookupByLibrary.simpleMessage("Bill Amount"),
    "billName": MessageLookupByLibrary.simpleMessage("Bill Name"),
    "bills": MessageLookupByLibrary.simpleMessage("Bills"),
    "billsChart": MessageLookupByLibrary.simpleMessage("Bills chart"),
    "birthDate": MessageLookupByLibrary.simpleMessage("Birth Date"),
    "breakfast": MessageLookupByLibrary.simpleMessage("Breakfast"),
    "by": MessageLookupByLibrary.simpleMessage("By"),
    "calm": MessageLookupByLibrary.simpleMessage("Calm"),
    "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "cannotAddStudentWithThisPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Cannot add student with this phone number",
    ),
    "changeLanguage": MessageLookupByLibrary.simpleMessage("Change Language"),
    "changePassword": MessageLookupByLibrary.simpleMessage("Change Password"),
    "checkOurLatestJobApplicationsNow": MessageLookupByLibrary.simpleMessage(
      "Check our latest job applications now!",
    ),
    "chooseActivityAssignType": MessageLookupByLibrary.simpleMessage(
      "Choose activity assign type",
    ),
    "city": MessageLookupByLibrary.simpleMessage("City"),
    "classActivities": MessageLookupByLibrary.simpleMessage("Class Activities"),
    "classDescription": MessageLookupByLibrary.simpleMessage(
      "Class Description",
    ),
    "className": MessageLookupByLibrary.simpleMessage("Class Name"),
    "classes": MessageLookupByLibrary.simpleMessage("Classes"),
    "clear": MessageLookupByLibrary.simpleMessage("Clear"),
    "clickToContact": MessageLookupByLibrary.simpleMessage("Click to contact"),
    "clothes": MessageLookupByLibrary.simpleMessage("Clothes"),
    "complete": MessageLookupByLibrary.simpleMessage("Complete"),
    "completeVerification": MessageLookupByLibrary.simpleMessage(
      "Complete Verification",
    ),
    "compressing": MessageLookupByLibrary.simpleMessage("Compressing"),
    "compressingVideo": MessageLookupByLibrary.simpleMessage(
      "Compressing video...",
    ),
    "confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
    "confirmDelete": MessageLookupByLibrary.simpleMessage("Confirm Delete"),
    "confirmNewPassword": MessageLookupByLibrary.simpleMessage(
      "Confirm new password",
    ),
    "confirmation": MessageLookupByLibrary.simpleMessage("Confirmation"),
    "congratulations": MessageLookupByLibrary.simpleMessage("Congratulations"),
    "contactSupport": MessageLookupByLibrary.simpleMessage("Contact Support"),
    "createANewClass": MessageLookupByLibrary.simpleMessage(
      "Create a new class",
    ),
    "createANewSupply": MessageLookupByLibrary.simpleMessage(
      "Add a new Supply",
    ),
    "createNewClass": MessageLookupByLibrary.simpleMessage("Create new class"),
    "currentActivity": MessageLookupByLibrary.simpleMessage("Current Activity"),
    "currentMonth": MessageLookupByLibrary.simpleMessage("Current month"),
    "cv": MessageLookupByLibrary.simpleMessage("CV"),
    "dailyActivities": MessageLookupByLibrary.simpleMessage("Daily Activities"),
    "dailySchedule": MessageLookupByLibrary.simpleMessage("Daily Schedule"),
    "dashboard": MessageLookupByLibrary.simpleMessage("Dashboard"),
    "date": MessageLookupByLibrary.simpleMessage("Date"),
    "day": MessageLookupByLibrary.simpleMessage("Day"),
    "daysOld": m2,
    "december": MessageLookupByLibrary.simpleMessage("December"),
    "delete": MessageLookupByLibrary.simpleMessage("Delete"),
    "deleteAccount": MessageLookupByLibrary.simpleMessage("Delete Account"),
    "deleteAnnouncement": MessageLookupByLibrary.simpleMessage(
      "Delete Announcement",
    ),
    "deletePlan": MessageLookupByLibrary.simpleMessage("Delete Plan"),
    "deletedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "deleted successfully",
    ),
    "description": MessageLookupByLibrary.simpleMessage("Description"),
    "diaper": MessageLookupByLibrary.simpleMessage("Diaper"),
    "didNotGetCode": MessageLookupByLibrary.simpleMessage(
      "Didn’t get the code?",
    ),
    "dontHaveAnAccount": MessageLookupByLibrary.simpleMessage(
      "You don’t have account?",
    ),
    "due": MessageLookupByLibrary.simpleMessage("Due"),
    "edit": MessageLookupByLibrary.simpleMessage("Edit"),
    "editActivity": MessageLookupByLibrary.simpleMessage("Edit Activity"),
    "editAnnouncement": MessageLookupByLibrary.simpleMessage(
      "Edit Announcement",
    ),
    "editClass": MessageLookupByLibrary.simpleMessage("Edit Class"),
    "editEvent": MessageLookupByLibrary.simpleMessage("Edit Event"),
    "editFood": MessageLookupByLibrary.simpleMessage("Edit Food"),
    "editMood": MessageLookupByLibrary.simpleMessage("Edit Mood"),
    "editPermissions": MessageLookupByLibrary.simpleMessage("Edit Permissions"),
    "editPermissionsFor": MessageLookupByLibrary.simpleMessage(
      "Edit permissions for",
    ),
    "editPlan": MessageLookupByLibrary.simpleMessage("Edit Plan"),
    "editSleep": MessageLookupByLibrary.simpleMessage("Edit Sleep"),
    "editStaffMember": MessageLookupByLibrary.simpleMessage(
      "Edit Staff Member",
    ),
    "editSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Edit successfully",
    ),
    "editSupply": MessageLookupByLibrary.simpleMessage("Edit Supply"),
    "editTeacher": MessageLookupByLibrary.simpleMessage("Edit Teacher"),
    "editToilet": MessageLookupByLibrary.simpleMessage("Edit Toilet"),
    "editedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Edited Successfully",
    ),
    "education": MessageLookupByLibrary.simpleMessage("Education"),
    "email": MessageLookupByLibrary.simpleMessage("Email"),
    "emailRequired": MessageLookupByLibrary.simpleMessage("Email is required"),
    "emergency": MessageLookupByLibrary.simpleMessage("Emergency"),
    "english": MessageLookupByLibrary.simpleMessage("English"),
    "enter": MessageLookupByLibrary.simpleMessage("Enter"),
    "enterEmail": MessageLookupByLibrary.simpleMessage("Enter email"),
    "enterEtisalatCashNumber": MessageLookupByLibrary.simpleMessage(
      "Enter Etisalat Cash number",
    ),
    "enterInstapayNumberOrLink": MessageLookupByLibrary.simpleMessage(
      "Enter InstaPay number or link",
    ),
    "enterJobTitle": MessageLookupByLibrary.simpleMessage("Enter job title"),
    "enterName": MessageLookupByLibrary.simpleMessage("Enter name"),
    "enterNewPassword": MessageLookupByLibrary.simpleMessage(
      "Enter new password",
    ),
    "enterNote": MessageLookupByLibrary.simpleMessage("Enter note"),
    "enterOrangeCashNumber": MessageLookupByLibrary.simpleMessage(
      "Enter Orange Cash number",
    ),
    "enterOtp": MessageLookupByLibrary.simpleMessage("Enter OTP"),
    "enterPassword": MessageLookupByLibrary.simpleMessage("Enter password"),
    "enterPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Enter phone number",
    ),
    "enterPhoneNumberFirst": MessageLookupByLibrary.simpleMessage(
      "First enter your phone number",
    ),
    "enterPickupPerson": MessageLookupByLibrary.simpleMessage(
      "Enter pickup person",
    ),
    "enterSectionDescription": MessageLookupByLibrary.simpleMessage(
      "Enter section description",
    ),
    "enterSectionTitle": MessageLookupByLibrary.simpleMessage(
      "Enter section title",
    ),
    "enterValidNurseryName": MessageLookupByLibrary.simpleMessage(
      "Enter valid nursery name",
    ),
    "enterValidPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Enter valid phone number",
    ),
    "enterVodafoneCashNumber": MessageLookupByLibrary.simpleMessage(
      "Enter Vodafone Cash number",
    ),
    "enterWeCashNumber": MessageLookupByLibrary.simpleMessage(
      "Enter WE Cash number",
    ),
    "errorLoadingAnswers": MessageLookupByLibrary.simpleMessage(
      "Error loading answers",
    ),
    "errorLoadingEvaluations": MessageLookupByLibrary.simpleMessage(
      "Error loading evaluations",
    ),
    "errorOccurred": MessageLookupByLibrary.simpleMessage("Error occurred"),
    "evaluation": MessageLookupByLibrary.simpleMessage("Evaluation"),
    "evaluationDate": MessageLookupByLibrary.simpleMessage("Evaluation Date"),
    "evaluationQuestions": MessageLookupByLibrary.simpleMessage(
      "Evaluation Questions",
    ),
    "evaluationReport": MessageLookupByLibrary.simpleMessage(
      "Evaluation Report",
    ),
    "evaluationSummary": MessageLookupByLibrary.simpleMessage(
      "Evaluation Summary",
    ),
    "evaluations": MessageLookupByLibrary.simpleMessage("Evaluations"),
    "eventName": MessageLookupByLibrary.simpleMessage("Event Name"),
    "eventThisMonth": MessageLookupByLibrary.simpleMessage("Event this month"),
    "eventType": MessageLookupByLibrary.simpleMessage("Event Type"),
    "events": MessageLookupByLibrary.simpleMessage("Events"),
    "exams": MessageLookupByLibrary.simpleMessage("Exams"),
    "excited": MessageLookupByLibrary.simpleMessage("Excited"),
    "expectedSalary": MessageLookupByLibrary.simpleMessage("Expected Salary"),
    "exportReport": MessageLookupByLibrary.simpleMessage("Export Report"),
    "failedToExportPdf": MessageLookupByLibrary.simpleMessage(
      "Failed to export PDF",
    ),
    "failedToUpdatePaymentMethods": MessageLookupByLibrary.simpleMessage(
      "Failed to update payment methods",
    ),
    "father": MessageLookupByLibrary.simpleMessage("Father"),
    "february": MessageLookupByLibrary.simpleMessage("February"),
    "fees": MessageLookupByLibrary.simpleMessage("Fees"),
    "filterByClass": MessageLookupByLibrary.simpleMessage("Filter by Class"),
    "filterByDate": MessageLookupByLibrary.simpleMessage("Filter by Date"),
    "financial": MessageLookupByLibrary.simpleMessage("Financial"),
    "finishLetsStart": MessageLookupByLibrary.simpleMessage(
      "Finish, let’s start",
    ),
    "first100": MessageLookupByLibrary.simpleMessage("First 100"),
    "food": MessageLookupByLibrary.simpleMessage("Food"),
    "foodAdded": MessageLookupByLibrary.simpleMessage(
      "Food added successfully",
    ),
    "forgetPassword": MessageLookupByLibrary.simpleMessage("Forget Password"),
    "friday": MessageLookupByLibrary.simpleMessage("Friday"),
    "from": MessageLookupByLibrary.simpleMessage("From"),
    "fromTimeShouldBeBeforeToTime": MessageLookupByLibrary.simpleMessage(
      "From time should be before to time",
    ),
    "fromTimeShouldNotBeEqualToToTime": MessageLookupByLibrary.simpleMessage(
      "From time should not be equal to to time",
    ),
    "gender": MessageLookupByLibrary.simpleMessage("Gender"),
    "getTalkingFrom": MessageLookupByLibrary.simpleMessage(
      "Every day is a journey. \nSign in to join us.",
    ),
    "goodAfternoon": MessageLookupByLibrary.simpleMessage("Good Afternoon!"),
    "goodMorning": MessageLookupByLibrary.simpleMessage("Good Morning!"),
    "happy": MessageLookupByLibrary.simpleMessage("Happy"),
    "haveAnyQuestionsContactUs": MessageLookupByLibrary.simpleMessage(
      "Have any questions?\nContact us",
    ),
    "high": MessageLookupByLibrary.simpleMessage("High"),
    "history": MessageLookupByLibrary.simpleMessage("History"),
    "home": MessageLookupByLibrary.simpleMessage("Home"),
    "homeAddress": MessageLookupByLibrary.simpleMessage("Home Address"),
    "iHaveReadThe": MessageLookupByLibrary.simpleMessage("I have read the"),
    "inClothes": MessageLookupByLibrary.simpleMessage("Clothes"),
    "inTheDiaper": MessageLookupByLibrary.simpleMessage("Diaper"),
    "inTheToilet": MessageLookupByLibrary.simpleMessage("Toilet"),
    "inactiveStudents": MessageLookupByLibrary.simpleMessage(
      "Inactive Students",
    ),
    "incomeChart": MessageLookupByLibrary.simpleMessage("Income chart"),
    "invalidEmail": MessageLookupByLibrary.simpleMessage(
      "Invalid email format",
    ),
    "invoiceAmount": MessageLookupByLibrary.simpleMessage("Income Amount"),
    "invoiceName": MessageLookupByLibrary.simpleMessage("Income Name"),
    "invoices": MessageLookupByLibrary.simpleMessage("Income"),
    "invoicesChart": MessageLookupByLibrary.simpleMessage("Income chart"),
    "january": MessageLookupByLibrary.simpleMessage("January"),
    "jobApplication": MessageLookupByLibrary.simpleMessage("Job Application"),
    "jobApplicationDetails": MessageLookupByLibrary.simpleMessage(
      "Job Application Details",
    ),
    "jobApplications": MessageLookupByLibrary.simpleMessage("Job Applications"),
    "jobTitle": MessageLookupByLibrary.simpleMessage("Job Title"),
    "jobs": MessageLookupByLibrary.simpleMessage("Jobs"),
    "joinedIn": MessageLookupByLibrary.simpleMessage("Joined in"),
    "july": MessageLookupByLibrary.simpleMessage("July"),
    "june": MessageLookupByLibrary.simpleMessage("June"),
    "largeDatasetDetected": MessageLookupByLibrary.simpleMessage(
      "Large dataset detected. Exporting first 100 responses for optimal performance.",
    ),
    "letsDoAGreatJob": MessageLookupByLibrary.simpleMessage(
      "Let’s do a great job",
    ),
    "letsStart": MessageLookupByLibrary.simpleMessage("Let’s Start"),
    "logout": MessageLookupByLibrary.simpleMessage("Logout"),
    "low": MessageLookupByLibrary.simpleMessage("Low"),
    "lunch": MessageLookupByLibrary.simpleMessage("Lunch"),
    "march": MessageLookupByLibrary.simpleMessage("March"),
    "matherPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Mather Phone number",
    ),
    "maxStudents": MessageLookupByLibrary.simpleMessage("Max Students"),
    "maxStudentsReachedPleaseContactSupport":
        MessageLookupByLibrary.simpleMessage(
          "Max students reached, please contact support !",
        ),
    "maxUploadFileSizeIsOnly5MB": MessageLookupByLibrary.simpleMessage(
      "Max upload image size is only 5 MB",
    ),
    "maxUploadFilesIsOnly13": MessageLookupByLibrary.simpleMessage(
      "Max upload files is only 13 (10 images + 3 videos)",
    ),
    "maxUploadFilesIsOnly4": MessageLookupByLibrary.simpleMessage(
      "Max upload images is only 4",
    ),
    "maxUploadVideosIsOnly3": MessageLookupByLibrary.simpleMessage(
      "Max upload videos is only 3",
    ),
    "may": MessageLookupByLibrary.simpleMessage("May"),
    "mealAmount": MessageLookupByLibrary.simpleMessage("Meal Amount"),
    "mealType": MessageLookupByLibrary.simpleMessage("Meal Type"),
    "meals": MessageLookupByLibrary.simpleMessage("Meals"),
    "media": MessageLookupByLibrary.simpleMessage("Media"),
    "medium": MessageLookupByLibrary.simpleMessage("Medium"),
    "members": MessageLookupByLibrary.simpleMessage("Members"),
    "message": MessageLookupByLibrary.simpleMessage("Message"),
    "messageSentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Message sent successfully",
    ),
    "messages": MessageLookupByLibrary.simpleMessage("Messages"),
    "monday": MessageLookupByLibrary.simpleMessage("Monday"),
    "month": MessageLookupByLibrary.simpleMessage("Month"),
    "monthlyTotal": MessageLookupByLibrary.simpleMessage("Monthly Total"),
    "monthsOld": m3,
    "monthsShort": m4,
    "mood": MessageLookupByLibrary.simpleMessage("Mood"),
    "moodAdded": MessageLookupByLibrary.simpleMessage(
      "Mood added successfully",
    ),
    "moodType": MessageLookupByLibrary.simpleMessage("Mood Type"),
    "more": MessageLookupByLibrary.simpleMessage("More"),
    "mother": MessageLookupByLibrary.simpleMessage("Mother"),
    "motherOrParentAlreadyRegistered": MessageLookupByLibrary.simpleMessage(
      "Mother or Parent already registered",
    ),
    "motherPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Mother Phone number",
    ),
    "myClass": MessageLookupByLibrary.simpleMessage("My Class"),
    "myClasses": MessageLookupByLibrary.simpleMessage("My Classes"),
    "name": MessageLookupByLibrary.simpleMessage("Name"),
    "nameRequired": MessageLookupByLibrary.simpleMessage("Name is required"),
    "newborn": MessageLookupByLibrary.simpleMessage("Newborn"),
    "newbornShort": MessageLookupByLibrary.simpleMessage("Newborn"),
    "next": MessageLookupByLibrary.simpleMessage("Next"),
    "noActivities": MessageLookupByLibrary.simpleMessage("No Activities"),
    "noActivitiesForThisDate": MessageLookupByLibrary.simpleMessage(
      "No activities for this date",
    ),
    "noActivitiesFound": MessageLookupByLibrary.simpleMessage(
      "No activities found",
    ),
    "noBills": MessageLookupByLibrary.simpleMessage("No Bills"),
    "noClasses": MessageLookupByLibrary.simpleMessage("No Classes"),
    "noData": MessageLookupByLibrary.simpleMessage("No Data"),
    "noDataToExport": MessageLookupByLibrary.simpleMessage("No data to export"),
    "noDate": MessageLookupByLibrary.simpleMessage("No Date"),
    "noEvaluationAnswers": MessageLookupByLibrary.simpleMessage(
      "No evaluation answers found",
    ),
    "noEvaluations": MessageLookupByLibrary.simpleMessage(
      "No evaluations found",
    ),
    "noEvents": MessageLookupByLibrary.simpleMessage("No events"),
    "noHistoryForThisDate": MessageLookupByLibrary.simpleMessage(
      "No history for this date",
    ),
    "noInvoices": MessageLookupByLibrary.simpleMessage("No Income"),
    "noJobApplications": MessageLookupByLibrary.simpleMessage(
      "No job applications found",
    ),
    "noMedia": MessageLookupByLibrary.simpleMessage("No Media"),
    "noNotifications": MessageLookupByLibrary.simpleMessage("No Notifications"),
    "noPersons": MessageLookupByLibrary.simpleMessage("No persons"),
    "noPlansForThisMonth": MessageLookupByLibrary.simpleMessage(
      "No plans for this month",
    ),
    "noQuestions": MessageLookupByLibrary.simpleMessage("No questions"),
    "noResultsFound": MessageLookupByLibrary.simpleMessage("No results found"),
    "noStaffMembers": MessageLookupByLibrary.simpleMessage(
      "No staff members found",
    ),
    "noStudents": MessageLookupByLibrary.simpleMessage("No Students"),
    "noSubscriptionDateSet": MessageLookupByLibrary.simpleMessage(
      "No subscription date set",
    ),
    "noSupplies": MessageLookupByLibrary.simpleMessage("No Supplies"),
    "noTeachers": MessageLookupByLibrary.simpleMessage("No Teachers"),
    "noVideos": MessageLookupByLibrary.simpleMessage("No Videos"),
    "none": MessageLookupByLibrary.simpleMessage("None"),
    "notAvailableForAccountant": MessageLookupByLibrary.simpleMessage(
      "Not available for accountant",
    ),
    "note": MessageLookupByLibrary.simpleMessage("Note"),
    "notifications": MessageLookupByLibrary.simpleMessage("Notifications"),
    "november": MessageLookupByLibrary.simpleMessage("November"),
    "numberOfResponses": MessageLookupByLibrary.simpleMessage(
      "Number of responses",
    ),
    "numberOfStudents": MessageLookupByLibrary.simpleMessage(
      "Number of Students",
    ),
    "nurseryActivities": MessageLookupByLibrary.simpleMessage(
      "Add a Nursery Activities",
    ),
    "nurseryDataNotFound": MessageLookupByLibrary.simpleMessage(
      "Nursery data not found",
    ),
    "nurseryInformation": MessageLookupByLibrary.simpleMessage(
      "Nursery Information",
    ),
    "nurseryLogo": MessageLookupByLibrary.simpleMessage("Nursery Logo"),
    "nurseryName": MessageLookupByLibrary.simpleMessage("Nursery Name"),
    "october": MessageLookupByLibrary.simpleMessage("October"),
    "oneDay": MessageLookupByLibrary.simpleMessage("1 day"),
    "oneMonth": MessageLookupByLibrary.simpleMessage("1 month old"),
    "oneWeek": MessageLookupByLibrary.simpleMessage("1 week old"),
    "oneYear": MessageLookupByLibrary.simpleMessage("1 year old"),
    "openCV": MessageLookupByLibrary.simpleMessage("Open CV"),
    "paid": MessageLookupByLibrary.simpleMessage("Paid"),
    "paidSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Paid successfully",
    ),
    "parentName": MessageLookupByLibrary.simpleMessage("Parent Name"),
    "parentPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Parent Phone number",
    ),
    "password": MessageLookupByLibrary.simpleMessage("Password"),
    "passwordConfirmation": MessageLookupByLibrary.simpleMessage(
      "Password Confirmation",
    ),
    "passwordRequired": MessageLookupByLibrary.simpleMessage(
      "Password is required",
    ),
    "passwordTooShort": MessageLookupByLibrary.simpleMessage(
      "Password must be at least 6 characters",
    ),
    "passwordsDoNotMatch": MessageLookupByLibrary.simpleMessage(
      "Passwords do not match",
    ),
    "passwordsShouldMatch": MessageLookupByLibrary.simpleMessage(
      "Passwords should match",
    ),
    "paymentMethod": MessageLookupByLibrary.simpleMessage("Payment Method"),
    "paymentMethods": MessageLookupByLibrary.simpleMessage("Payment Methods"),
    "paymentMethodsUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Payment methods updated successfully",
    ),
    "paymentPendingApproval": MessageLookupByLibrary.simpleMessage(
      "Payment Pending Approval",
    ),
    "paymentScreenshot": MessageLookupByLibrary.simpleMessage(
      "Payment Screenshot",
    ),
    "pdfExportedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "PDF exported successfully",
    ),
    "permissionsUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Permissions updated successfully",
    ),
    "persons": MessageLookupByLibrary.simpleMessage("Persons"),
    "phone": MessageLookupByLibrary.simpleMessage("Phone"),
    "phoneNumber": MessageLookupByLibrary.simpleMessage("Phone Number"),
    "phoneRequired": MessageLookupByLibrary.simpleMessage(
      "Phone number is required",
    ),
    "pickImage": MessageLookupByLibrary.simpleMessage("Pick Image"),
    "pickupPerson": MessageLookupByLibrary.simpleMessage("Pickup Person"),
    "pickups": MessageLookupByLibrary.simpleMessage("Pickups"),
    "plan": MessageLookupByLibrary.simpleMessage("Plan"),
    "plans": MessageLookupByLibrary.simpleMessage("Plans"),
    "playVideo": MessageLookupByLibrary.simpleMessage("Play Video"),
    "pleaseAcceptTerms": MessageLookupByLibrary.simpleMessage(
      "Please accept terms",
    ),
    "pleaseAddAtLeastOneSection": MessageLookupByLibrary.simpleMessage(
      "Please add at least one section",
    ),
    "pleaseAddTheStudentSubscriptionDateFirst":
        MessageLookupByLibrary.simpleMessage(
          "Please add the student subscription date first before making payment.",
        ),
    "pleaseEnterAValidFromToTime": MessageLookupByLibrary.simpleMessage(
      "Please enter a valid from & to time",
    ),
    "pleasePickAnImage": MessageLookupByLibrary.simpleMessage(
      "Please pick an image",
    ),
    "pleaseSelectStudent": MessageLookupByLibrary.simpleMessage(
      "Please select student",
    ),
    "pleaseVerifyPhone": MessageLookupByLibrary.simpleMessage(
      "Please verify phone",
    ),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage("Privacy Policy"),
    "profile": MessageLookupByLibrary.simpleMessage("Profile"),
    "quantity": MessageLookupByLibrary.simpleMessage("Quantity"),
    "question": MessageLookupByLibrary.simpleMessage("Question"),
    "questionType": MessageLookupByLibrary.simpleMessage("Question Type"),
    "questions": MessageLookupByLibrary.simpleMessage("Questions"),
    "questionsCount": MessageLookupByLibrary.simpleMessage("Questions Count"),
    "rating": MessageLookupByLibrary.simpleMessage("Rating"),
    "ratingScale": MessageLookupByLibrary.simpleMessage("Rating Scale"),
    "reminderSentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Reminder sent successfully",
    ),
    "removeImage": MessageLookupByLibrary.simpleMessage("Remove Image"),
    "removeSection": MessageLookupByLibrary.simpleMessage("Remove Section"),
    "reports": MessageLookupByLibrary.simpleMessage("Reports"),
    "resend": MessageLookupByLibrary.simpleMessage("Resend"),
    "resendCode": MessageLookupByLibrary.simpleMessage("Resend Code"),
    "reset": MessageLookupByLibrary.simpleMessage("Reset"),
    "resetPassword": MessageLookupByLibrary.simpleMessage(
      "Reset your password",
    ),
    "responses": MessageLookupByLibrary.simpleMessage("Responses"),
    "results": MessageLookupByLibrary.simpleMessage("Results"),
    "sad": MessageLookupByLibrary.simpleMessage("Sad"),
    "saturday": MessageLookupByLibrary.simpleMessage("Saturday"),
    "save": MessageLookupByLibrary.simpleMessage("Save"),
    "savePaymentMethods": MessageLookupByLibrary.simpleMessage(
      "Save Payment Methods",
    ),
    "savedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Saved successfully",
    ),
    "search": MessageLookupByLibrary.simpleMessage("Search"),
    "searchClasses": MessageLookupByLibrary.simpleMessage("Search Classes"),
    "searchQuestion": MessageLookupByLibrary.simpleMessage("Search Question"),
    "searchStudent": MessageLookupByLibrary.simpleMessage("Search Student"),
    "section": MessageLookupByLibrary.simpleMessage("Section"),
    "sectionDescription": MessageLookupByLibrary.simpleMessage(
      "Section Description",
    ),
    "sectionImage": MessageLookupByLibrary.simpleMessage("Section Image"),
    "sectionTitle": MessageLookupByLibrary.simpleMessage("Section Title"),
    "sections": MessageLookupByLibrary.simpleMessage("Sections"),
    "selectClass": MessageLookupByLibrary.simpleMessage(
      "Please select a class",
    ),
    "selectClasses": MessageLookupByLibrary.simpleMessage("Select Classes"),
    "selectMonth": MessageLookupByLibrary.simpleMessage("Select Month"),
    "selectNursery": MessageLookupByLibrary.simpleMessage("Select Nursery"),
    "selectNurseryToViewStatistics": MessageLookupByLibrary.simpleMessage(
      "Select a nursery to view statistics",
    ),
    "selectPeriod": MessageLookupByLibrary.simpleMessage("Select period"),
    "selectRole": MessageLookupByLibrary.simpleMessage("Select Role"),
    "selectStaffMemberRole": MessageLookupByLibrary.simpleMessage(
      "Select Staff Member Role",
    ),
    "selectTarget": MessageLookupByLibrary.simpleMessage("Select Target"),
    "selectUserType": MessageLookupByLibrary.simpleMessage("Select User Type"),
    "selectedRole": MessageLookupByLibrary.simpleMessage("Selected Role"),
    "send": MessageLookupByLibrary.simpleMessage("Send"),
    "sendANewMessage": MessageLookupByLibrary.simpleMessage(
      "Send a new message",
    ),
    "sendANewMessageTo": m5,
    "sendSupplies": MessageLookupByLibrary.simpleMessage("Send Supplies"),
    "sendSupplyToStudent": MessageLookupByLibrary.simpleMessage(
      "Send Supply To Student",
    ),
    "sendTo": MessageLookupByLibrary.simpleMessage("Send To"),
    "sentVerificationCode": MessageLookupByLibrary.simpleMessage(
      "We sent a verification code to",
    ),
    "september": MessageLookupByLibrary.simpleMessage("September"),
    "sessions": MessageLookupByLibrary.simpleMessage("Sessions"),
    "setupYourClasses": MessageLookupByLibrary.simpleMessage(
      "Setup your classes",
    ),
    "signIn": MessageLookupByLibrary.simpleMessage("Sign In"),
    "signUp": MessageLookupByLibrary.simpleMessage("Sign Up"),
    "singleActivity": MessageLookupByLibrary.simpleMessage("Single Activity"),
    "skip": MessageLookupByLibrary.simpleMessage("Skip"),
    "skipForNow": MessageLookupByLibrary.simpleMessage("Skip for Now"),
    "sleep": MessageLookupByLibrary.simpleMessage("Sleep"),
    "sleepAdded": MessageLookupByLibrary.simpleMessage(
      "Sleep added successfully",
    ),
    "sleepTime": MessageLookupByLibrary.simpleMessage("Sleep Time"),
    "sleepy": MessageLookupByLibrary.simpleMessage("Sleepy"),
    "snack": MessageLookupByLibrary.simpleMessage("Snack"),
    "some": MessageLookupByLibrary.simpleMessage("Some"),
    "speakWithConfidence": MessageLookupByLibrary.simpleMessage(
      "Speak with confidence",
    ),
    "specificClasses": MessageLookupByLibrary.simpleMessage("Specific Classes"),
    "staff": MessageLookupByLibrary.simpleMessage("Staff"),
    "staffMemberAddedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Staff member added successfully",
    ),
    "staffMemberDeletedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Staff member deleted successfully",
    ),
    "staffMemberUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Staff member updated successfully",
    ),
    "staffMembers": MessageLookupByLibrary.simpleMessage("Staff Members"),
    "starRating": MessageLookupByLibrary.simpleMessage("Star Rating (1-5)"),
    "stool": MessageLookupByLibrary.simpleMessage("Stool"),
    "student": MessageLookupByLibrary.simpleMessage("Student"),
    "studentAndClass": MessageLookupByLibrary.simpleMessage("Student & Class"),
    "studentDetails": MessageLookupByLibrary.simpleMessage("Student Details"),
    "studentName": MessageLookupByLibrary.simpleMessage("Student Name"),
    "students": MessageLookupByLibrary.simpleMessage("Students"),
    "submit": MessageLookupByLibrary.simpleMessage("Submit"),
    "subscriptionDate": MessageLookupByLibrary.simpleMessage(
      "Subscription Date",
    ),
    "subscriptionDateNeeded": MessageLookupByLibrary.simpleMessage(
      "Subscription date needed",
    ),
    "subscriptionDateRequired": MessageLookupByLibrary.simpleMessage(
      "Subscription Date Required",
    ),
    "subscriptionExpiredPleaseContactSupport":
        MessageLookupByLibrary.simpleMessage(
          "Subscription expired, please contact support !",
        ),
    "subscriptionRemind": MessageLookupByLibrary.simpleMessage(
      "Subscription Remind",
    ),
    "subscriptionReminderBody": m6,
    "subscriptionReminderTitle": MessageLookupByLibrary.simpleMessage(
      "Subscription Payment Reminder",
    ),
    "subscriptions": MessageLookupByLibrary.simpleMessage("Subscriptions"),
    "sunday": MessageLookupByLibrary.simpleMessage("Sunday"),
    "supervisor": MessageLookupByLibrary.simpleMessage("Supervisor"),
    "supervisorRoleDescription": MessageLookupByLibrary.simpleMessage(
      "Oversees operations and manages staff members",
    ),
    "supplies": MessageLookupByLibrary.simpleMessage("Supplies"),
    "supply": MessageLookupByLibrary.simpleMessage("Supply"),
    "supplyAdded": MessageLookupByLibrary.simpleMessage(
      "Supply added successfully",
    ),
    "supplyName": MessageLookupByLibrary.simpleMessage("supply Name"),
    "supplyType": MessageLookupByLibrary.simpleMessage("Supply Type"),
    "tClass": MessageLookupByLibrary.simpleMessage("Class"),
    "target": MessageLookupByLibrary.simpleMessage("Target"),
    "teacher": MessageLookupByLibrary.simpleMessage("Teacher"),
    "teacherInfo": MessageLookupByLibrary.simpleMessage("Teacher info"),
    "teacherName": MessageLookupByLibrary.simpleMessage("Teacher Name"),
    "teacherRoleDescription": MessageLookupByLibrary.simpleMessage(
      "Manages classes, activities, and student progress",
    ),
    "teacherSignUp": MessageLookupByLibrary.simpleMessage("Teacher Sign up"),
    "teachers": MessageLookupByLibrary.simpleMessage("Teachers"),
    "team": MessageLookupByLibrary.simpleMessage("Team"),
    "text": MessageLookupByLibrary.simpleMessage("Text"),
    "textAnswer": MessageLookupByLibrary.simpleMessage("Text Answer"),
    "textAnswerPlaceholder": MessageLookupByLibrary.simpleMessage(
      "Enter your answer here...",
    ),
    "thisFieldIsRequired": MessageLookupByLibrary.simpleMessage(
      "This field is required",
    ),
    "thursday": MessageLookupByLibrary.simpleMessage("Thursday"),
    "title": MessageLookupByLibrary.simpleMessage("Title"),
    "to": MessageLookupByLibrary.simpleMessage("To"),
    "today": MessageLookupByLibrary.simpleMessage("Today"),
    "toilet": MessageLookupByLibrary.simpleMessage("Toilet"),
    "toiletAdded": MessageLookupByLibrary.simpleMessage(
      "Toilet added successfully",
    ),
    "toiletMethod": MessageLookupByLibrary.simpleMessage("Toilet Method"),
    "toiletType": MessageLookupByLibrary.simpleMessage("Toilet Type"),
    "toiletWay": MessageLookupByLibrary.simpleMessage("Toilet"),
    "total": MessageLookupByLibrary.simpleMessage("Total"),
    "totalVideoSizeExceeds50MB": MessageLookupByLibrary.simpleMessage(
      "Total video size exceeds 50MB limit",
    ),
    "tuesday": MessageLookupByLibrary.simpleMessage("Tuesday"),
    "unAssign": MessageLookupByLibrary.simpleMessage("UnAssign"),
    "unableToCalculateNextPaymentDate": MessageLookupByLibrary.simpleMessage(
      "Unable to calculate next payment date.",
    ),
    "unpaid": MessageLookupByLibrary.simpleMessage("Unpaid"),
    "unwell": MessageLookupByLibrary.simpleMessage("Unwell"),
    "update": MessageLookupByLibrary.simpleMessage("Update"),
    "updateRequired": MessageLookupByLibrary.simpleMessage(
      "An update is required to continue using the app. Please update it now.",
    ),
    "uploadLogo": MessageLookupByLibrary.simpleMessage("Upload logo"),
    "uploading": MessageLookupByLibrary.simpleMessage("Uploading"),
    "urine": MessageLookupByLibrary.simpleMessage("Urine"),
    "userNotFound": MessageLookupByLibrary.simpleMessage("User not Found"),
    "validateYourPhoneFirstPlease": MessageLookupByLibrary.simpleMessage(
      "Validate your phone first please",
    ),
    "verificationCodeIsWrong": MessageLookupByLibrary.simpleMessage(
      "Verification code is wrong",
    ),
    "verificationSuccessful": MessageLookupByLibrary.simpleMessage(
      "Verification successful",
    ),
    "verify": MessageLookupByLibrary.simpleMessage("verify"),
    "videoTooLarge": MessageLookupByLibrary.simpleMessage(
      "Video file too large. Please select a smaller video.",
    ),
    "videoTooLarge50MB": MessageLookupByLibrary.simpleMessage(
      "Video file too large. Max size is 50MB",
    ),
    "view": MessageLookupByLibrary.simpleMessage("View"),
    "viewDetails": MessageLookupByLibrary.simpleMessage("View Details"),
    "warning": MessageLookupByLibrary.simpleMessage("Warning"),
    "wednesday": MessageLookupByLibrary.simpleMessage("Wednesday"),
    "weekly": MessageLookupByLibrary.simpleMessage("Weekly"),
    "weeklyActivity": MessageLookupByLibrary.simpleMessage("Weekly Activity"),
    "weeksOld": m7,
    "weeksShort": m8,
    "worried": MessageLookupByLibrary.simpleMessage("Worried"),
    "yearsMonthsShort": m9,
    "yearsOld": m10,
    "yearsShort": m11,
    "youAreParentPleaseLoginOnParentApplication":
        MessageLookupByLibrary.simpleMessage(
          "You are parent, please login on parent application !",
        ),
    "youAreParentPleaseRegisterOnParentApplication":
        MessageLookupByLibrary.simpleMessage(
          "You are parent, please register on parent application !",
        ),
    "youCannotDeleteThisQuestionBecauseitsHasStudentResults":
        MessageLookupByLibrary.simpleMessage(
          "You cannot delete this question because it\'s has student results",
        ),
  };
}
