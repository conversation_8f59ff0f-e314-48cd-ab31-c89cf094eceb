// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  static String m0(name) => "هل أنت متأكد من حذف ${name}؟";

  static String m1(date) =>
      "هل أنت متأكد من جعل هذا الاشتراك مدفوعاً لتاريخ ${date}؟";

  static String m2(days) => "${days} أيام";

  static String m3(months) => "${months} أشهر";

  static String m4(months) => "${months}ش";

  static String m5(name) => "إرسال رسالة جديدة إلى ${name}";

  static String m6(studentName) =>
      "اشتراك ${studentName} مستحق اليوم. يرجى تحصيل الدفعة.";

  static String m7(weeks) => "${weeks} أسابيع";

  static String m8(weeks) => "${weeks}أ";

  static String m9(years, months) => "${years}س ${months}ش";

  static String m10(years) => "${years} سنوات";

  static String m11(years) => "${years}س";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "IfYouContinueThisWillAffectParentsAppAndTheyCanSeeAllChildrenUnderTheirNumber":
        MessageLookupByLibrary.simpleMessage(
          "إذا واصلت، سيؤثر هذا على تطبيق الوالدين ويمكنهم رؤية جميع الأطفال تحت رقمهم",
        ),
    "Of": MessageLookupByLibrary.simpleMessage("من"),
    "SignupAsa": MessageLookupByLibrary.simpleMessage("التسجيل كـ"),
    "SkipForNow": MessageLookupByLibrary.simpleMessage("تخطي الآن"),
    "absent": MessageLookupByLibrary.simpleMessage("غائب"),
    "accessPermissions": MessageLookupByLibrary.simpleMessage("صلاحيات الوصول"),
    "accountant": MessageLookupByLibrary.simpleMessage("محاسب"),
    "accountantRoleDescription": MessageLookupByLibrary.simpleMessage(
      "يتعامل مع السجلات المالية ومدفوعات الطلاب",
    ),
    "active": MessageLookupByLibrary.simpleMessage("نشط"),
    "activeStudents": MessageLookupByLibrary.simpleMessage("الطلاب النشطين"),
    "activities": MessageLookupByLibrary.simpleMessage("الأنشطة"),
    "activitiesCompleted": MessageLookupByLibrary.simpleMessage(
      "الأنشطة المكتملة",
    ),
    "activity": MessageLookupByLibrary.simpleMessage("النشاط"),
    "activityChart": MessageLookupByLibrary.simpleMessage("مخطط النشاط"),
    "activityDescription": MessageLookupByLibrary.simpleMessage("وصف النشاط"),
    "activityLevel": MessageLookupByLibrary.simpleMessage("مستوى النشاط"),
    "activityName": MessageLookupByLibrary.simpleMessage("اسم النشاط"),
    "add": MessageLookupByLibrary.simpleMessage("إضافة"),
    "addANewStaffMember": MessageLookupByLibrary.simpleMessage(
      "إضافة عضو فريق جديد",
    ),
    "addActivity": MessageLookupByLibrary.simpleMessage("إضافة نشاط"),
    "addActivityLevel": MessageLookupByLibrary.simpleMessage(
      "إضافة مستوى النشاط",
    ),
    "addAnnouncement": MessageLookupByLibrary.simpleMessage("إضافة إعلان"),
    "addAnnouncements": MessageLookupByLibrary.simpleMessage("إضافة إعلانات"),
    "addAttendance": MessageLookupByLibrary.simpleMessage("إضافة حضور"),
    "addEvaluation": MessageLookupByLibrary.simpleMessage("إضافة تقييم"),
    "addEvents": MessageLookupByLibrary.simpleMessage("إضافة أحداث"),
    "addExams": MessageLookupByLibrary.simpleMessage("إضافة امتحانات"),
    "addFood": MessageLookupByLibrary.simpleMessage("إضافة طعام"),
    "addImage": MessageLookupByLibrary.simpleMessage("إضافة صورة"),
    "addMedia": MessageLookupByLibrary.simpleMessage("إضافة صور"),
    "addMood": MessageLookupByLibrary.simpleMessage("إضافة مزاج"),
    "addNewBill": MessageLookupByLibrary.simpleMessage("إضافة فاتورة جديدة"),
    "addNewClass": MessageLookupByLibrary.simpleMessage("إضافة فصل جديد"),
    "addNewEvent": MessageLookupByLibrary.simpleMessage("إضافة حدث جديد"),
    "addNewInvoice": MessageLookupByLibrary.simpleMessage("إضافة ايراد جديدة"),
    "addNewQuestion": MessageLookupByLibrary.simpleMessage("إضافة سؤال جديد"),
    "addNewSection": MessageLookupByLibrary.simpleMessage("إضافة قسم جديد"),
    "addNewStudents": MessageLookupByLibrary.simpleMessage("إضافة طلاب جدد"),
    "addNote": MessageLookupByLibrary.simpleMessage("إضافة ملاحظة"),
    "addNurseryActivities": MessageLookupByLibrary.simpleMessage(
      "إضافة نشاط جديد",
    ),
    "addNurseryTeam": MessageLookupByLibrary.simpleMessage(
      "إضافة فريق الحضانة",
    ),
    "addNurseryTeamMember": MessageLookupByLibrary.simpleMessage(
      "إضافة عضو فريق جديد",
    ),
    "addParentsPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "إضافة رقم هاتف الوالدين",
    ),
    "addPickupPerson": MessageLookupByLibrary.simpleMessage(
      "إضافة شخص الاستلام",
    ),
    "addPickups": MessageLookupByLibrary.simpleMessage("إضافة استلام"),
    "addPlan": MessageLookupByLibrary.simpleMessage("إضافة خطة"),
    "addPlans": MessageLookupByLibrary.simpleMessage("إضافة خطط"),
    "addQuestion": MessageLookupByLibrary.simpleMessage("إضافة سؤال"),
    "addResults": MessageLookupByLibrary.simpleMessage("إضافة نتائج"),
    "addSleep": MessageLookupByLibrary.simpleMessage("إضافة نوم"),
    "addStaffMember": MessageLookupByLibrary.simpleMessage("إضافة عضو فريق"),
    "addStudents": MessageLookupByLibrary.simpleMessage("إضافة طلاب"),
    "addSupply": MessageLookupByLibrary.simpleMessage("إضافة مستلزم"),
    "addToilet": MessageLookupByLibrary.simpleMessage("إضافة حمام"),
    "addVideo": MessageLookupByLibrary.simpleMessage("إضافة فيديو"),
    "addedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تمت الإضافة بنجاح",
    ),
    "address": MessageLookupByLibrary.simpleMessage("العنوان"),
    "adminAlreadyRegistered": MessageLookupByLibrary.simpleMessage(
      "المسؤول مسجل بالفعل",
    ),
    "adminSignUp": MessageLookupByLibrary.simpleMessage("تسجيل المسؤول"),
    "administrator": MessageLookupByLibrary.simpleMessage("مسؤول"),
    "all": MessageLookupByLibrary.simpleMessage("الكل"),
    "allMonths": MessageLookupByLibrary.simpleMessage("جميع الشهور"),
    "allParents": MessageLookupByLibrary.simpleMessage("جميع الأهالي"),
    "allStudents": MessageLookupByLibrary.simpleMessage("جميع الطلاب"),
    "alreadyHaveAnAccount": MessageLookupByLibrary.simpleMessage(
      "هل لديك حساب بالفعل؟",
    ),
    "amount": MessageLookupByLibrary.simpleMessage("الكمية"),
    "angry": MessageLookupByLibrary.simpleMessage("غاضب"),
    "announcements": MessageLookupByLibrary.simpleMessage("الإعلانات"),
    "answers": MessageLookupByLibrary.simpleMessage("الإجابات"),
    "april": MessageLookupByLibrary.simpleMessage("أبريل"),
    "arabic": MessageLookupByLibrary.simpleMessage("العربية"),
    "areYouSureDeleteStaffMember": m0,
    "areYouSureToDeleteThisActivity": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذا النشاط؟",
    ),
    "areYouSureToDeleteThisBill": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذه الفاتورة؟",
    ),
    "areYouSureToDeleteThisClass": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذا الفصل؟",
    ),
    "areYouSureToDeleteThisEvent": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذا الحدث؟",
    ),
    "areYouSureToDeleteThisInvoice": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذا ايراد؟",
    ),
    "areYouSureToDeleteThisQuestion": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذا السؤال؟",
    ),
    "areYouSureToDeleteThisStudent": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذا الطالب؟",
    ),
    "areYouSureToDeleteThisSupply": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذه المستلزمات؟",
    ),
    "areYouSureToDeleteThisTeacher": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذا المدرس؟",
    ),
    "areYouSureToDeleteYourAccount": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذا الحساب؟",
    ),
    "areYouSureToMakeThisSubscriptionPaid":
        MessageLookupByLibrary.simpleMessage(
          "هل أنت متأكد من عمل هذا الاشتراك مدفوع؟",
        ),
    "areYouSureToMakeThisSubscriptionPaidFor": m1,
    "areYouSureToSendSubscriptionRemind": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من إرسال تذكير الاشتراك؟",
    ),
    "areYouSureYouWantToDeleteThisAnnouncement":
        MessageLookupByLibrary.simpleMessage(
          "هل أنت متأكد من حذف هذا الإعلان؟",
        ),
    "areYouSureYouWantToDeleteThisPlan": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذه الخطة؟",
    ),
    "assign": MessageLookupByLibrary.simpleMessage("تعيين"),
    "assignActivity": MessageLookupByLibrary.simpleMessage("تعيين نشاط"),
    "assignActivityToClass": MessageLookupByLibrary.simpleMessage(
      "تعيين النشاط للفصل",
    ),
    "assignSupplyToStudent": MessageLookupByLibrary.simpleMessage(
      "تعيين مستلزم للطالب",
    ),
    "assignToClass": MessageLookupByLibrary.simpleMessage("تعيين إلى الفصل"),
    "assigned": MessageLookupByLibrary.simpleMessage("تم التعيين"),
    "attendance": MessageLookupByLibrary.simpleMessage("الحضور"),
    "attendanceChart": MessageLookupByLibrary.simpleMessage("مخطط الحضور"),
    "attendanceTracking": MessageLookupByLibrary.simpleMessage("تتبع الحضور"),
    "attended": MessageLookupByLibrary.simpleMessage("حضر"),
    "attendeesOfToday": MessageLookupByLibrary.simpleMessage("الحضور اليوم"),
    "august": MessageLookupByLibrary.simpleMessage("أغسطس"),
    "back": MessageLookupByLibrary.simpleMessage("رجوع"),
    "billAmount": MessageLookupByLibrary.simpleMessage("مبلغ الفاتورة"),
    "billName": MessageLookupByLibrary.simpleMessage("اسم الفاتورة"),
    "bills": MessageLookupByLibrary.simpleMessage("الفواتير"),
    "billsChart": MessageLookupByLibrary.simpleMessage("مخطط الفواتير"),
    "birthDate": MessageLookupByLibrary.simpleMessage("تاريخ الميلاد"),
    "breakfast": MessageLookupByLibrary.simpleMessage("الإفطار"),
    "by": MessageLookupByLibrary.simpleMessage("بواسطة"),
    "calm": MessageLookupByLibrary.simpleMessage("هادئ"),
    "cancel": MessageLookupByLibrary.simpleMessage("إلغاء"),
    "cannotAddStudentWithThisPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "لا يمكن إضافة طالب بهذا الرقم",
    ),
    "changeLanguage": MessageLookupByLibrary.simpleMessage("تغيير اللغة"),
    "changePassword": MessageLookupByLibrary.simpleMessage("تغيير كلمة المرور"),
    "checkOurLatestJobApplicationsNow": MessageLookupByLibrary.simpleMessage(
      "تحقق من أحدث طلبات التوظيف لدينا الآن!",
    ),
    "chooseActivityAssignType": MessageLookupByLibrary.simpleMessage(
      "اختر نوع تعيين النشاط",
    ),
    "city": MessageLookupByLibrary.simpleMessage("المحافظة"),
    "classActivities": MessageLookupByLibrary.simpleMessage("أنشطة الفصل"),
    "classDescription": MessageLookupByLibrary.simpleMessage("وصف الفصل"),
    "className": MessageLookupByLibrary.simpleMessage("اسم الفصل"),
    "classes": MessageLookupByLibrary.simpleMessage("الفصول"),
    "clear": MessageLookupByLibrary.simpleMessage("مسح"),
    "clickToContact": MessageLookupByLibrary.simpleMessage("انقر للاتصال"),
    "clothes": MessageLookupByLibrary.simpleMessage("ملابس"),
    "complete": MessageLookupByLibrary.simpleMessage("مكتمل"),
    "completeVerification": MessageLookupByLibrary.simpleMessage(
      "إكمال التحقق",
    ),
    "compressing": MessageLookupByLibrary.simpleMessage("ضغط"),
    "compressingVideo": MessageLookupByLibrary.simpleMessage("ضغط الفيديو..."),
    "confirm": MessageLookupByLibrary.simpleMessage("تأكيد"),
    "confirmDelete": MessageLookupByLibrary.simpleMessage("تأكيد الحذف"),
    "confirmNewPassword": MessageLookupByLibrary.simpleMessage(
      "تأكيد كلمة المرور الجديدة",
    ),
    "confirmation": MessageLookupByLibrary.simpleMessage("التأكيد"),
    "congratulations": MessageLookupByLibrary.simpleMessage("تهانينا"),
    "contactSupport": MessageLookupByLibrary.simpleMessage("الاتصال بالدعم"),
    "createANewClass": MessageLookupByLibrary.simpleMessage("إنشاء فصل جديد"),
    "createANewSupply": MessageLookupByLibrary.simpleMessage(
      "إضافة مستلزمات جديدة",
    ),
    "createNewClass": MessageLookupByLibrary.simpleMessage("إنشاء فصل جديد"),
    "currentActivity": MessageLookupByLibrary.simpleMessage("النشاط الحالي"),
    "currentMonth": MessageLookupByLibrary.simpleMessage("الشهر الحالي"),
    "cv": MessageLookupByLibrary.simpleMessage("السيرة الذاتية"),
    "dailyActivities": MessageLookupByLibrary.simpleMessage("الأنشطة اليومية"),
    "dailySchedule": MessageLookupByLibrary.simpleMessage("الجدول اليومي"),
    "dashboard": MessageLookupByLibrary.simpleMessage("لوحة التحكم"),
    "date": MessageLookupByLibrary.simpleMessage("التاريخ"),
    "day": MessageLookupByLibrary.simpleMessage("اليوم"),
    "daysOld": m2,
    "december": MessageLookupByLibrary.simpleMessage("ديسمبر"),
    "delete": MessageLookupByLibrary.simpleMessage("حذف"),
    "deleteAccount": MessageLookupByLibrary.simpleMessage("حذف الحساب"),
    "deleteAnnouncement": MessageLookupByLibrary.simpleMessage("حذف الإعلان"),
    "deletePlan": MessageLookupByLibrary.simpleMessage("حذف الخطة"),
    "deletedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم الحذف بنجاح",
    ),
    "description": MessageLookupByLibrary.simpleMessage("الوصف"),
    "diaper": MessageLookupByLibrary.simpleMessage("حفاضة"),
    "didNotGetCode": MessageLookupByLibrary.simpleMessage(
      "ألم تحصل على الرمز؟",
    ),
    "dontHaveAnAccount": MessageLookupByLibrary.simpleMessage("ليس لديك حساب؟"),
    "due": MessageLookupByLibrary.simpleMessage("مستحق"),
    "edit": MessageLookupByLibrary.simpleMessage("تحرير"),
    "editActivity": MessageLookupByLibrary.simpleMessage("تعديل النشاط"),
    "editAnnouncement": MessageLookupByLibrary.simpleMessage("تعديل الإعلان"),
    "editClass": MessageLookupByLibrary.simpleMessage("تحرير الفصل"),
    "editEvent": MessageLookupByLibrary.simpleMessage("تحرير الحدث"),
    "editFood": MessageLookupByLibrary.simpleMessage("تعديل الطعام"),
    "editMood": MessageLookupByLibrary.simpleMessage("تعديل المزاج"),
    "editPermissions": MessageLookupByLibrary.simpleMessage("تعديل الصلاحيات"),
    "editPermissionsFor": MessageLookupByLibrary.simpleMessage(
      "تعديل الصلاحيات لـ",
    ),
    "editPlan": MessageLookupByLibrary.simpleMessage("تعديل الخطة"),
    "editSleep": MessageLookupByLibrary.simpleMessage("تعديل النوم"),
    "editStaffMember": MessageLookupByLibrary.simpleMessage("تعديل عضو فريق"),
    "editSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم التحرير بنجاح",
    ),
    "editSupply": MessageLookupByLibrary.simpleMessage("تعديل المستلزم"),
    "editTeacher": MessageLookupByLibrary.simpleMessage("تحرير المدرس"),
    "editToilet": MessageLookupByLibrary.simpleMessage("تعديل الحمام"),
    "editedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم التعديل بنجاح",
    ),
    "education": MessageLookupByLibrary.simpleMessage("التعليم"),
    "email": MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
    "emailRequired": MessageLookupByLibrary.simpleMessage(
      "البريد الإلكتروني مطلوب",
    ),
    "emergency": MessageLookupByLibrary.simpleMessage("الطوارئ"),
    "english": MessageLookupByLibrary.simpleMessage("الإنجليزية"),
    "enter": MessageLookupByLibrary.simpleMessage("أدخل"),
    "enterEmail": MessageLookupByLibrary.simpleMessage(
      "أدخل البريد الإلكتروني",
    ),
    "enterEtisalatCashNumber": MessageLookupByLibrary.simpleMessage(
      "أدخل رقم Etisalat Cash",
    ),
    "enterInstapayNumberOrLink": MessageLookupByLibrary.simpleMessage(
      "أدخل رقم أو رابط InstaPay",
    ),
    "enterJobTitle": MessageLookupByLibrary.simpleMessage(
      "أدخل المسمى الوظيفي",
    ),
    "enterName": MessageLookupByLibrary.simpleMessage("أدخل الاسم"),
    "enterNewPassword": MessageLookupByLibrary.simpleMessage(
      "أدخل كلمة مرور جديدة",
    ),
    "enterNote": MessageLookupByLibrary.simpleMessage("أدخل ملاحظة"),
    "enterOrangeCashNumber": MessageLookupByLibrary.simpleMessage(
      "أدخل رقم Orange Cash",
    ),
    "enterOtp": MessageLookupByLibrary.simpleMessage("أدخل رمز OTP"),
    "enterPassword": MessageLookupByLibrary.simpleMessage("أدخل كلمة المرور"),
    "enterPhoneNumber": MessageLookupByLibrary.simpleMessage("أدخل رقم الهاتف"),
    "enterPhoneNumberFirst": MessageLookupByLibrary.simpleMessage(
      "أدخل رقم هاتفك أولاً",
    ),
    "enterPickupPerson": MessageLookupByLibrary.simpleMessage(
      "أدخل شخص الاستلام",
    ),
    "enterSectionDescription": MessageLookupByLibrary.simpleMessage(
      "أدخل وصف القسم",
    ),
    "enterSectionTitle": MessageLookupByLibrary.simpleMessage(
      "أدخل عنوان القسم",
    ),
    "enterValidNurseryName": MessageLookupByLibrary.simpleMessage(
      "أدخل اسم حضانة صحيح",
    ),
    "enterValidPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "أدخل رقم هاتف صالح",
    ),
    "enterVodafoneCashNumber": MessageLookupByLibrary.simpleMessage(
      "أدخل رقم Vodafone Cash",
    ),
    "enterWeCashNumber": MessageLookupByLibrary.simpleMessage(
      "أدخل رقم WE Cash",
    ),
    "errorLoadingAnswers": MessageLookupByLibrary.simpleMessage(
      "خطأ في تحميل الإجابات",
    ),
    "errorLoadingEvaluations": MessageLookupByLibrary.simpleMessage(
      "خطأ في تحميل التقييمات",
    ),
    "errorOccurred": MessageLookupByLibrary.simpleMessage("حدث خطأ"),
    "evaluation": MessageLookupByLibrary.simpleMessage("التقييم"),
    "evaluationDate": MessageLookupByLibrary.simpleMessage("تاريخ التقييم"),
    "evaluationQuestions": MessageLookupByLibrary.simpleMessage(
      "أسئلة التقييم",
    ),
    "evaluationReport": MessageLookupByLibrary.simpleMessage("تقرير التقييم"),
    "evaluationSummary": MessageLookupByLibrary.simpleMessage("ملخص التقييم"),
    "evaluations": MessageLookupByLibrary.simpleMessage("التقييمات"),
    "eventName": MessageLookupByLibrary.simpleMessage("اسم الحدث"),
    "eventThisMonth": MessageLookupByLibrary.simpleMessage("حدث هذا الشهر"),
    "eventType": MessageLookupByLibrary.simpleMessage("نوع الحدث"),
    "events": MessageLookupByLibrary.simpleMessage("الأحداث"),
    "exams": MessageLookupByLibrary.simpleMessage("الامتحانات"),
    "excited": MessageLookupByLibrary.simpleMessage("متحمس"),
    "expectedSalary": MessageLookupByLibrary.simpleMessage("الراتب المتوقع"),
    "exportReport": MessageLookupByLibrary.simpleMessage("تصدير التقرير"),
    "failedToExportPdf": MessageLookupByLibrary.simpleMessage(
      "فشل في تصدير PDF",
    ),
    "failedToUpdatePaymentMethods": MessageLookupByLibrary.simpleMessage(
      "فشل في تحديث طرق الدفع",
    ),
    "father": MessageLookupByLibrary.simpleMessage("الأب"),
    "february": MessageLookupByLibrary.simpleMessage("فبراير"),
    "fees": MessageLookupByLibrary.simpleMessage("الرسوم"),
    "filterByClass": MessageLookupByLibrary.simpleMessage("تصفية حسب الفصل"),
    "filterByDate": MessageLookupByLibrary.simpleMessage("تصفية حسب التاريخ"),
    "financial": MessageLookupByLibrary.simpleMessage("المالية"),
    "finishLetsStart": MessageLookupByLibrary.simpleMessage("إنهاء، لنبدأ"),
    "first100": MessageLookupByLibrary.simpleMessage("أول 100"),
    "food": MessageLookupByLibrary.simpleMessage("الطعام"),
    "foodAdded": MessageLookupByLibrary.simpleMessage("تم إضافة الطعام بنجاح"),
    "forgetPassword": MessageLookupByLibrary.simpleMessage("نسيت كلمة المرور"),
    "friday": MessageLookupByLibrary.simpleMessage("الجمعة"),
    "from": MessageLookupByLibrary.simpleMessage("من"),
    "fromTimeShouldBeBeforeToTime": MessageLookupByLibrary.simpleMessage(
      "يجب أن يكون وقت البداية قبل وقت النهاية",
    ),
    "fromTimeShouldNotBeEqualToToTime": MessageLookupByLibrary.simpleMessage(
      "لا يمكن أن يكون وقت البدء مساويا لوقت الانتهاء",
    ),
    "gender": MessageLookupByLibrary.simpleMessage("النوع"),
    "getTalkingFrom": MessageLookupByLibrary.simpleMessage(
      "كل يوم هو رحلة جديده\n سجل دخول للانضمام إلينا.",
    ),
    "goodAfternoon": MessageLookupByLibrary.simpleMessage("مساء الخير!"),
    "goodMorning": MessageLookupByLibrary.simpleMessage("صباح الخير!"),
    "happy": MessageLookupByLibrary.simpleMessage("سعيد"),
    "haveAnyQuestionsContactUs": MessageLookupByLibrary.simpleMessage(
      "هل لديك أي أسئلة؟\nاتصل بنا",
    ),
    "high": MessageLookupByLibrary.simpleMessage("عالي"),
    "history": MessageLookupByLibrary.simpleMessage("السجل"),
    "home": MessageLookupByLibrary.simpleMessage("الرئيسية"),
    "homeAddress": MessageLookupByLibrary.simpleMessage("العنوان"),
    "iHaveReadThe": MessageLookupByLibrary.simpleMessage("لقد قرأت"),
    "inClothes": MessageLookupByLibrary.simpleMessage("الملابس"),
    "inTheDiaper": MessageLookupByLibrary.simpleMessage("الحفاض"),
    "inTheToilet": MessageLookupByLibrary.simpleMessage("الحمام"),
    "inactiveStudents": MessageLookupByLibrary.simpleMessage(
      "الطلاب غير النشطين",
    ),
    "incomeChart": MessageLookupByLibrary.simpleMessage("مخطط الدخل"),
    "invalidEmail": MessageLookupByLibrary.simpleMessage(
      "تنسيق البريد الإلكتروني غير صحيح",
    ),
    "invoiceAmount": MessageLookupByLibrary.simpleMessage("مبلغ الايراد"),
    "invoiceName": MessageLookupByLibrary.simpleMessage("اسم الايراد"),
    "invoices": MessageLookupByLibrary.simpleMessage("الايرادات"),
    "invoicesChart": MessageLookupByLibrary.simpleMessage("مخطط الايرادات"),
    "january": MessageLookupByLibrary.simpleMessage("يناير"),
    "jobApplication": MessageLookupByLibrary.simpleMessage("طلب التوظيف"),
    "jobApplicationDetails": MessageLookupByLibrary.simpleMessage(
      "تفاصيل طلب التوظيف",
    ),
    "jobApplications": MessageLookupByLibrary.simpleMessage("طلبات التوظيف"),
    "jobTitle": MessageLookupByLibrary.simpleMessage("الوظيفة"),
    "jobs": MessageLookupByLibrary.simpleMessage("الوظائف"),
    "joinedIn": MessageLookupByLibrary.simpleMessage("انضم في"),
    "july": MessageLookupByLibrary.simpleMessage("يوليو"),
    "june": MessageLookupByLibrary.simpleMessage("يونيو"),
    "largeDatasetDetected": MessageLookupByLibrary.simpleMessage(
      "تم اكتشاف مجموعة بيانات كبيرة. تصدير أول 100 استجابة للحصول على الأداء الأمثل.",
    ),
    "letsDoAGreatJob": MessageLookupByLibrary.simpleMessage(
      "دعونا نقوم بعمل رائع",
    ),
    "letsStart": MessageLookupByLibrary.simpleMessage("لنبدأ"),
    "logout": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
    "low": MessageLookupByLibrary.simpleMessage("منخفض"),
    "lunch": MessageLookupByLibrary.simpleMessage("الغداء"),
    "march": MessageLookupByLibrary.simpleMessage("مارس"),
    "matherPhoneNumber": MessageLookupByLibrary.simpleMessage("رقم هاتف الأم"),
    "maxStudents": MessageLookupByLibrary.simpleMessage("الحد الأقصى للطلاب"),
    "maxStudentsReachedPleaseContactSupport":
        MessageLookupByLibrary.simpleMessage(
          "تم الوصول إلى الحد الأقصى للطلاب، يرجى الاتصال بالدعم!",
        ),
    "maxUploadFileSizeIsOnly5MB": MessageLookupByLibrary.simpleMessage(
      "الحد الأقصى لحجم صورة التحميل هو 5 ميجابايت فقط",
    ),
    "maxUploadFilesIsOnly13": MessageLookupByLibrary.simpleMessage(
      "الحد الأقصى للملفات المرفوعة هو 13 (10 صور + 3 فيديوهات)",
    ),
    "maxUploadFilesIsOnly4": MessageLookupByLibrary.simpleMessage(
      "الحد الأقصى لصور التحميل هو 4 فقط",
    ),
    "maxUploadVideosIsOnly3": MessageLookupByLibrary.simpleMessage(
      "الحد الأقصى للفيديوهات المرفوعة هو 3 فقط",
    ),
    "may": MessageLookupByLibrary.simpleMessage("مايو"),
    "mealAmount": MessageLookupByLibrary.simpleMessage("كمية الوجبة"),
    "mealType": MessageLookupByLibrary.simpleMessage("نوع الوجبة"),
    "meals": MessageLookupByLibrary.simpleMessage("الوجبات"),
    "media": MessageLookupByLibrary.simpleMessage("الصور"),
    "medium": MessageLookupByLibrary.simpleMessage("متوسط"),
    "members": MessageLookupByLibrary.simpleMessage("الأعضاء"),
    "message": MessageLookupByLibrary.simpleMessage("الرسالة"),
    "messageSentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم إرسال الرسالة بنجاح",
    ),
    "messages": MessageLookupByLibrary.simpleMessage("الرسائل"),
    "monday": MessageLookupByLibrary.simpleMessage("الاثنين"),
    "month": MessageLookupByLibrary.simpleMessage("الشهر"),
    "monthlyTotal": MessageLookupByLibrary.simpleMessage("الإجمالي الشهري"),
    "monthsOld": m3,
    "monthsShort": m4,
    "mood": MessageLookupByLibrary.simpleMessage("المزاج"),
    "moodAdded": MessageLookupByLibrary.simpleMessage("تم إضافة المزاج بنجاح"),
    "moodType": MessageLookupByLibrary.simpleMessage("نوع المزاج"),
    "more": MessageLookupByLibrary.simpleMessage("أكثر"),
    "mother": MessageLookupByLibrary.simpleMessage("الأم"),
    "motherOrParentAlreadyRegistered": MessageLookupByLibrary.simpleMessage(
      "الأم أو الوالد مسجل بالفعل",
    ),
    "motherPhoneNumber": MessageLookupByLibrary.simpleMessage("رقم هاتف الأم"),
    "myClass": MessageLookupByLibrary.simpleMessage("فصلي"),
    "myClasses": MessageLookupByLibrary.simpleMessage("فصولي"),
    "name": MessageLookupByLibrary.simpleMessage("الاسم"),
    "nameRequired": MessageLookupByLibrary.simpleMessage("الاسم مطلوب"),
    "newborn": MessageLookupByLibrary.simpleMessage("مولود جديد"),
    "newbornShort": MessageLookupByLibrary.simpleMessage("مولود"),
    "next": MessageLookupByLibrary.simpleMessage("التالي"),
    "noActivities": MessageLookupByLibrary.simpleMessage("لا توجد أنشطة"),
    "noActivitiesForThisDate": MessageLookupByLibrary.simpleMessage(
      "لا توجد أنشطة لهذا التاريخ",
    ),
    "noActivitiesFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على أنشطة",
    ),
    "noBills": MessageLookupByLibrary.simpleMessage("لا توجد فواتير"),
    "noClasses": MessageLookupByLibrary.simpleMessage("لا توجد فصول"),
    "noData": MessageLookupByLibrary.simpleMessage("لا توجد بيانات"),
    "noDataToExport": MessageLookupByLibrary.simpleMessage(
      "لا توجد بيانات للتصدير",
    ),
    "noDate": MessageLookupByLibrary.simpleMessage("لا يوجد تاريخ"),
    "noEvaluationAnswers": MessageLookupByLibrary.simpleMessage(
      "لا توجد إجابات تقييم",
    ),
    "noEvaluations": MessageLookupByLibrary.simpleMessage("لا توجد تقييمات"),
    "noEvents": MessageLookupByLibrary.simpleMessage("لا توجد أحداث"),
    "noHistoryForThisDate": MessageLookupByLibrary.simpleMessage(
      "لا توجد سجلات لهذا التاريخ",
    ),
    "noInvoices": MessageLookupByLibrary.simpleMessage("لا توجد ايرادات"),
    "noJobApplications": MessageLookupByLibrary.simpleMessage(
      "لا توجد طلبات توظيف",
    ),
    "noMedia": MessageLookupByLibrary.simpleMessage("لا توجد صور"),
    "noNotifications": MessageLookupByLibrary.simpleMessage("لا توجد إشعارات"),
    "noPersons": MessageLookupByLibrary.simpleMessage("لا يوجد أشخاص"),
    "noPlansForThisMonth": MessageLookupByLibrary.simpleMessage(
      "لا توجد خطط لهذا الشهر",
    ),
    "noQuestions": MessageLookupByLibrary.simpleMessage("لا توجد أسئلة"),
    "noResultsFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على نتائج",
    ),
    "noStaffMembers": MessageLookupByLibrary.simpleMessage(
      "لا يوجد أعضاء فريق",
    ),
    "noStudents": MessageLookupByLibrary.simpleMessage("لا يوجد طلاب"),
    "noSubscriptionDateSet": MessageLookupByLibrary.simpleMessage(
      "لم يتم تحديد تاريخ الاشتراك",
    ),
    "noSupplies": MessageLookupByLibrary.simpleMessage("لا توجد مستلزمات"),
    "noTeachers": MessageLookupByLibrary.simpleMessage("لا يوجد مدرسون"),
    "noVideos": MessageLookupByLibrary.simpleMessage("لا توجد فيديوهات"),
    "none": MessageLookupByLibrary.simpleMessage("لا شيء"),
    "notAvailableForAccountant": MessageLookupByLibrary.simpleMessage(
      "غير متوفر للمحاسب",
    ),
    "note": MessageLookupByLibrary.simpleMessage("الملاحظة"),
    "notifications": MessageLookupByLibrary.simpleMessage("الإشعارات"),
    "november": MessageLookupByLibrary.simpleMessage("نوفمبر"),
    "numberOfResponses": MessageLookupByLibrary.simpleMessage("عدد الاستجابات"),
    "numberOfStudents": MessageLookupByLibrary.simpleMessage("عدد الطلاب"),
    "nurseryActivities": MessageLookupByLibrary.simpleMessage(
      "إضافة أنشطة الحضانة",
    ),
    "nurseryDataNotFound": MessageLookupByLibrary.simpleMessage(
      "بيانات الحضانة غير موجودة",
    ),
    "nurseryInformation": MessageLookupByLibrary.simpleMessage(
      "معلومات الحضانة",
    ),
    "nurseryLogo": MessageLookupByLibrary.simpleMessage("شعار الحضانة"),
    "nurseryName": MessageLookupByLibrary.simpleMessage("اسم الحضانة"),
    "october": MessageLookupByLibrary.simpleMessage("أكتوبر"),
    "oneDay": MessageLookupByLibrary.simpleMessage("يوم واحد"),
    "oneMonth": MessageLookupByLibrary.simpleMessage("شهر واحد"),
    "oneWeek": MessageLookupByLibrary.simpleMessage("أسبوع واحد"),
    "oneYear": MessageLookupByLibrary.simpleMessage("سنة واحدة"),
    "openCV": MessageLookupByLibrary.simpleMessage("فتح السيرة الذاتية"),
    "paid": MessageLookupByLibrary.simpleMessage("مدفوع"),
    "paidSuccessfully": MessageLookupByLibrary.simpleMessage("تم الدفع بنجاح"),
    "parentName": MessageLookupByLibrary.simpleMessage("اسم الوالد"),
    "parentPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "رقم هاتف الوالدين",
    ),
    "password": MessageLookupByLibrary.simpleMessage("كلمة المرور"),
    "passwordConfirmation": MessageLookupByLibrary.simpleMessage(
      "تأكيد كلمة المرور",
    ),
    "passwordRequired": MessageLookupByLibrary.simpleMessage(
      "كلمة المرور مطلوبة",
    ),
    "passwordTooShort": MessageLookupByLibrary.simpleMessage(
      "كلمة المرور يجب أن تكون 6 أحرف على الأقل",
    ),
    "passwordsDoNotMatch": MessageLookupByLibrary.simpleMessage(
      "كلمات المرور غير متطابقة",
    ),
    "passwordsShouldMatch": MessageLookupByLibrary.simpleMessage(
      "يجب أن تتطابق كلمات المرور",
    ),
    "paymentMethod": MessageLookupByLibrary.simpleMessage("طريقة الدفع"),
    "paymentMethods": MessageLookupByLibrary.simpleMessage("طرق الدفع"),
    "paymentMethodsUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم تحديث طرق الدفع بنجاح",
    ),
    "paymentPendingApproval": MessageLookupByLibrary.simpleMessage(
      "الدفع في انتظار الموافقة",
    ),
    "paymentScreenshot": MessageLookupByLibrary.simpleMessage(
      "لقطة شاشة الدفع",
    ),
    "pdfExportedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم تصدير PDF بنجاح",
    ),
    "permissionsUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم تحديث الصلاحيات بنجاح",
    ),
    "persons": MessageLookupByLibrary.simpleMessage("أشخاص"),
    "phone": MessageLookupByLibrary.simpleMessage("الهاتف"),
    "phoneNumber": MessageLookupByLibrary.simpleMessage("رقم الهاتف"),
    "phoneRequired": MessageLookupByLibrary.simpleMessage("رقم الهاتف مطلوب"),
    "pickImage": MessageLookupByLibrary.simpleMessage("اختيار صورة"),
    "pickupPerson": MessageLookupByLibrary.simpleMessage("شخص الاستلام"),
    "pickups": MessageLookupByLibrary.simpleMessage("الاستلام"),
    "plan": MessageLookupByLibrary.simpleMessage("خطة"),
    "plans": MessageLookupByLibrary.simpleMessage("الخطط"),
    "playVideo": MessageLookupByLibrary.simpleMessage("تشغيل الفيديو"),
    "pleaseAcceptTerms": MessageLookupByLibrary.simpleMessage(
      "الرجاء قبول الشروط",
    ),
    "pleaseAddAtLeastOneSection": MessageLookupByLibrary.simpleMessage(
      "يرجى إضافة قسم واحد على الأقل",
    ),
    "pleaseAddTheStudentSubscriptionDateFirst":
        MessageLookupByLibrary.simpleMessage(
          "يرجى إضافة تاريخ اشتراك الطالب أولاً قبل الدفع.",
        ),
    "pleaseEnterAValidFromToTime": MessageLookupByLibrary.simpleMessage(
      "برجاء ادخال وقت من و إلى بشكل صحيح",
    ),
    "pleasePickAnImage": MessageLookupByLibrary.simpleMessage(
      "الرجاء اختيار صورة",
    ),
    "pleaseSelectStudent": MessageLookupByLibrary.simpleMessage(
      "يرجى اختيار طالب",
    ),
    "pleaseVerifyPhone": MessageLookupByLibrary.simpleMessage(
      "الرجاء التحقق من الهاتف",
    ),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage("سياسة الخصوصية"),
    "profile": MessageLookupByLibrary.simpleMessage("الملف الشخصي"),
    "quantity": MessageLookupByLibrary.simpleMessage("الكمية"),
    "question": MessageLookupByLibrary.simpleMessage("سؤال"),
    "questionType": MessageLookupByLibrary.simpleMessage("نوع السؤال"),
    "questions": MessageLookupByLibrary.simpleMessage("الأسئلة"),
    "questionsCount": MessageLookupByLibrary.simpleMessage("عدد الأسئلة"),
    "rating": MessageLookupByLibrary.simpleMessage("التقييم"),
    "ratingScale": MessageLookupByLibrary.simpleMessage("مقياس التقييم"),
    "reminderSentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم إرسال التذكير بنجاح",
    ),
    "removeImage": MessageLookupByLibrary.simpleMessage("إزالة الصورة"),
    "removeSection": MessageLookupByLibrary.simpleMessage("إزالة القسم"),
    "reports": MessageLookupByLibrary.simpleMessage("التقارير"),
    "resend": MessageLookupByLibrary.simpleMessage("إعادة إرسال"),
    "resendCode": MessageLookupByLibrary.simpleMessage("إعادة إرسال الرمز"),
    "reset": MessageLookupByLibrary.simpleMessage("إعادة تعيين"),
    "resetPassword": MessageLookupByLibrary.simpleMessage(
      "إعادة تعيين كلمة المرور",
    ),
    "responses": MessageLookupByLibrary.simpleMessage("الإجابات"),
    "results": MessageLookupByLibrary.simpleMessage("النتائج"),
    "sad": MessageLookupByLibrary.simpleMessage("حزين"),
    "saturday": MessageLookupByLibrary.simpleMessage("السبت"),
    "save": MessageLookupByLibrary.simpleMessage("حفظ"),
    "savePaymentMethods": MessageLookupByLibrary.simpleMessage("حفظ طرق الدفع"),
    "savedSuccessfully": MessageLookupByLibrary.simpleMessage("تم الحفظ بنجاح"),
    "search": MessageLookupByLibrary.simpleMessage("بحث"),
    "searchClasses": MessageLookupByLibrary.simpleMessage("البحث عن الفصول"),
    "searchQuestion": MessageLookupByLibrary.simpleMessage("البحث عن سؤال"),
    "searchStudent": MessageLookupByLibrary.simpleMessage("البحث عن طالب"),
    "section": MessageLookupByLibrary.simpleMessage("القسم"),
    "sectionDescription": MessageLookupByLibrary.simpleMessage("وصف القسم"),
    "sectionImage": MessageLookupByLibrary.simpleMessage("صورة القسم"),
    "sectionTitle": MessageLookupByLibrary.simpleMessage("عنوان القسم"),
    "sections": MessageLookupByLibrary.simpleMessage("الأقسام"),
    "selectClass": MessageLookupByLibrary.simpleMessage("يرجى اختيار فصل"),
    "selectClasses": MessageLookupByLibrary.simpleMessage("اختر الفصول"),
    "selectMonth": MessageLookupByLibrary.simpleMessage("اختر الشهر"),
    "selectNursery": MessageLookupByLibrary.simpleMessage("اختر الحضانة"),
    "selectNurseryToViewStatistics": MessageLookupByLibrary.simpleMessage(
      "اختر حضانة لعرض الإحصائيات",
    ),
    "selectPeriod": MessageLookupByLibrary.simpleMessage("اختر الفترة"),
    "selectRole": MessageLookupByLibrary.simpleMessage("اختر الدور"),
    "selectStaffMemberRole": MessageLookupByLibrary.simpleMessage(
      "اختر دور عضو الفريق",
    ),
    "selectTarget": MessageLookupByLibrary.simpleMessage("اختر الهدف"),
    "selectUserType": MessageLookupByLibrary.simpleMessage("اختر نوع المستخدم"),
    "selectedRole": MessageLookupByLibrary.simpleMessage("الدور المحدد"),
    "send": MessageLookupByLibrary.simpleMessage("إرسال"),
    "sendANewMessage": MessageLookupByLibrary.simpleMessage(
      "إرسال رسالة جديدة",
    ),
    "sendANewMessageTo": m5,
    "sendSupplies": MessageLookupByLibrary.simpleMessage("إرسال المستلزمات"),
    "sendSupplyToStudent": MessageLookupByLibrary.simpleMessage(
      "إرسال المستلزمات للطالب",
    ),
    "sendTo": MessageLookupByLibrary.simpleMessage("إرسال إلى"),
    "sentVerificationCode": MessageLookupByLibrary.simpleMessage(
      "لقد أرسلنا رمز تحقق إلى",
    ),
    "september": MessageLookupByLibrary.simpleMessage("سبتمبر"),
    "sessions": MessageLookupByLibrary.simpleMessage("الجدول اليومي"),
    "setupYourClasses": MessageLookupByLibrary.simpleMessage("إعداد فصولك"),
    "signIn": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
    "signUp": MessageLookupByLibrary.simpleMessage("اشتراك"),
    "singleActivity": MessageLookupByLibrary.simpleMessage("نشاط واحد"),
    "skip": MessageLookupByLibrary.simpleMessage("تخطي"),
    "skipForNow": MessageLookupByLibrary.simpleMessage("تخطي الآن"),
    "sleep": MessageLookupByLibrary.simpleMessage("النوم"),
    "sleepAdded": MessageLookupByLibrary.simpleMessage("تم إضافة النوم بنجاح"),
    "sleepTime": MessageLookupByLibrary.simpleMessage("وقت النوم"),
    "sleepy": MessageLookupByLibrary.simpleMessage("نعسان"),
    "snack": MessageLookupByLibrary.simpleMessage("وجبة خفيفة"),
    "some": MessageLookupByLibrary.simpleMessage("بعض"),
    "speakWithConfidence": MessageLookupByLibrary.simpleMessage("تحدث بثقة"),
    "specificClasses": MessageLookupByLibrary.simpleMessage("فصول محددة"),
    "staff": MessageLookupByLibrary.simpleMessage("الموظفين"),
    "staffMemberAddedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم إضافة عضو الفريق بنجاح",
    ),
    "staffMemberDeletedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم حذف عضو الفريق بنجاح",
    ),
    "staffMemberUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم تحديث عضو الفريق بنجاح",
    ),
    "staffMembers": MessageLookupByLibrary.simpleMessage("أعضاء الفريق"),
    "starRating": MessageLookupByLibrary.simpleMessage("تقييم بالنجوم (1-5)"),
    "stool": MessageLookupByLibrary.simpleMessage("براز"),
    "student": MessageLookupByLibrary.simpleMessage("الطالب"),
    "studentAndClass": MessageLookupByLibrary.simpleMessage("الطالب والفصل"),
    "studentDetails": MessageLookupByLibrary.simpleMessage("تفاصيل الطالب"),
    "studentName": MessageLookupByLibrary.simpleMessage("اسم الطالب"),
    "students": MessageLookupByLibrary.simpleMessage("الطلاب"),
    "submit": MessageLookupByLibrary.simpleMessage("إرسال"),
    "subscriptionDate": MessageLookupByLibrary.simpleMessage("تاريخ الاشتراك"),
    "subscriptionDateNeeded": MessageLookupByLibrary.simpleMessage(
      "تاريخ الاشتراك مطلوب",
    ),
    "subscriptionDateRequired": MessageLookupByLibrary.simpleMessage(
      "تاريخ الاشتراك مطلوب",
    ),
    "subscriptionExpiredPleaseContactSupport":
        MessageLookupByLibrary.simpleMessage(
          "انتهى الاشتراك، يرجى الاتصال بالدعم !",
        ),
    "subscriptionRemind": MessageLookupByLibrary.simpleMessage(
      "تذكير بالاشتراك",
    ),
    "subscriptionReminderBody": m6,
    "subscriptionReminderTitle": MessageLookupByLibrary.simpleMessage(
      "تذكير دفع الاشتراك",
    ),
    "subscriptions": MessageLookupByLibrary.simpleMessage("الاشتراكات"),
    "sunday": MessageLookupByLibrary.simpleMessage("الأحد"),
    "supervisor": MessageLookupByLibrary.simpleMessage("مشرف"),
    "supervisorRoleDescription": MessageLookupByLibrary.simpleMessage(
      "يشرف على العمليات ويدير أعضاء الفريق",
    ),
    "supplies": MessageLookupByLibrary.simpleMessage("المستلزمات"),
    "supply": MessageLookupByLibrary.simpleMessage("المستلزمات"),
    "supplyAdded": MessageLookupByLibrary.simpleMessage(
      "تم إضافة المستلزم بنجاح",
    ),
    "supplyName": MessageLookupByLibrary.simpleMessage("اسم المستلزمات"),
    "supplyType": MessageLookupByLibrary.simpleMessage("نوع المستلزم"),
    "tClass": MessageLookupByLibrary.simpleMessage("الفصل"),
    "target": MessageLookupByLibrary.simpleMessage("الهدف"),
    "teacher": MessageLookupByLibrary.simpleMessage("مدرس"),
    "teacherInfo": MessageLookupByLibrary.simpleMessage("معلومات المدرس"),
    "teacherName": MessageLookupByLibrary.simpleMessage("اسم المدرس"),
    "teacherRoleDescription": MessageLookupByLibrary.simpleMessage(
      "يدير الفصول والأنشطة وتقدم الطلاب",
    ),
    "teacherSignUp": MessageLookupByLibrary.simpleMessage("تسجيل المدرس"),
    "teachers": MessageLookupByLibrary.simpleMessage("المعلمين"),
    "team": MessageLookupByLibrary.simpleMessage("الفريق"),
    "text": MessageLookupByLibrary.simpleMessage("نص"),
    "textAnswer": MessageLookupByLibrary.simpleMessage("إجابة نصية"),
    "textAnswerPlaceholder": MessageLookupByLibrary.simpleMessage(
      "أدخل إجابتك هنا...",
    ),
    "thisFieldIsRequired": MessageLookupByLibrary.simpleMessage(
      "هذا الحقل مطلوب",
    ),
    "thursday": MessageLookupByLibrary.simpleMessage("الخميس"),
    "title": MessageLookupByLibrary.simpleMessage("العنوان"),
    "to": MessageLookupByLibrary.simpleMessage("إلى"),
    "today": MessageLookupByLibrary.simpleMessage("اليوم"),
    "toilet": MessageLookupByLibrary.simpleMessage("الحمام"),
    "toiletAdded": MessageLookupByLibrary.simpleMessage(
      "تم إضافة الحمام بنجاح",
    ),
    "toiletMethod": MessageLookupByLibrary.simpleMessage("طريقة الحمام"),
    "toiletType": MessageLookupByLibrary.simpleMessage("نوع الحمام"),
    "toiletWay": MessageLookupByLibrary.simpleMessage("المرحاض"),
    "total": MessageLookupByLibrary.simpleMessage("الإجمالي"),
    "totalVideoSizeExceeds50MB": MessageLookupByLibrary.simpleMessage(
      "الحجم الإجمالي للفيديوهات يتجاوز حد 50 ميجابايت",
    ),
    "tuesday": MessageLookupByLibrary.simpleMessage("الثلاثاء"),
    "unAssign": MessageLookupByLibrary.simpleMessage("إلغاء التعيين"),
    "unableToCalculateNextPaymentDate": MessageLookupByLibrary.simpleMessage(
      "غير قادر على حساب تاريخ الدفع التالي.",
    ),
    "unpaid": MessageLookupByLibrary.simpleMessage("غير مدفوع"),
    "unwell": MessageLookupByLibrary.simpleMessage("مريض"),
    "update": MessageLookupByLibrary.simpleMessage("تحديث"),
    "updateRequired": MessageLookupByLibrary.simpleMessage(
      "مطلوب تحديث للاستمرار في استخدام التطبيق. يرجى تحديثه الآن.",
    ),
    "uploadLogo": MessageLookupByLibrary.simpleMessage("تحميل الشعار"),
    "uploading": MessageLookupByLibrary.simpleMessage("رفع"),
    "urine": MessageLookupByLibrary.simpleMessage("بول"),
    "userNotFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على المستخدم",
    ),
    "validateYourPhoneFirstPlease": MessageLookupByLibrary.simpleMessage(
      "الرجاء التحقق من هاتفك أولاً",
    ),
    "verificationCodeIsWrong": MessageLookupByLibrary.simpleMessage(
      "رمز التحقق خاطئ",
    ),
    "verificationSuccessful": MessageLookupByLibrary.simpleMessage(
      "تم التحقق بنجاح",
    ),
    "verify": MessageLookupByLibrary.simpleMessage("التحقق"),
    "videoTooLarge": MessageLookupByLibrary.simpleMessage(
      "ملف الفيديو كبير جداً. يرجى اختيار فيديو أصغر.",
    ),
    "videoTooLarge50MB": MessageLookupByLibrary.simpleMessage(
      "ملف الفيديو كبير جداً. الحد الأقصى هو 50 ميجابايت",
    ),
    "view": MessageLookupByLibrary.simpleMessage("عرض"),
    "viewDetails": MessageLookupByLibrary.simpleMessage("عرض التفاصيل"),
    "warning": MessageLookupByLibrary.simpleMessage("تحذير"),
    "wednesday": MessageLookupByLibrary.simpleMessage("الأربعاء"),
    "weekly": MessageLookupByLibrary.simpleMessage("أسبوعياً"),
    "weeklyActivity": MessageLookupByLibrary.simpleMessage("نشاط أسبوعي"),
    "weeksOld": m7,
    "weeksShort": m8,
    "worried": MessageLookupByLibrary.simpleMessage("قلق"),
    "yearsMonthsShort": m9,
    "yearsOld": m10,
    "yearsShort": m11,
    "youAreParentPleaseLoginOnParentApplication":
        MessageLookupByLibrary.simpleMessage(
          "أنت ولي أمر، يرجى تسجيل الدخول على تطبيق الوالدين !",
        ),
    "youAreParentPleaseRegisterOnParentApplication":
        MessageLookupByLibrary.simpleMessage(
          "أنت ولي أمر، يرجى التسجيل على تطبيق الوالدين !",
        ),
    "youCannotDeleteThisQuestionBecauseitsHasStudentResults":
        MessageLookupByLibrary.simpleMessage(
          "لا يمكنك حذف هذا السؤال لأنه يحتوي على نتائج للطلاب",
        ),
  };
}
