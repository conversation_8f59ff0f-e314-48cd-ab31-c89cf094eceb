{"speakWithConfidence": "Speak with confidence", "getTalkingFrom": "Every day is a journey. \nSign in to join us.", "signUp": "Sign Up", "alreadyHaveAnAccount": "Already have an account?", "dontHaveAnAccount": "You don’t have account?", "noQuestions": "No questions", "question": "Question", "exams": "<PERSON><PERSON>", "addNewQuestion": "Add New Question", "sessions": "Sessions", "areYouSureToDeleteThisQuestion": "Are you sure to delete this question ?", "youCannotDeleteThisQuestionBecauseitsHasStudentResults": "You cannot delete this question because it's has student results", "signIn": "Sign In", "SignupAsa": "Sign up as a", "fromTimeShouldNotBeEqualToToTime": "From time should not be equal to to time", "editSupply": "Edit Supply", "administrator": "Administrator", "history": "History", "results": "Results", "teacher": "Teacher", "searchQuestion": "Search Question", "phoneNumber": "Phone Number", "password": "Password", "enter": "Enter", "allStudents": "All Students", "adminSignUp": "Administrator Sign up", "teacherSignUp": "Teacher Sign up", "nurseryLogo": "Nursery Logo", "nurseryName": "Nursery Name", "email": "Email", "iHaveReadThe": "I have read the", "subscriptions": "Subscriptions", "activeStudents": "Active Students", "inactiveStudents": "Inactive Students", "privacyPolicy": "Privacy Policy", "finishLetsStart": "Finish, let’s start", "uploadLogo": "Upload logo", "setupYourClasses": "Setup your classes", "addNewClass": "Add New Class", "editClass": "Edit Class", "SkipForNow": "Skip for now", "goodMorning": "Good Morning!", "goodAfternoon": "Good Afternoon!", "classes": "Classes", "className": "Class Name", "classDescription": "Class Description", "team": "Team", "students": "Students", "activities": "Activities", "staff": "Staff", "addStudents": "Add Students", "addNewStudents": "Add New Students", "skipForNow": "Skip for Now", "addNurseryTeam": "Add Nursery Team", "addNurseryTeamMember": "Add New Team Member", "skip": "<PERSON><PERSON>", "letsStart": "Let’s Start", "nurseryActivities": "Add a Nursery Activities", "addNurseryActivities": "Add New Activity ", "maxUploadFilesIsOnly4": "Max upload images is only 4", "maxUploadFilesIsOnly13": "Max upload files is only 13 (10 images + 3 videos)", "maxUploadVideosIsOnly3": "Max upload videos is only 3", "totalVideoSizeExceeds50MB": "Total video size exceeds 50MB limit", "videoTooLarge50MB": "Video file too large. Max size is 50MB", "responses": "Responses", "maxUploadFileSizeIsOnly5MB": "Max upload image size is only 5 MB", "congratulations": "Congratulations", "letsDoAGreatJob": "Let’s do a great job", "dashboard": "Dashboard", "selectNursery": "Select Nursery", "selectNurseryToViewStatistics": "Select a nursery to view statistics", "nurseryInformation": "Nursery Information", "maxStudents": "Max Students", "answers": "Answers", "questions": "Questions", "reset": "Reset", "joinedIn": "Joined in", "oneDay": "1 day", "daysOld": "{days} days", "today": "Today", "messages": "Messages", "home": "Home", "attendeesOfToday": "Attendees of Today", "Of": "Of", "currentActivity": "Current Activity", "attendance": "Attendance", "addAttendance": "Add Attendance", "addExams": "<PERSON>d <PERSON>", "addResults": "Add Results", "selectRole": "Select Role", "selectStaffMemberRole": "Select Staff Member Role", "supervisor": "Supervisor", "accountant": "Accountant", "teacherRoleDescription": "Manages classes, activities, and student progress", "supervisorRoleDescription": "Oversees operations and manages staff members", "accountantRoleDescription": "Handles financial records and student payments", "selectedRole": "Selected Role", "financial": "Financial", "emergency": "Emergency", "events": "Events", "members": "Members", "eventThisMonth": "Event this month", "createNewClass": "Create new class", "back": "Back", "myClasses": "My Classes", "myClass": "My Class", "teacherInfo": "Teacher info", "dailySchedule": "Daily Schedule", "pickImage": "Pick Image", "save": "Save", "createANewClass": "Create a new class", "createANewSupply": "Add a new Supply", "addANewStaffMember": "Add a new staff member", "teacherName": "Teacher Name", "supplyName": "supply Name", "description": "Description", "pleasePickAnImage": "Please pick an image", "studentName": "Student Name", "matherPhoneNumber": "Mather Phone number", "homeAddress": "Home Address", "addParentsPhoneNumber": "Add Parents Phone number", "birthDate": "Birth Date", "activityName": "Activity Name", "activityDescription": "Activity Description", "next": "Next", "motherPhoneNumber": "Mother Phone number", "parentPhoneNumber": "Parent Phone number", "attendanceChart": "Attendance chart", "studentAndClass": "Student & Class", "selectPeriod": "Select period", "attended": "attended", "absent": "absent", "incomeChart": "Income chart", "activitiesCompleted": "Activities completed", "billsChart": "Bills chart", "invoicesChart": "Income chart", "currentMonth": "Current month", "assignToClass": "Assign to class", "assign": "Assign", "assigned": "Assigned", "invoices": "Income", "bills": "Bills", "addNewBill": "Add New Bill", "from": "From", "to": "To", "addNewInvoice": "Add New Income", "edit": "Edit", "delete": "Delete", "date": "Date", "billName": "<PERSON>", "billAmount": "<PERSON>", "confirm": "Confirm", "confirmation": "Confirmation", "cancel": "Cancel", "areYouSureToDeleteThisBill": "Are you sure to delete this bill ?", "areYouSureToDeleteThisInvoice": "Are you sure to delete this Income ?", "areYouSureToDeleteThisClass": "Are you sure to delete this class ?", "areYouSureToDeleteThisSupply": "Are you sure to delete this supply ?", "areYouSureToDeleteThisTeacher": "Are you sure to delete this teacher ?", "areYouSureToDeleteThisStudent": "Are you sure to delete this student ?", "areYouSureToDeleteThisActivity": "Are you sure to delete this activity ?", "invoiceAmount": "Income Amount", "invoiceName": "Income Name", "deletedSuccessfully": "deleted successfully", "editSuccessfully": "Edit successfully", "editTeacher": "Edit Teacher", "addedSuccessfully": "Added successfully", "didNotGetCode": "Didn’t get the code?", "resendCode": "Resend Code", "completeVerification": "Complete Verification", "errorOccurred": "Error occurred", "verificationCodeIsWrong": "Verification code is wrong", "enterOtp": "Enter OTP", "sentVerificationCode": "We sent a verification code to", "submit": "Submit", "verify": "verify", "enterValidPhoneNumber": "Enter valid phone number", "enterPhoneNumberFirst": "First enter your phone number", "verificationSuccessful": "Verification successful", "pleaseAcceptTerms": "Please accept terms", "pleaseVerifyPhone": "Please verify phone", "noEvents": "No events", "noBills": "No Bills", "noInvoices": "No Income", "noClasses": "No Classes", "noNotifications": "No Notifications", "noTeachers": "No Teachers", "noStudents": "No Students", "noActivities": "No Activities", "noSupplies": "No Supplies", "active": "Active", "addNewEvent": "Add New Event", "eventName": "Event Name", "eventType": "Event Type", "mother": "Mother", "father": "Father", "address": "Address", "title": "Title", "message": "Message", "sendANewMessage": "Send a new message", "sendANewMessageTo": "Send a new message to {name}", "areYouSureToDeleteThisEvent": "Are you sure to delete this event ?", "editEvent": "Edit Event", "teachers": "Teachers", "resetPassword": "Reset your password", "forgetPassword": "Forget Password", "enterNewPassword": "Enter new password", "passwordsShouldMatch": "Passwords should match", "confirmNewPassword": "Confirm new password", "userNotFound": "User not Found", "add": "Add", "supplies": "Supplies", "food": "Food", "toilet": "<PERSON><PERSON><PERSON>", "sleep": "Sleep", "activityLevel": "Activity Level", "mood": "<PERSON><PERSON>", "addActivityLevel": "Add Activity Level", "addMood": "<PERSON><PERSON>", "low": "Low", "medium": "Medium", "high": "High", "angry": "Angry", "calm": "Calm", "excited": "Excited", "happy": "Happy", "sad": "Sad", "sleepy": "Sleepy", "unwell": "<PERSON><PERSON>", "worried": "Worried", "note": "Note", "enterNote": "Enter note", "breakfast": "Breakfast", "snack": "Snack", "lunch": "Lunch", "all": "All", "more": "More", "some": "Some", "none": "None", "urine": "<PERSON><PERSON>", "day": "Day", "stool": "Stool", "inClothes": "<PERSON><PERSON><PERSON>", "inTheDiaper": "Diaper", "notifications": "Notifications", "paidSuccessfully": "<PERSON><PERSON> successfully", "inTheToilet": "<PERSON><PERSON><PERSON>", "toiletType": "Toilet Type", "numberOfStudents": "Number of Students", "reminderSentSuccessfully": "<PERSON><PERSON><PERSON> sent successfully", "subscriptionRemind": "Subscription Remind", "areYouSureToSendSubscriptionRemind": "Are you sure to send subscription remind ?", "fees": "Fees", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "mealType": "Meal Type", "mealAmount": "<PERSON><PERSON> Amount", "tClass": "Class", "gender": "Gender", "assignSupplyToStudent": "Assign Supply to Student", "sendSupplyToStudent": "Send Supply To Student", "sendSupplies": "Send Supplies", "assignActivityToClass": "Assign Activity To Class", "logout": "Logout", "attendanceTracking": "Attendance Tracking", "reports": "Reports", "update": "Update", "activityChart": "Activity chart", "month": "Month", "noActivitiesFound": "No activities found", "maxStudentsReachedPleaseContactSupport": "Max students reached, please contact support !", "changeLanguage": "Change Language", "addActivity": "Add Activity", "editActivity": "Edit Activity", "english": "English", "arabic": "Arabic", "unAssign": "UnAssign", "messageSentSuccessfully": "Message sent successfully", "profile": "Profile", "changePassword": "Change Password", "validateYourPhoneFirstPlease": "Validate your phone first please", "passwordsDoNotMatch": "Passwords do not match", "passwordConfirmation": "Password Confirmation", "name": "Name", "search": "Search", "fromTimeShouldBeBeforeToTime": "From time should be before to time", "enterValidNurseryName": "Enter valid nursery name", "pleaseEnterAValidFromToTime": "Please enter a valid from & to time", "deleteAccount": "Delete Account", "areYouSureToDeleteYourAccount": "Are you sure to delete your account ?", "haveAnyQuestionsContactUs": "Have any questions?\nContact us", "chooseActivityAssignType": "Choose activity assign type", "weekly": "Weekly", "noPersons": "No persons", "classActivities": "Class Activities", "searchStudent": "Search Student", "enterPickupPerson": "Enter pickup person", "pickups": "Pickups", "persons": "Persons", "addNote": "Add Note", "singleActivity": "Single Activity", "pickupPerson": "Pickup Person", "weeklyActivity": "Weekly Activity", "addPickupPerson": "Add Pickup Person", "addMedia": "Add Media", "addVideo": "Add Video", "compressingVideo": "Compressing video...", "videoTooLarge": "Video file too large. Please select a smaller video.", "playVideo": "Play Video", "staffMembers": "Staff Members", "accessPermissions": "Access Permissions", "editPermissions": "Edit Permissions", "selectUserType": "Select User Type", "addStaffMember": "Add Staff Member", "editStaffMember": "Edit Staff Member", "noStaffMembers": "No staff members found", "staffMemberAddedSuccessfully": "Staff member added successfully", "staffMemberUpdatedSuccessfully": "Staff member updated successfully", "staffMemberDeletedSuccessfully": "Staff member deleted successfully", "permissionsUpdatedSuccessfully": "Permissions updated successfully", "editPermissionsFor": "Edit permissions for", "confirmDelete": "Confirm Delete", "areYouSureDeleteStaffMember": "Are you sure you want to delete {name}?", "nameRequired": "Name is required", "emailRequired": "Email is required", "invalidEmail": "Invalid email format", "phoneRequired": "Phone number is required", "passwordRequired": "Password is required", "passwordTooShort": "Password must be at least 6 characters", "enterName": "Enter name", "enterEmail": "Enter email", "pleaseSelectStudent": "Please select student", "notAvailableForAccountant": "Not available for accountant", "enterPhoneNumber": "Enter phone number", "enterJobTitle": "Enter job title", "enterPassword": "Enter password", "jobTitle": "Job Title", "media": "Media", "savedSuccessfully": "Saved successfully", "noMedia": "No Media", "meals": "Meals", "clickToContact": "Click to contact", "contactSupport": "Contact Support", "warning": "Warning", "noData": "No Data", "total": "Total", "updateRequired": "An update is required to continue using the app. Please update it now.", "selectClasses": "Select Classes", "compressing": "Compressing", "uploading": "Uploading", "noResultsFound": "No results found", "searchClasses": "Search Classes", "selectClass": "Please select a class", "paid": "Paid", "unpaid": "Unpaid", "areYouSureToMakeThisSubscriptionPaid": "Are you sure to make this subscription paid ?", "youAreParentPleaseRegisterOnParentApplication": "You are parent, please register on parent application !", "youAreParentPleaseLoginOnParentApplication": "You are parent, please login on parent application !", "subscriptionExpiredPleaseContactSupport": "Subscription expired, please contact support !", "adminAlreadyRegistered": "Admin already registered", "amount": "Amount", "motherOrParentAlreadyRegistered": "Mother or Parent already registered", "IfYouContinueThisWillAffectParentsAppAndTheyCanSeeAllChildrenUnderTheirNumber": "If you continue this will affect parents app and they can see all children under their number", "supply": "Supply", "activity": "Activity", "noHistoryForThisDate": "No history for this date", "student": "Student", "clear": "Clear", "announcements": "Announcements", "addAnnouncement": "Add Announcement", "editAnnouncement": "Edit Announcement", "target": "Target", "selectTarget": "Select Target", "resend": "Resend", "deleteAnnouncement": "Delete Announcement", "areYouSureYouWantToDeleteThisAnnouncement": "Are you sure you want to delete this announcement?", "by": "By", "send": "Send", "thisFieldIsRequired": "This field is required", "plan": "Plan", "plans": "Plans", "addPlan": "Add Plan", "editPlan": "Edit Plan", "deletePlan": "Delete Plan", "areYouSureYouWantToDeleteThisPlan": "Are you sure you want to delete this plan?", "noPlansForThisMonth": "No plans for this month", "sections": "Sections", "section": "Section", "addNewSection": "Add New Section", "removeSection": "Remove Section", "sectionTitle": "Section Title", "sectionDescription": "Section Description", "enterSectionTitle": "Enter section title", "enterSectionDescription": "Enter section description", "pleaseAddAtLeastOneSection": "Please add at least one section", "sectionImage": "Section Image", "addImage": "Add Image", "noVideos": "No Videos", "complete": "Complete", "removeImage": "Remove Image", "paymentMethods": "Payment Methods", "subscriptionDate": "Subscription Date", "noSubscriptionDateSet": "No subscription date set", "subscriptionDateRequired": "Subscription Date Required", "pleaseAddTheStudentSubscriptionDateFirst": "Please add the student subscription date first before making payment.", "unableToCalculateNextPaymentDate": "Unable to calculate next payment date.", "areYouSureToMakeThisSubscriptionPaidFor": "Are you sure to make this subscription paid for {date}?", "due": "Due", "subscriptionDateNeeded": "Subscription date needed", "subscriptionReminderTitle": "Subscription Payment Reminder", "subscriptionReminderBody": "{studentName} subscription payment is due today. Please collect the payment.", "enterInstapayNumberOrLink": "Enter InstaPay number or link", "enterVodafoneCashNumber": "Enter Vodafone Cash number", "enterEtisalatCashNumber": "Enter Etisalat Cash number", "enterWeCashNumber": "Enter WE Cash number", "enterOrangeCashNumber": "Enter Orange Cash number", "savePaymentMethods": "Save Payment Methods", "paymentMethodsUpdatedSuccessfully": "Payment methods updated successfully", "failedToUpdatePaymentMethods": "Failed to update payment methods", "nurseryDataNotFound": "Nursery data not found", "paymentPendingApproval": "Payment Pending Approval", "paymentMethod": "Payment Method", "paymentScreenshot": "Payment Screenshot", "jobApplications": "Job Applications", "jobApplication": "Job Application", "city": "City", "education": "Education", "cannotAddStudentWithThisPhoneNumber": "Cannot add student with this phone number", "expectedSalary": "Expected <PERSON><PERSON>", "cv": "CV", "jobs": "Jobs", "noJobApplications": "No job applications found", "view": "View", "addEvents": "Add Events", "addPickups": "Add Pickups", "addAnnouncements": "Add Announcements", "addPlans": "Add Plans", "jobApplicationDetails": "Job Application Details", "openCV": "Open CV", "checkOurLatestJobApplicationsNow": "Check our latest job applications now!", "phone": "Phone", "monthlyTotal": "Monthly Total", "studentDetails": "Student Details", "newborn": "Newborn", "oneWeek": "1 week old", "weeksOld": "{weeks} weeks old", "oneMonth": "1 month old", "monthsOld": "{months} months old", "oneYear": "1 year old", "yearsOld": "{years} years old", "newbornShort": "Newborn", "weeksShort": "{weeks}w", "monthsShort": "{months}m", "yearsShort": "{years}y", "yearsMonthsShort": "{years}y {months}m", "dailyActivities": "Daily Activities", "noActivitiesForThisDate": "No activities for this date", "assignActivity": "Assign Activity", "viewDetails": "View Details", "addToilet": "<PERSON>d <PERSON>", "addSleep": "Add Sleep", "addSupply": "Add Supply", "addFood": "Add Food", "moodAdded": "<PERSON><PERSON> added successfully", "toiletAdded": "<PERSON><PERSON><PERSON> added successfully", "sleepAdded": "Sleep added successfully", "supplyAdded": "Supply added successfully", "foodAdded": "Food added successfully", "diaper": "Diaper", "clothes": "<PERSON><PERSON><PERSON>", "toiletWay": "<PERSON><PERSON><PERSON>", "sleepTime": "Sleep Time", "toiletMethod": "<PERSON><PERSON>t Method", "moodType": "Mood Type", "supplyType": "Supply Type", "quantity": "Quantity", "editFood": "Edit Food", "editMood": "<PERSON>", "editToilet": "<PERSON>", "editSleep": "Edit Sleep", "editedSuccessfully": "Edited Successfully", "evaluations": "Evaluations", "addEvaluation": "Add Evaluation", "evaluation": "Evaluation", "evaluationQuestions": "Evaluation Questions", "addQuestion": "Add Question", "questionType": "Question Type", "textAnswer": "Text Answer", "starRating": "Star Rating (1-5)", "sendTo": "Send To", "allParents": "All Parents", "specificClasses": "Specific Classes", "selectMonth": "Select Month", "exportReport": "Export Report", "evaluationReport": "Evaluation Report", "parentName": "Parent Name", "evaluationDate": "Evaluation Date", "noEvaluations": "No evaluations found", "noEvaluationAnswers": "No evaluation answers found", "filterByDate": "Filter by Date", "filterByClass": "Filter by Class", "allMonths": "All Months", "january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December", "rating": "Rating", "text": "Text", "ratingScale": "Rating Scale", "textAnswerPlaceholder": "Enter your answer here...", "largeDatasetDetected": "Large dataset detected. Exporting first 100 responses for optimal performance.", "pdfExportedSuccessfully": "PDF exported successfully", "noDataToExport": "No data to export", "failedToExportPdf": "Failed to export PDF", "first100": "First 100", "errorLoadingEvaluations": "Error loading evaluations", "errorLoadingAnswers": "Error loading answers", "numberOfResponses": "Number of responses", "evaluationSummary": "Evaluation Summary", "questionsCount": "Questions Count", "noDate": "No Date"}