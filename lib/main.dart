import 'dart:io';

import 'package:connectify_app/firebase_options.dart';
import 'package:connectify_app/src/app.dart';
import 'package:connectify_app/src/shared/services/notifications/global_notification_handler.dart';
import 'package:connectify_app/src/shared/services/notifications/local_notifications_service.dart';
import 'package:fast_cached_network_image/fast_cached_network_image.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_restart_plus/flutter_restart_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path_provider/path_provider.dart';
import 'package:restart_app/restart_app.dart';
import 'package:xr_helper/xr_helper.dart';

Future<void> restartApp(BuildContext context) async {
  if (Platform.isIOS) {
    await FlutterRestartPlus().restartApp();
  } else {
    Restart.restartApp();
  }
}

//? Update Register Logo as admin on iOS

// Global variable to store initial notification message
RemoteMessage? initialNotificationMessage;

// Background message handler
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  Log.w('Background message received: ${message.notification?.title}');
}

Future<void> initCache() async {
  final storageLocation = (Platform.isIOS || Platform.isAndroid)
      ? (await getTemporaryDirectory()).path
      : (await getApplicationDocumentsDirectory()).path;

  FastCachedImageConfig.init(
    subDir: storageLocation,
  );
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

  try {
    LocalNotificationsService.init();

    await Future.wait([
      initCache(),
      Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      ),
      GetStorageService.init(),
    ]);

    // Set up Firebase messaging background handler
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    // Get initial message if app was opened from notification
    initialNotificationMessage =
        await FirebaseMessaging.instance.getInitialMessage();
    if (initialNotificationMessage != null) {
      Log.w(
          'App opened from notification: ${initialNotificationMessage!.notification?.title}');
      // Store the message globally to be handled when the app is ready
      GlobalNotificationHandler.setPendingMessage(initialNotificationMessage);
    }

    NotificationService.init();

    LocalNotificationsService.init();
  } catch (e) {
    Log.e('Error initializing app: $e');
  }

  runApp(const ProviderScope(child: BaseApp()));
}
