import 'package:collection/collection.dart';
import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/attendance/controllers/attendance_controller.dart';
import 'package:connectify_app/src/screens/attendance/models/attendance_model.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/consts/app_constants.dart';

final isClickAttendance = Map<String, bool>.from({});

class AttendanceCard extends StatefulWidget {
  final AttendanceModel? attendance;
  final StudentModel? student;
  final bool isAttended;
  final bool isToday;
  final String day;

  const AttendanceCard(
      {Key? key,
      required this.attendance,
      required this.student,
      this.isToday = false,
      this.day = '',
      required this.isAttended})
      : super(key: key);

  @override
  _AttendanceCardState createState() => _AttendanceCardState();
}

class _AttendanceCardState extends State<AttendanceCard> {
  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    final key = widget.day + widget.student!.id!.toString();

    final isAdmin = const UserModel().isCurrentUserAdmin;
    final currentUser = const UserModel().currentUser;
    final canAddAttendance = hasPermission(currentUser, 'addAttendance');

    return Consumer(
      builder: (context, ref, child) {
        final attendanceCtrl =
            ref.watch(attendancesProviderController(context));

        return Column(
          children: [
            BaseContainer(
              padding: 0,
              boxShadow: ConstantsWidgets.boxShadow,
              child: ClipRRect(
                borderRadius:
                    BorderRadius.circular(AppRadius.baseContainerRadius),
                child: ExpansionTile(
                  shape: InputBorder.none,
                  showTrailingIcon: const UserModel().isCurrentUserAdmin,
                  title: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          widget.student?.name ?? '',
                          style: context.blueHint.copyWith(fontSize: 16),
                        ),
                      ),
                      if (isLoading)
                        const CircularProgressIndicator()
                      else
                        IconButton(
                          onPressed: isAdmin || !canAddAttendance
                              ? null
                              : () async {
                                  if (!widget.isToday) return;

                                  setState(() {
                                    isLoading = true;
                                  });

                                  if (isClickAttendance[key] == true ||
                                      widget.isAttended) {
                                    final allAttendances = await attendanceCtrl
                                        .getAttendancesDataByDate(
                                            date: widget.attendance
                                                    ?.attendanceDate ??
                                                '');

                                    final attendance = allAttendances
                                        .firstWhereOrNull((attendance) =>
                                            attendance.student?.id ==
                                            widget.student?.id);

                                    await attendanceCtrl.deleteAttendance(
                                        id: attendance!.id!);

                                    isClickAttendance[key] = false;

                                    Log.w('FFF ${isClickAttendance[key]}');

                                    setState(() {
                                      isLoading = false;
                                    });

                                    return;
                                  }

                                  final attendance = AttendanceModel(
                                    student: widget.student,
                                    classModel: widget.student?.classModel,
                                  );

                                  await attendanceCtrl.addAttendance(
                                      attendance: attendance);

                                  isClickAttendance[key] = true;

                                  setState(() {
                                    isLoading = false;
                                  });
                                },
                          icon: Image.asset(
                            isClickAttendance.putIfAbsent(
                                        key, () => widget.isAttended) ==
                                    true
                                ? Assets.imagesAttended
                                : isClickAttendance[key] == false
                                    ? Assets.imagesAbsent
                                    : Assets.imagesAbsent,
                            height: 25.h,
                            width: 25.h,
                          ),
                          // icon:
                          // Image.asset(
                          //   isClickAttendance[key] == true
                          //       ? Assets.imagesAttended
                          //       : isClickAttendance[key] == false
                          //           ? Assets.imagesAbsent
                          //           : widget.isAttended
                          //               ? Assets.imagesAttended
                          //               : Assets.imagesAbsent,
                          //   height: 25.h,
                          //   width: 25.h,
                          // ),
                        )
                    ],
                  ),
                  leading: Container(
                    height: 35.h,
                    width: 40.w,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(30),
                      child: BaseCachedImage(
                        widget.student?.image?.url ?? '',
                        errorWidget: const BaseCachedImage(
                          AppConsts.studentPlaceholder,
                        ),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  tilePadding: const EdgeInsets.symmetric(
                      horizontal: AppSpaces.smallPadding),
                  children: const UserModel().isCurrentUserAdmin
                      ? [
                          Container(
                              padding:
                                  const EdgeInsets.all(AppSpaces.smallPadding),
                              child: Row(
                                children: [
                                  Text(
                                    '${context.tr.teacher} : ${widget.attendance?.teacher?.name ?? '-'}',
                                    style: context.subTitle,
                                  ),
                                  const Spacer(),
                                  Text(
                                    widget.attendance?.attendanceDate ?? '-',
                                    style: context.labelLarge
                                        .copyWith(color: Colors.grey),
                                  ),
                                ],
                              ))
                        ]
                      : [],
                ),
              ),
            )
          ],
        );
      },
    );
  }
}
// class AttendanceCard extends HookConsumerWidget {
//   final AttendanceModel? attendance;
//   final StudentModel? student;
//   final bool isAttended;
//   final bool isToday;
//   final String day;
//
//   const AttendanceCard(
//       {super.key,
//       required this.attendance,
//       required this.student,
//       this.isToday = false,
//       this.day = '',
//       required this.isAttended});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final attendanceCtrl = ref.watch(attendancesProviderController(context));
//
//     final key = day + student!.id!.toString();
//
//     final isAdmin = const UserModel().isAdmin;
//
//     return StatefulBuilder(builder: (context, setState) {
//       return Column(
//         children: [
//           BaseContainer(
//             padding: 0,
//             boxShadow: ConstantsWidgets.boxShadow,
//             child: ClipRRect(
//               borderRadius:
//                   BorderRadius.circular(AppRadius.baseContainerRadius),
//               child: ExpansionTile(
//                 shape: InputBorder.none,
//
//                 //! Student Name
//                 title: Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     Expanded(
//                       child: Text(
//                         student?.name ?? '',
//                         style: context.blueHint.copyWith(fontSize: 16),
//                       ),
//                     ),
//
//                     //! Attendance Icon Check
//                     IconButton(
//                       onPressed: isAdmin ? null :  () async {
//                         if (!isToday) return;
//
//                         if (isClickAttendance[key] == true || isAttended) {
//                           final allAttendances =
//                               await attendanceCtrl.getAttendancesDataForToday();
//
//                           final attendance = allAttendances.firstWhereOrNull(
//                               (attendance) =>
//                                   attendance.student?.id == student?.id);
//
//                           await attendanceCtrl.deleteAttendance(
//                               id: attendance!.id!);
//
//                           isClickAttendance[key] = false;
//
//                           setState(() {});
//
//                           return;
//                         }
//
//
//                         final attendance = AttendanceModel(
//                           student: student,
//                           classModel: student?.classModel,
//                         );
//
//                         await attendanceCtrl.addAttendance(
//                             attendance: attendance);
//
//                         isClickAttendance[key] = true;
//
//                         setState(() {});
//
//                       },
//                       icon: Image.asset(
//                         isClickAttendance[key] == true
//                             ? Assets.imagesAttended
//                             : isAttended
//                                 ? Assets.imagesAttended
//                                 : Assets.imagesAbsent,
//                         height: 25.h,
//                         width: 25.h,
//                       ),
//                     )
//                   ],
//                 ),
//
//                 //! Student Image
//                 leading: Container(
//                   height: 35.h,
//                   width: 40.w,
//                   decoration: const BoxDecoration(
//                     shape: BoxShape.circle,
//                   ),
//                   child: ClipRRect(
//                     borderRadius: BorderRadius.circular(30),
//                     child: BaseCachedImage(
//                       student?.image?.url ?? '',
//                       fit: BoxFit.cover,
//                     ),
//                   ),
//                 ),
//                 tilePadding: const EdgeInsets.symmetric(
//                     horizontal: AppSpaces.smallPadding),
//                 children: [
//                   //! Teacher Name & Attendance
//                   Container(
//                       padding: const EdgeInsets.all(AppSpaces.smallPadding),
//                       child: Row(
//                         children: [
//                           //! Teacher Name
//                           Text(
//                             '${context.tr.teacher} : ${attendance?.teacher?.name ?? '-'}',
//                             style: context.subTitle,
//                           ),
//                           const Spacer(),
//                           //! Attendance Date
//                           Text(
//                             attendance?.attendanceDate ?? '-',
//                             style:
//                                 context.labelLarge.copyWith(color: Colors.grey),
//                           ),
//                         ],
//                       ))
//                 ],
//               ),
//             ),
//           )
//         ],
//       );
//     });
//   }
// }
