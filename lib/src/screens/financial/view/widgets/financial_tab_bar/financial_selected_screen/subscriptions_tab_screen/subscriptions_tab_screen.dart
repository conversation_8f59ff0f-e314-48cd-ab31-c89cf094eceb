import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/financial/view/widgets/financial_tab_bar/financial_selected_screen/subscriptions_tab_screen/widgets/subscription_top_section.widget.dart';
import 'package:connectify_app/src/screens/financial/view/widgets/financial_tab_bar/financial_selected_screen/subscriptions_tab_screen/widgets/subscriptions_card_widget.dart';
import 'package:connectify_app/src/screens/financial/view/widgets/financial_tab_bar/financial_selected_screen/subscriptions_tab_screen/widgets/subscriptions_tab_bar.dart';
import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/screens/student/view/student_screen/widgets/students_grid_view.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_list/base_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../student/models/student_model.dart';

class SubscriptionsTabScreen extends HookConsumerWidget {
  final int currentIndex;

  const SubscriptionsTabScreen({super.key, this.currentIndex = 0});

  /// Calculate next payment date with 25-day rule
  /// Shows current month until 25 days have passed since subscription date
  /// Also considers if current month is already paid and there's a pending payment for next month
  DateTime? _calculateNextPaymentDate(
      DateTime subscriptionDate, StudentModel student) {
    final now = DateTime.now();
    DateTime currentMonthPaymentDate =
        DateTime(now.year, now.month, subscriptionDate.day);

    // Calculate days passed since subscription date in current month
    int daysPassed = 0;
    if (currentMonthPaymentDate.isBefore(now) ||
        currentMonthPaymentDate.isAtSameMomentAs(now)) {
      daysPassed = now.difference(currentMonthPaymentDate).inDays;
    }

    // Check if current month is already paid
    final currentMonthPaid = student.subscriptions.any((element) {
      if (!element.isPaid || !element.isApproved) return false;
      final paymentDate = element.date.formatStringToDateTime;
      return paymentDate.year == currentMonthPaymentDate.year &&
          paymentDate.month == currentMonthPaymentDate.month;
    });

    // If current month is paid, check for pending payment for next month
    if (currentMonthPaid) {
      final nextMonthDate =
          DateTime(now.year, now.month + 1, subscriptionDate.day);
      final hasNextMonthPending = student.subscriptions.any((element) {
        if (element.isPaid ||
            element.isApproved ||
            element.paymentScreenshot == null) return false;
        final paymentDate = element.date.formatStringToDateTime;
        return paymentDate.year == nextMonthDate.year &&
            paymentDate.month == nextMonthDate.month;
      });

      // If there's a pending payment for next month, show next month date
      if (hasNextMonthPending) {
        return nextMonthDate;
      }
    }

    // Apply 25-day rule for normal cases
    // If subscription day hasn't passed yet this month, show current month
    if (currentMonthPaymentDate.isAfter(now)) {
      return currentMonthPaymentDate;
    }
    // If subscription day has passed but less than 25 days, still show current month
    else if (daysPassed < 25) {
      return currentMonthPaymentDate;
    }
    // If 25 or more days have passed, show next month
    else {
      return DateTime(now.year, now.month + 1, subscriptionDate.day);
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isPaid = useState<bool>(currentIndex == 0);
    log('asfasfsfsf ${isPaid.value} fff $currentIndex');

    final getSubscriptionsStudentsFuture =
        ref.watch(getSubscriptionsStudentsProvider(context));

    return getSubscriptionsStudentsFuture.get(
      data: (students) {
        return HookBuilder(builder: (context) {
          final isEmptyList = students.isEmpty;

          if (isEmptyList) {
            return const EmptyStudentsList(
              navigateWidget: SubscriptionsTabScreen(),
            );
          }

          final paidStudents = useState(students.where((student) {
            // Check if student has subscription date
            if (student.subscriptionDate == null) return false;

            // Check if student has paid for current subscription period using 25-day rule
            final nextPaymentDate =
                _calculateNextPaymentDate(student.subscriptionDate!, student);

            if (nextPaymentDate == null) return false;

            return student.subscriptions.firstWhereOrNull(
                  (element) {
                    final paymentDate = element.date.formatStringToDateTime;
                    return paymentDate.year == nextPaymentDate.year &&
                        paymentDate.month == nextPaymentDate.month &&
                        element.isPaid &&
                        element.isApproved;
                  },
                ) !=
                null;
          }).toList());

          final unpaidStudents = useState(students.where((student) {
            // If student has no subscription date, they need to set it first
            if (student.subscriptionDate == null) return true;

            // Check if student needs to pay for current subscription period using 25-day rule
            final nextPaymentDate =
                _calculateNextPaymentDate(student.subscriptionDate!, student);

            if (nextPaymentDate == null) return true;

            return student.subscriptions.lastWhereOrNull(
                  (element) {
                    final paymentDate = element.date.formatStringToDateTime;
                    return paymentDate.year == nextPaymentDate.year &&
                        paymentDate.month == nextPaymentDate.month &&
                        element.isPaid &&
                        element.isApproved;
                  },
                ) ==
                null;
          }).toList());

          // sort un Approved with not approved subscriptions first sort by payment screenshot is not null
          unpaidStudents.value.sort((a, b) {
            final aHasScreenshot =
                a.subscriptions.any((s) => s.paymentScreenshot != null);
            final bHasScreenshot =
                b.subscriptions.any((s) => s.paymentScreenshot != null);

            if (aHasScreenshot && !bHasScreenshot) return -1;
            if (!aHasScreenshot && bHasScreenshot) return 1;

            return 0; // If both have or don't have screenshots, maintain original order
          });

          return StatefulBuilder(builder: (context, setState) {
            return Column(
              children: [
                SubscriptionsTabBarWidget(
                  isPaid: isPaid,
                  currentIndex: currentIndex,
                ),
                context.largeGap,
                SubscriptionTopSectionWidget(
                  isPaid: isPaid.value,
                  paidStudentsList: paidStudents.value,
                  unpaidStudentsList: unpaidStudents.value,
                ),
                context.mediumGap,
                const Divider(
                  color: ColorManager.grey,
                  thickness: 1,
                ).paddingSymmetric(horizontal: 10),
                context.xSmallGap,
                Expanded(
                  child: BaseList(
                    padding: const EdgeInsets.only(
                      bottom: 50,
                    ),
                    data: isPaid.value
                        ? paidStudents.value
                        : unpaidStudents.value,
                    separatorGap: context.mediumGap,
                    itemBuilder: (data, index) {
                      return SubscriptionsCardWidget(
                        student: data,
                        isPaidTab: isPaid.value,
                        paidStudents: paidStudents,
                        unPaidStudents: unpaidStudents,
                        setState: setState,
                      );
                    },
                    mainAxisSpacing: 15.h,
                    crossAxisSpacing: 10,
                  ),
                ),
              ],
            );
          });
        });
      },
    );
  }
}
