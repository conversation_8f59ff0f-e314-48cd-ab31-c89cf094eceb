import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/financial/view/financial_screen.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/student/view/student_screen/widgets/add_student.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/services/notifications/local_notifications_service.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:xr_helper/xr_helper.dart';

class SubscriptionsCardWidget extends ConsumerWidget {
  final StudentModel student;
  final bool isPaidTab;
  final ValueNotifier<List<StudentModel>> paidStudents;
  final ValueNotifier<List<StudentModel>> unPaidStudents;
  final Function setState;

  const SubscriptionsCardWidget({
    super.key,
    required this.student,
    required this.isPaidTab,
    required this.paidStudents,
    required this.unPaidStudents,
    required this.setState,
  });

  /// Calculate next payment date with 25-day rule
  /// Shows current month until 25 days have passed since subscription date
  /// Also considers if current month is already paid and there's a pending payment for next month
  DateTime? _calculateNextPaymentDate(DateTime subscriptionDate) {
    final now = DateTime.now();
    DateTime currentMonthPaymentDate =
        DateTime(now.year, now.month, subscriptionDate.day);

    // Calculate days passed since subscription date in current month
    int daysPassed = 0;
    if (currentMonthPaymentDate.isBefore(now) ||
        currentMonthPaymentDate.isAtSameMomentAs(now)) {
      daysPassed = now.difference(currentMonthPaymentDate).inDays;
    }

    // Check if current month is already paid
    final currentMonthPaid = student.subscriptions.any((element) {
      if (!element.isPaid || !element.isApproved) return false;
      final paymentDate = element.date.formatStringToDateTime;
      return paymentDate.year == currentMonthPaymentDate.year &&
          paymentDate.month == currentMonthPaymentDate.month;
    });

    // If current month is paid, check for pending payment for next month
    if (currentMonthPaid) {
      final nextMonthDate =
          DateTime(now.year, now.month + 1, subscriptionDate.day);
      final hasNextMonthPending = student.subscriptions.any((element) {
        if (element.isPaid ||
            element.isApproved ||
            element.paymentScreenshot == null) {
          return false;
        }
        final paymentDate = element.date.formatStringToDateTime;
        return paymentDate.year == nextMonthDate.year &&
            paymentDate.month == nextMonthDate.month;
      });

      // If there's a pending payment for next month, show next month date
      if (hasNextMonthPending) {
        return nextMonthDate;
      }
    }

    // Apply 25-day rule for normal cases
    // If subscription day hasn't passed yet this month, show current month
    if (currentMonthPaymentDate.isAfter(now)) {
      return currentMonthPaymentDate;
    }
    // If subscription day has passed but less than 25 days, still show current month
    else if (daysPassed < 25) {
      return currentMonthPaymentDate;
    }
    // If 25 or more days have passed, show next month
    else {
      return DateTime(now.year, now.month + 1, subscriptionDate.day);
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final studentCtrl = ref.read(studentChangeNotifierProvider(context));

    // Calculate next payment date with 25-day rule
    final nextPaymentDate = student.subscriptionDate != null
        ? _calculateNextPaymentDate(student.subscriptionDate!)
        : null;

    // Get the most recent approved and paid subscription for display
    final mostRecentApprovedSubscription =
        student.subscriptions.lastWhereOrNull(
      (element) {
        return element.isPaid && element.isApproved;
      },
    );

    // Check for pending payment request (has screenshot but not approved)
    // Look for any pending payment request regardless of date
    SubscriptionModel? pendingPaymentRequest =
        student.subscriptions.lastWhereOrNull(
      (element) {
        return !element.isPaid &&
            !element.isApproved &&
            element.paymentScreenshot != null;
      },
    );

    // sort un paid students with due next payment
    unPaidStudents.value.sort((a, b) {
      final aNextPaymentDate = _calculateNextPaymentDate(a.subscriptionDate!);
      final bNextPaymentDate = _calculateNextPaymentDate(b.subscriptionDate!);
      return aNextPaymentDate!.compareTo(bNextPaymentDate!);
    });

    return BaseContainer(
        padding: AppSpaces.appbarPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                //! Subscription (Name - Date)
                Expanded(
                  child: Row(
                    children: [
                      GestureDetector(
                        onTap: () => showAddStudentDialog(
                          context,
                          ref: ref,
                          student: student,
                          navigateWidget: FinancialScreen(
                            currentIndex: isPaidTab ? 0 : 1,
                          ),
                        ),
                        child: SizedBox(
                          height: 50.h,
                          width: 50.w,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(
                                AppRadius.baseContainerRadius),
                            child: Image.network(
                              student.image?.url ?? '',
                              height: 50.h,
                              width: 50.w,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  Image.network(
                                AppConsts.studentPlaceholder,
                              ),
                            ),
                          ),
                        ),
                      ),
                      context.smallGap,
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            //! Subscription Name
                            Text(
                              student.name,
                              style: context.blueHint
                                  .copyWith(fontWeight: FontWeight.bold),
                            ),

                            context.xSmallGap,

                            //! Subscription Date or Status
                            FittedBox(
                              fit: BoxFit.scaleDown,
                              child: Text(
                                student.subscriptionDate == null
                                    ? context.tr.noSubscriptionDateSet
                                    : isPaidTab &&
                                            mostRecentApprovedSubscription !=
                                                null
                                        ? '${context.tr.paid}: ${mostRecentApprovedSubscription.date}'
                                        : pendingPaymentRequest != null
                                            ? '${context.tr.paymentPendingApproval}\n${pendingPaymentRequest.date}' // Show actual payment request date
                                            : nextPaymentDate != null
                                                ? '${context.tr.due}: ${nextPaymentDate.formatDateToString}'
                                                : context
                                                    .tr.subscriptionDateNeeded,
                                style: context.hint.copyWith(
                                  fontSize: 12,
                                  color: student.subscriptionDate == null
                                      ? Colors.red
                                      : isPaidTab
                                          ? Colors.green
                                          : pendingPaymentRequest != null
                                              ? Colors.blue
                                              : Colors.orange,
                                ),
                              ),
                            ),
                            // Show payment method if there's a pending request
                            if (pendingPaymentRequest?.paymentMethod !=
                                null) ...[
                              context.xSmallGap,
                              Text(
                                '${context.tr.paymentMethod}: ${pendingPaymentRequest!.paymentMethod}',
                                style: context.hint.copyWith(
                                  fontSize: 11,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                //! Subscription Price

                Text(
                  // isPaidTab
                  //     ? ' \$${currentPeriodSubscription?.amount ?? 0}'
                  //     :
                  ' \$${student.fees ?? NurseryModelHelper.currentNursery()?.fees ?? 0}',
                  style: context.priceTitle,
                ),

                context.mediumGap,

                if (!isPaidTab)
                  IconButton(
                    onPressed: () async {
                      // Check if student has subscription date
                      if (student.subscriptionDate == null) {
                        // Open edit student dialog
                        showAddStudentDialog(
                          context,
                          ref: ref,
                          student: student,
                          navigateWidget: FinancialScreen(
                            currentIndex: isPaidTab ? 0 : 1,
                          ),
                        );

                        context.showBarMessage(
                          context.tr.pleaseAddTheStudentSubscriptionDateFirst,
                          isError: true,
                        );

                        return;
                      }

                      // Calculate next payment date with 25-day rule
                      final nextPaymentDate =
                          _calculateNextPaymentDate(student.subscriptionDate!);
                      if (nextPaymentDate == null) {
                        QuickAlert.show(
                          context: context,
                          title: context.tr.errorOccurred,
                          text: context.tr.unableToCalculateNextPaymentDate,
                          type: QuickAlertType.error,
                          confirmBtnText: 'OK',
                          confirmBtnColor: ColorManager.buttonColor,
                        );
                        return;
                      }

                      QuickAlert.show(
                        context: context,
                        title: context.tr.warning,
                        text:
                            context.tr.areYouSureToMakeThisSubscriptionPaidFor(
                          pendingPaymentRequest
                                  ?.date ?? // Use actual payment request date
                              nextPaymentDate.formatDateToString,
                        ),
                        type: QuickAlertType.info,
                        confirmBtnText: context.tr.confirm,
                        cancelBtnText: context.tr.cancel,
                        showCancelBtn: true,
                        confirmBtnColor: ColorManager.buttonColor,
                        onConfirmBtnTap: () async {
                          Navigator.of(context).pop();

                          List<SubscriptionModel> subscriptions;

                          // Check if there's a pending payment request to approve
                          if (pendingPaymentRequest != null) {
                            // Update the existing pending request to approved and paid
                            // Keep the original payment request date
                            subscriptions =
                                student.subscriptions.map((subscription) {
                              if (subscription.date ==
                                  pendingPaymentRequest!.date) {
                                return subscription.copyWith(
                                  date: pendingPaymentRequest!
                                      .date, // Keep original date
                                  isPaid: true,
                                  isApproved: true,
                                );
                              }
                              return subscription;
                            }).toList();
                          } else {
                            // Create a new subscription if no pending request exists
                            subscriptions = [
                              ...student.subscriptions,
                              SubscriptionModel(
                                  amount: student.fees ??
                                      NurseryModelHelper.currentNursery()
                                          ?.fees ??
                                      0,
                                  date: nextPaymentDate.formatDateToString,
                                  isPaid: true,
                                  isApproved: true),
                            ];
                          }

                          await studentCtrl.paySubscription(
                            studentId: student.id!,
                            subscriptions: subscriptions,
                            navigateWidget: FinancialScreen(
                              currentIndex: isPaidTab ? 0 : 1,
                            ),
                          );

                          final copiedStudent = student.copyWith(
                            subscriptions: subscriptions,
                          );

                          // Schedule next month's subscription reminder
                          if (copiedStudent.subscriptionDate != null) {
                            await LocalNotificationsService
                                .rescheduleSubscriptionReminder(
                              student: copiedStudent,
                              title: context.tr.subscriptionReminderTitle,
                              body: context.tr
                                  .subscriptionReminderBody(copiedStudent.name),
                            );
                          }

                          context.showBarMessage(context.tr.paidSuccessfully);

                          paidStudents.value.insert(0, copiedStudent);
                          unPaidStudents.value.removeWhere(
                            (element) => element.id == student.id,
                          );

                          setState(() {});
                        },
                      );
                    },
                    icon: const Icon(
                      Icons.done_all,
                      color: ColorManager.buttonColor,
                    ),
                  ),
              ],
            ),

            // Payment Screenshot Section
            if (pendingPaymentRequest?.paymentScreenshot != null) ...[
              context.mediumGap,
              Divider(
                color: Colors.grey.shade300,
              ),
              context.xSmallGap,
              Text(
                '${context.tr.paymentScreenshot}:',
                style: context.hint.copyWith(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue,
                ),
              ),
              context.xSmallGap,
              GestureDetector(
                onTap: () {
                  // Show full screen image
                  showDialog(
                    context: context,
                    builder: (context) => Dialog(
                      child: Image.network(
                        pendingPaymentRequest?.paymentScreenshot?.url ?? '',
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) =>
                            const Center(
                          child: Icon(
                            Icons.error,
                            size: 50,
                            color: Colors.red,
                          ),
                        ),
                      ),
                    ),
                  );
                },
                child: Container(
                  height: 120.h,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      pendingPaymentRequest?.paymentScreenshot?.url ?? '',
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) =>
                          const Center(
                        child: Icon(
                          Icons.error,
                          size: 30,
                          color: Colors.red,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ],
        ).paddingOnly(
          right: AppSpaces.mediumPadding,
        ));
  }

  void showAcceptSubscriptionDialog(
    BuildContext context, {
    required WidgetRef ref,
  }) {}
}
