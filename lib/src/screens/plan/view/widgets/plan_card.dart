import 'package:connectify_app/src/screens/plan/controller/plan_controller.dart';
import 'package:connectify_app/src/screens/plan/model/plan_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/base_popupmenu/base_popupmenu.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../auth/models/user_model.dart';
import '../add_plan_screen.dart';

class PlanCard extends HookConsumerWidget {
  final PlanModel plan;

  const PlanCard({
    super.key,
    required this.plan,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final planCtrl = ref.watch(planChangeNotifierControllerProvider(context));

    return BaseContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (plan.createdAt != null)
                Text(
                  plan.createdAt.formatDateToTimeAndString,
                  style: context.labelSmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              if (plan.classModel != null) ...[
                context.smallGap,
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue),
                  ),
                  child: Text(
                    plan.classModel!.name,
                    style: context.labelSmall?.copyWith(
                      color: Colors.blue,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ],
          ),

          context.mediumGap,
          // Header with title and actions
          Row(
            children: [
              Expanded(
                child: Text(
                  plan.sections.isNotEmpty &&
                          plan.sections.first.title.isNotEmpty
                      ? plan.sections.first.title
                      : context.tr.plan,
                  style: context.title?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              (const UserModel().isCurrentUserAdmin ||
                      hasPermission(const UserModel().currentUser, 'addPlans'))
                  ? BasePopupmenu(deleteOnTap: () {
                      _showDeleteDialog(context, planCtrl);
                    }, editOnTap: () {
                      context.to(AddPlanScreen(plan: plan));
                    })
                  : const SizedBox.shrink(),
            ],
          ),

          context.smallGap,

          // Sections
          if (plan.sections.isNotEmpty) ...[
            Theme(
              data: Theme.of(context).copyWith(
                dividerColor: Colors.transparent,
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
              ),
              child: ExpansionTile(
                tilePadding: EdgeInsets.only(
                  right: context.isEng ? 8 : 0,
                  left: context.isEng ? 0 : 8,
                ),
                collapsedIconColor: Colors.black,
                iconColor: Colors.black,
                backgroundColor: Colors.transparent,
                collapsedBackgroundColor: Colors.transparent,
                title: Text(
                  '${context.tr.sections} (${plan.sections.length})',
                  style: context.labelMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                children: [
                  ...plan.sections.asMap().entries.map((entry) {
                    final index = entry.key;
                    final section = entry.value;

                    return Container(
                      width: double.infinity,
                      margin: const EdgeInsets.only(bottom: 8),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (section.title.isNotEmpty) ...[
                            Text(
                              '${index + 1}. ${section.title}',
                              style: context.labelMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            if (section.description.isNotEmpty)
                              context.smallGap,
                          ],
                          if (section.description.isNotEmpty)
                            Text(
                              section.description,
                              style: context.labelLarge,
                            ),
                          if (section.image?.url?.isNotEmpty ?? false)
                            GestureDetector(
                              onTap: () {
                                showDialog(
                                  context: context,
                                  builder: (context) => Dialog(
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(12),
                                      child: Image.network(
                                        section.image!.url!,
                                        errorBuilder:
                                            (context, error, stackTrace) =>
                                                const SizedBox(),
                                      ),
                                    ),
                                  ),
                                );
                              },
                              child: Padding(
                                padding: const EdgeInsets.only(top: 8.0),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: Image.network(
                                    section.image!.url!,
                                    width: double.infinity,
                                    height: 120,
                                    fit: BoxFit.cover,
                                    errorBuilder:
                                        (context, error, stackTrace) =>
                                            const SizedBox(),
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    );
                  }).toList(),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, PlanController controller) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(context.tr.deletePlan),
          content: Text(context.tr.areYouSureYouWantToDeleteThisPlan),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(context.tr.cancel),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await controller.deletePlan(id: plan.id!);
              },
              child: Text(
                context.tr.delete,
                style: const TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }
}
