import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/notification/view/notifications_screen.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../main.dart';
import '../../../../../shared/widgets/icon_widget/icon_widget.dart';
import '../../../../notification/controller/notification_controller.dart';

ValueNotifier<ClassModel?> selectedTeacherClass =
    ValueNotifier<ClassModel?>(null);

class MainAppBar extends HookConsumerWidget implements PreferredSizeWidget {
  final String title;
  final bool isHome;
  final bool isBackButton;
  final String iconPath;
  final Function()? onBackButton;

  const MainAppBar({
    super.key,
    this.isBackButton = false,
    this.isHome = false,
    required this.title,
    this.iconPath = Assets.svgNotification,
    this.onBackButton,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final localNotificationId = GetStorageService.getLocalData(
      key: LocalKeys.notificationId,
    );

    final lastNotificationIdValue = useState<int?>(null);

    final notificationController =
        ref.read(notificationControllerProvider(context));

    final savedTeacherClassId = GetStorageService.getLocalData(
      key: LocalKeys.savedTeacherClassId,
    );

    if (selectedTeacherClass.value == null &&
        const UserModel().isCurrentUserNotAdmin) {
      selectedTeacherClass.value =
          const UserModel().currentUser.classes?.firstWhereOrNull(
                    (element) => element.id == savedTeacherClassId,
                  ) ??
              const UserModel().currentUser.classes?.firstOrNull;
    }

    useEffect(() {
      if (lastNotificationIdValue.value == null && iconPath != '') {
        notificationController.getLastNotification().then((data) {
          log('Last Notification ID: ${data.id}');
          lastNotificationIdValue.value = data.id;
        }).catchError((error) {
          lastNotificationIdValue.value = 0;
        });
      }
      return () {};
    }, []);

    final notificationCount = iconPath != ''
        ? (lastNotificationIdValue.value == localNotificationId ? 0 : 1)
        : 0;

    return AppBar(
      automaticallyImplyLeading:
          isBackButton && onBackButton == null ? true : false,
      leading: isBackButton && onBackButton != null
          ? IconButton(
              onPressed: onBackButton,
              icon: const Icon(Icons.arrow_back),
            )
          : null,
      toolbarHeight: 120.h,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          bottomRight: Radius.circular(AppRadius.appBarRadius),
          bottomLeft: Radius.circular(AppRadius.appBarRadius),
        ),
      ),
      title: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: context.whiteHeadLine
                    .copyWith(fontSize: isHome ? null : 16),
              ).paddingOnly(top: AppSpaces.xSmallPadding + 3),
              context.smallGap,
              if (isHome)
                Row(
                  children: [
                    if (NurseryModelHelper.currentNursery()?.image?.url != null)
                      ClipRRect(
                        borderRadius:
                            BorderRadius.circular(AppRadius.baseRadius),
                        child: Image.network(
                          NurseryModelHelper.currentNursery()?.image?.url ?? '',
                          width: 30.w,
                          height: 30.h,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              BaseCachedImage(
                            AppConsts.nurseryPlaceholder,
                            width: 30.w,
                            height: 30.h,
                          ),
                        ),
                      )
                    else
                      Image.asset(Assets.imagesCircleK)
                          .sized(height: 30.h, width: 30.w),
                    context.smallGap,
                    Text(
                      const UserModel().isCurrentUserNotAdmin
                          ? const UserModel().currentUser.name
                          : NurseryModelHelper.currentNursery()?.name ?? '',
                      style: context.whiteLabelLarge,
                    ),
                    if (const UserModel().isCurrentUserNotAdmin &&
                        (const UserModel().currentUser.classes?.length ?? 0) >
                            1) ...[
                      context.mediumGap,
                      SizedBox(
                        width: context.width * 0.4,
                        child: ValueListenableBuilder(
                          valueListenable: selectedTeacherClass,
                          builder: (context, value, child) {
                            return BaseDropDown(
                              isWhiteText: true,
                              onChanged: (value) {
                                selectedTeacherClass.value = value;

                                GetStorageService.setLocalData(
                                  key: LocalKeys.savedTeacherClassId,
                                  value: value?.id,
                                );

                                if (!kDebugMode) {
                                  restartApp(context);
                                }
                              },
                              data: const UserModel().currentUser.classes ?? [],
                              asString: (e) => (e as ClassModel?)?.name ?? '',
                              selectedValue: selectedTeacherClass.value,
                            );
                          },
                        ),
                      ),
                    ],
                  ],
                ),
            ],
          ),
          const Spacer(),
          if (iconPath != '')
            InkWell(
              onTap: () {
                if (notificationCount != 0) {
                  GetStorageService.setLocalData(
                    key: LocalKeys.notificationId,
                    value: lastNotificationIdValue.value,
                  );
                }

                context.to(const NotificationScreen());
              },
              child: Stack(
                alignment: Alignment.topRight,
                clipBehavior: Clip.none,
                children: [
                  IconWidget(icon: iconPath),
                  if (notificationCount != 0)
                    const Positioned(
                      top: -5,
                      right: -5,
                      child: CircleAvatar(
                        radius: 7,
                        backgroundColor: Colors.amber,
                      ),
                    )
                ],
              ),
            ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(isHome ? 150 : 60);
}
