import 'dart:developer';

import 'package:connectify_app/src/screens/food/controller/food_controller.dart';
import 'package:connectify_app/src/screens/food/view/widgets/add_food_widgets.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/student/view/student_details_screen/student_details_screen.dart';
import 'package:connectify_app/src/screens/toilet/controller/toilet_controller.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/radio_list_tile_widget/radio_list_tile_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/widgets/shared_widgets.dart';
import 'add_toilet_widgets.dart';
import '../../model/toilet_model.dart';

Future<void> showAddToiletDialog(context,
    {StudentModel? student, ToiletModel? existingToilet}) {
  return showDialog(
    context: context,
    builder: (context) {
      return HookConsumer(
        builder: (context, ref, child) {
          final toiletChangeNotifierCtrl =
              ref.watch(toiletChangeNotifierControllerProvider(context));

          final valueNotifiers = {
            ApiStrings.student: useState<StudentModel?>(student),
            ApiStrings.toiletType:
                useState<int>(existingToilet?.toiletType?.index ?? 0),
            ApiStrings.toiletWay:
                useState<int>(existingToilet?.toiletWay?.index ?? 0),
          };

          //!-----------------------------------------------------

          final formKey = useState(GlobalKey<FormState>());

          //!-----------------------------------------------------

          Future<void> addOrUpdateToilet() async {
            if (!formKey.value.currentState!.validate()) return;

            final selectedStudent =
                valueNotifiers[ApiStrings.student]?.value as StudentModel?;
            if (selectedStudent == null) {
              context.showBarMessage(context.tr.pleaseSelectStudent,
                  isError: true);
              return;
            }

            if (existingToilet != null) {
              // Update existing toilet
              await toiletChangeNotifierCtrl.updateToilet(
                toiletTypeValue:
                    valueNotifiers[ApiStrings.toiletType]?.value as int,
                toiletWayValue:
                    valueNotifiers[ApiStrings.toiletWay]?.value as int,
                student: selectedStudent,
                toiletId: existingToilet.id!,
              );
            } else {
              // Add new toilet
              await toiletChangeNotifierCtrl.addToilet(
                toiletTypeValue:
                    valueNotifiers[ApiStrings.toiletType]?.value as int,
                toiletWayValue:
                    valueNotifiers[ApiStrings.toiletWay]?.value as int,
                student: selectedStudent,
              );
            }

            if (!context.mounted) return;

            // Navigate back to student details if student was provided
            if (student != null) {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => StudentDetailsScreen(student: student),
                ),
              );
            } else {
              context.back();
            }
            context.showBarMessage(existingToilet != null
                ? context.tr.editedSuccessfully
                : context.tr.addedSuccessfully);
          }

          return AlertDialogWidget(
              header: existingToilet != null
                  ? context.tr.editToilet
                  : context.tr.toilet,
              isLoading: toiletChangeNotifierCtrl.isLoading,
              isImage: false,
              child: Form(
                key: formKey.value,
                child: AddToiletWidgets(
                  valueNotifiers: valueNotifiers,
                  hideStudentDropdown: student != null,
                ),
              ),
              onConfirm: () async => await addOrUpdateToilet());
        },
      );
    },
  );
}
