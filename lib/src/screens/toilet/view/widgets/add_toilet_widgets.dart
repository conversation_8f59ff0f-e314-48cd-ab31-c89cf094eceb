import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/radio_list_tile_widget/radio_list_tile_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/widgets/drop_downs/student_drop_down.dart';
import '../../../../shared/widgets/tab_bar_widgets/add_container_widget.dart';

class AddToiletWidgets extends HookWidget {
  final Map<String, ValueNotifier> valueNotifiers;
  final bool hideStudentDropdown;

  const AddToiletWidgets({
    super.key,
    required this.valueNotifiers,
    this.hideStudentDropdown = false,
  });

  @override
  Widget build(BuildContext context) {
    final List<List<String>> list = [
      [
        context.tr.urine,
        context.tr.stool,
      ],
      [
        context.tr.inTheDiaper,
        context.tr.inClothes,
        context.tr.inTheToilet,
      ]
    ];

    final List<List<String>> toiletImages = [
      [
        Assets.svgUrien,
        Assets.svgStool,
      ],
      [
        Assets.svgDiaper,
        Assets.svgClothes,
        Assets.svgToilet,
      ],
    ];

    final indexToiletTypeValue = useState<int>(0);
    final indexToiletWayValue = useState<int>(0);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!hideStudentDropdown) ...[
          StudentDropDown(
            selectedStudent: valueNotifiers[ApiStrings.student]
                as ValueNotifier<StudentModel?>,
          ),
          context.mediumGap,
        ],
        Text(
          context.tr.toiletType,
          style: context.title,
        ),
        context.mediumGap,
        Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: list.first
                .asMap()
                .entries
                .indexed
                .map((e) => SizedBox(
                      width: context.width / 2.5,
                      child: AddContainerWidget(
                        value: e.$1,
                        title: e.$2.value,
                        groupValue: valueNotifiers[ApiStrings.toiletType]
                            as ValueNotifier<int>,
                        imagePath: toiletImages.first[e.$1],
                        index: indexToiletTypeValue,
                        onChanged: (value) {
                          indexToiletTypeValue.value = value as int;
                          // Log.w(' ================ $value');
                          valueNotifiers[ApiStrings.toiletType]?.value = value;
                        },
                        onTap: () {
                          indexToiletTypeValue.value = e.$1;
                          valueNotifiers[ApiStrings.toiletType]?.value = e.$1;
                        },
                      ),
                    ))
                .toList(),
          ),
        ),
        context.mediumGap,
        Text(
          context.tr.toiletType,
          style: context.title,
        ),
        context.mediumGap,
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: list.last
              .asMap()
              .entries
              .indexed
              .map((e) => SizedBox(
                    width: context.width / 3.5,
                    child: AddContainerWidget(
                      value: e.$1,
                      title: e.$2.value,
                      groupValue: valueNotifiers[ApiStrings.toiletWay]
                          as ValueNotifier<int>,
                      imagePath: toiletImages.last[e.$1],
                      index: indexToiletWayValue,
                      onChanged: (value) {
                        indexToiletWayValue.value = value as int;
                        // Log.w(value);
                        valueNotifiers[ApiStrings.toiletWay]?.value = value;
                      },
                      onTap: () {
                        indexToiletWayValue.value = e.$1;
                        valueNotifiers[ApiStrings.toiletWay]?.value = e.$1;
                      },
                    ),
                  ))
              .toList(),
        ),
      ],
    );
  }
}
