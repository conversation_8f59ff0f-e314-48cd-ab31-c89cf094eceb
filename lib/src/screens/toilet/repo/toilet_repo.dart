import 'package:connectify_app/src/screens/toilet/model/toilet_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../history/model/history_model.dart';
import '../../history/repo/history_repo.dart';

// * =========================================================

final toiletRepoProvider = Provider((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);

  return ToiletRepo(networkApiServices);
});

//? ========================================================

class ToiletRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  ToiletRepo(this._networkApiServices);

//? get Toilet Data ========================================================
  Future<List<ToiletModel>> getToiletData() async {
    return await baseFunction(() async {
      final response =
          _networkApiServices.getResponse(ApiEndpoints.editDeleteToilet);

      final toiletData = await compute(responseToToiletModelList, response);

      return toiletData;
    });
  }

//? Add Toilet ========================================================

  Future<void> addToilet({required ToiletModel toilet}) async {
    return await baseFunction(() async {
      final toiletData = await _networkApiServices
          .postResponse(ApiEndpoints.editDeleteToilet, body: toilet.toJson());

      addNewHistory(
          historyModel: HistoryModel(
        historyType: HistoryType.toilet,
        toilet: ToiletModel(
          id: toiletData['data']['id'],
        ),
        student: toilet.student,
      ));
    });
  }

  //? Update Toilet ========================================================
  Future<void> updateToilet({required ToiletModel toilet}) async {
    return await baseFunction(() async {
      await _networkApiServices.putResponse(
        '${ApiEndpoints.editDeleteToilet}/${toilet.id}',
        data: toilet.toJson(),
      );

      addNewHistory(
          historyModel: HistoryModel(
        historyType: HistoryType.toilet,
        toilet: ToiletModel(
          id: toilet.id,
        ),
        student: toilet.student,
      ));
    });
  }

  //? Delete Toilet ========================================================
  Future<void> deleteToilet({required int id}) async {
    return await baseFunction(() async {
      await _networkApiServices
          .deleteResponse('${ApiEndpoints.editDeleteToilet}/$id');
    });
  }
}
