import 'dart:developer';

import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final staffMembersRepoProvider = Provider<StaffMembersRepo>((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);
  return StaffMembersRepo(networkApiServices);
});

class StaffMembersRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  StaffMembersRepo(this._networkApiServices);

  Future<List<UserModel>> getStaffMembers() async {
    return await baseFunction(() async {
      final response = await _networkApiServices.getResponse(
        ApiEndpoints.staffMembers,
      );

      final staffMembersData =
          List.from(response).map((e) => UserModel.fromJson(e)).toList();

      return staffMembersData;
    });
  }

  Future<int?> addStaffMember({
    required UserModel staffMember,
    required String pickedImage,
  }) async {
    return await baseFunction(() async {
      final res = await _networkApiServices.postResponse(
        ApiEndpoints.auth,
        body: staffMember.toStaffMemberJson(),
        filePaths: [pickedImage],
        fromAuth: true,
        isNormalMethod: true,
      );

      try {
        await _updateProfileImage(
          id: res[ApiStrings.user][ApiStrings.id].toString(),
          image: pickedImage,
        );
      } catch (e) {}

      return res[ApiStrings.user][ApiStrings.id];
    });
  }

  Future<void> editStaffMember({
    required UserModel staffMember,
    required String pickedImage,
    required int id,
  }) async {
    return await baseFunction(() async {
      final res = await _networkApiServices.putResponse(
        '${ApiEndpoints.users}/$id',
        data: staffMember.toStaffMemberJson(isEdit: true),
        fromAuth: true,
        isNormalMethod: true,
      );

      try {
        await _updateProfileImage(
          id: res[ApiStrings.id].toString(),
          image: pickedImage,
        );
      } catch (e) {}
    });
  }

  Future<void> deleteStaffMember(int staffMemberId) async {
    return await baseFunction(() async {
      return await _networkApiServices.deleteResponse(
        '${ApiEndpoints.users}/$staffMemberId',
      );
    });
  }

  Future<void> updateAccessPermissions(
      int staffMemberId, List<String> permissions) async {
    return await baseFunction(() async {
      final permissionsData = permissions
          .map((permission) => {
                'permission': permission,
              })
          .toList();

      return await _networkApiServices.putResponse(
        '${ApiEndpoints.editDeleteTeacher}/$staffMemberId',
        data: {
          'access_permissions': permissionsData,
        },
        fromAuth: true,
        isNormalMethod: true,
      );
    });
  }

  Future<void> _updateProfileImage({
    required String? id,
    required String? image,
  }) async {
    try {
      log('🟢id 🟢 $id');

      final uploadImageModel = UploadImageModel(
        refId: id.toString(),
        ref: 'plugin::users-permissions.user',
        field: 'image',
      );

      final response = await _networkApiServices.uploadFile(
        filePath: image ?? '',
        uploadImageModel: uploadImageModel,
      );

      log('🟢addProfileImage🟢 $response');
    } catch (error) {
      log('🔴addProfileImageError🔴 $error');
      rethrow;
    }
  }
}
