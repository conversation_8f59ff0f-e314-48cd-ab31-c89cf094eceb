import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/staff_members/view/widgets/add_staff_member_dialog.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class RoleSelectionScreen extends HookConsumerWidget {
  const RoleSelectionScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedRole = useState<UserTypeEnum>(UserTypeEnum.teacher);

    return Scaffold(
      appBar: AppBar(
        title: Text(context.tr.selectRole),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              child: Column(
                children: [
                  _buildRoleCard(
                    context: context,
                    role: UserTypeEnum.teacher,
                    title: context.tr.teacher,
                    description: context.tr.teacherRoleDescription,
                    icon: Icons.school,
                    color: Colors.blue,
                    isSelected: selectedRole.value == UserTypeEnum.teacher,
                    onTap: () => selectedRole.value = UserTypeEnum.teacher,
                  ),
                  context.mediumGap,
                  _buildRoleCard(
                    context: context,
                    role: UserTypeEnum.supervisor,
                    title: context.tr.supervisor,
                    description: context.tr.supervisorRoleDescription,
                    icon: Icons.supervisor_account,
                    color: Colors.green,
                    isSelected: selectedRole.value == UserTypeEnum.supervisor,
                    onTap: () => selectedRole.value = UserTypeEnum.supervisor,
                  ),
                  context.mediumGap,
                  _buildRoleCard(
                    context: context,
                    role: UserTypeEnum.accountant,
                    title: context.tr.accountant,
                    description: context.tr.accountantRoleDescription,
                    icon: Icons.account_balance,
                    color: Colors.pink,
                    isSelected: selectedRole.value == UserTypeEnum.accountant,
                    onTap: () => selectedRole.value = UserTypeEnum.accountant,
                  ),
                ],
              ),
            ),
            context.largeGap,
            Button(
              label: context.tr.next,
              onPressed: () {
                // context.back();
                showAddStaffMemberDialog(
                  context,
                  preSelectedRole: selectedRole.value,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoleCard({
    required BuildContext context,
    required UserTypeEnum role,
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.1) : Colors.white,
          border: Border.all(
            color: isSelected ? color : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            context.mediumGap,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: context.subTitle.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isSelected ? color : Colors.black87,
                    ),
                  ),
                  context.xSmallGap,
                  Text(
                    description,
                    style: context.body.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
