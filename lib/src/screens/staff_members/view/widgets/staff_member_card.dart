import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/staff_members/view/widgets/access_permissions_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/widgets/shared_widgets.dart';

class StaffMemberCard extends StatelessWidget {
  final UserModel staffMember;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onRefresh;

  const StaffMemberCard({
    super.key,
    required this.staffMember,
    this.onEdit,
    this.onDelete,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: ColorManager.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            spreadRadius: 1,
            blurRadius: 14,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Avatar
            _buildAvatar(context),

            const SizedBox(width: 16),

            // User Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name and User Type
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          staffMember.name,
                          style: context.title?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 8),
                      _buildUserTypeBadge(context),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Phone Number
                  if (staffMember.phone?.isNotEmpty == true)
                    Row(
                      children: [
                        Icon(
                          Icons.phone,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          staffMember.phone!,
                          style: context.body?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),

            // Actions
            PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'permissions':
                    _showAccessPermissions(context);
                    break;
                  case 'edit':
                    onEdit?.call();
                    break;
                  case 'delete':
                    _showDeleteConfirmation(context);
                    break;
                }
              },
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'permissions',
                  child: Row(
                    children: [
                      const Icon(Icons.security),
                      const SizedBox(width: 8),
                      Text(context.tr.editPermissions),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      const Icon(Icons.edit),
                      const SizedBox(width: 8),
                      Text(context.tr.edit),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      const Icon(Icons.delete, color: Colors.red),
                      const SizedBox(width: 8),
                      Text(
                        context.tr.delete,
                        style: const TextStyle(color: Colors.red),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAvatar(BuildContext context) {
    if (staffMember.image?.url?.isNotEmpty == true) {
      return CircleAvatar(
        radius: 30,
        child: ClipOval(
          child: BaseCachedImage(
            staffMember.image!.url!,
            width: 60,
            height: 60,
            fit: BoxFit.cover,
          ),
        ),
      );
    } else {
      return _buildInitialsAvatar(context);
    }
  }

  Widget _buildInitialsAvatar(BuildContext context) {
    final initials = _getInitials(staffMember.name);
    return CircleAvatar(
      radius: 30,
      backgroundColor: ColorManager.primaryColor.withOpacity(0.1),
      child: Text(
        initials,
        style: context.title?.copyWith(
          color: ColorManager.primaryColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  String _getInitials(String name) {
    final words = name.trim().split(' ').where((w) => w.isNotEmpty).toList();

    if (words.isEmpty) return '';

    if (words.length == 1) {
      return words[0].substring(0, 1).toUpperCase();
    } else {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    }
  }

  Widget _buildUserTypeBadge(BuildContext context) {
    Color backgroundColor;
    Color textColor;
    IconData icon;
    String label;

    switch (staffMember.userType) {
      case UserTypeEnum.teacher:
        backgroundColor = Colors.blue;
        textColor = Colors.white;
        icon = Icons.school;
        label = context.tr.teacher;
        break;
      case UserTypeEnum.supervisor:
        backgroundColor = Colors.green;
        textColor = Colors.white;
        icon = Icons.supervisor_account;
        label = context.tr.supervisor;
        break;
      case UserTypeEnum.accountant:
        backgroundColor = Colors.pink;
        textColor = Colors.white;
        icon = Icons.account_balance;
        label = context.tr.accountant;
        break;
      default:
        backgroundColor = Colors.grey;
        textColor = Colors.white;
        icon = Icons.person;
        label = 'Unknown';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: textColor,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: textColor,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _showAccessPermissions(BuildContext context) {
    context.to(AccessPermissionsScreen(
      staffMember: staffMember,
    ));
    // showDialog(
    //   context: context,
    //   builder: (context) => AccessPermissionsScreen(
    //     staffMember: staffMember,
    //     onPermissionsUpdated: onRefresh,
    //   ),
    // );
  }

  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr.confirmDelete),
        content: Text(
          context.tr.areYouSureDeleteStaffMember(staffMember.name),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(context.tr.cancel),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onDelete?.call();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: Text(context.tr.delete),
          ),
        ],
      ),
    );
  }
}
