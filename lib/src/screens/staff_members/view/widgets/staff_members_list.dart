import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/staff_members/controllers/staff_members_controller.dart';
import 'package:connectify_app/src/screens/staff_members/view/widgets/add_staff_member_dialog.dart';
import 'package:connectify_app/src/screens/staff_members/view/widgets/staff_member_card.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class StaffMembersList extends HookConsumerWidget {
  final List<UserModel> staffMembers;
  final VoidCallback? onRefresh;

  const StaffMembersList({
    super.key,
    required this.staffMembers,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final staffMembersController = ref.watch(staffMembersControllerProvider(context));

    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: staffMembers.length,
      itemBuilder: (context, index) {
        final staffMember = staffMembers[index];
        
        return StaffMemberCard(
          staffMember: staffMember,
          onEdit: () => _editStaffMember(context, staffMember),
          onDelete: () => _deleteStaffMember(context, staffMembersController, staffMember),
          onRefresh: onRefresh,
        );
      },
    );
  }

  void _editStaffMember(BuildContext context, UserModel staffMember) {
    showAddStaffMemberDialog(
      context,
      staffMember: staffMember,
    );
  }

  void _deleteStaffMember(
    BuildContext context,
    StaffMembersController controller,
    UserModel staffMember,
  ) async {
    if (staffMember.id != null) {
      await controller.deleteStaffMember(staffMember.id!);
    }
  }
}
