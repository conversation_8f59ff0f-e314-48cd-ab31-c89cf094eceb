import 'dart:developer';

import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/screens/staff_members/controllers/staff_members_controller.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/widgets/loading/loading_widget.dart';

class AccessPermissionsScreen extends HookConsumerWidget {
  final UserModel staffMember;

  const AccessPermissionsScreen({
    super.key,
    required this.staffMember,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final staffMembersController =
        ref.watch(staffMembersControllerProvider(context));

    // Get all available permissions from home model
    final allPermissions = _getAllAvailablePermissions(context);

    // Initialize permissions state
    final permissions = useState<Map<String, bool>>(
      Map.fromEntries(
        allPermissions.map((permission) => MapEntry(
              permission['key']!,
              containDefaultTeacherPermissionAndPermissionsAreEmpty(
                      staffMember, permission['key'] ?? '') ||
                  staffMember.accessPermissions.contains(permission['key']),
            )),
      ),
    );

    final isLoading = useState(false);

    return Scaffold(
      appBar: MainAppBar(
        title: '${staffMember.name} ${context.tr.accessPermissions}',
        iconPath: '',
        isBackButton: true,
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Button(
          loadingWidget: const LoadingWidget(),
          isLoading: isLoading.value || staffMembersController.isLoading,
          onPressed: () => _savePermissions(
            context,
            ref,
            permissions.value,
            isLoading,
          ),
          label: context.tr.save,
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: allPermissions.map((permission) {
            final key = permission['key']!;
            final title = permission['title']!;
            final type = permission['type'] ?? 'single';
            final color = permission['color'] as Color?;
            // final subtitle = permission['subtitle'];

            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.grey.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: SwitchListTile(
                title: type != 'single' && title.contains(' (')
                    ? RichText(
                        text: TextSpan(
                          style: context.labelLarge?.copyWith(
                            fontWeight: FontWeight.normal,
                          ),
                          children: [
                            TextSpan(
                              text: title.split(' (')[0],
                              style: const TextStyle(
                                fontWeight: FontWeight.normal,
                              ),
                            ),
                            TextSpan(
                              text: ' (${title.split(' (')[1]}',
                              style: TextStyle(
                                color: color,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      )
                    : Text(
                        title,
                        style: context.labelLarge?.copyWith(
                          fontWeight: FontWeight.normal,
                        ),
                      ),
                // subtitle: subtitle != null ? Text(subtitle) : null,
                value: permissions.value[key] ?? false,
                onChanged: (value) {
                  final newPermissions =
                      Map<String, bool>.from(permissions.value);
                  newPermissions[key] = value;
                  permissions.value = newPermissions;
                },
                contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                activeColor: color,
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  bool containDefaultTeacherPermissionAndPermissionsAreEmpty(
      UserModel staffMember, String permissionKey) {
    final defaultTeacherPermissions = [
      'students',
      'addStudents',
      'activities',
      'sessions',
      'events',
      'addEvents',
      'attendance',
      'addAttendance',
      'emergency',
      'supplies',
      'exams',
      'addExams',
      'addResults',
      'pickups',
      'announcements',
      'addAnnouncements',
      'plans',
      'addPlans',
      'evaluations',
      'addEvaluations',
    ];

    final defaultAccountantPermissions = [
      'students',
      'financial',
    ];

    return (staffMember.accessPermissions.isEmpty &&
            staffMember.isTeacher &&
            defaultTeacherPermissions.contains(permissionKey)) ||
        (staffMember.accessPermissions.isEmpty &&
            staffMember.isAccountant &&
            defaultAccountantPermissions.contains(permissionKey));
  }

  List<Map<String, dynamic>> _getAllAvailablePermissions(BuildContext context) {
    return [
      {
        'key': 'classes',
        'title': context.tr.classes,
        'subtitle': context.tr.classes,
        'type': 'single',
      },
      {
        'key': 'staffMembers',
        'title': context.tr.staffMembers,
        'subtitle': context.tr.members,
        'type': 'single',
      },
      {
        'key': 'students',
        'title': '${context.tr.students} (${context.tr.view})',
        'subtitle': context.tr.students,
        'type': 'view',
        'color': Colors.green,
      },
      {
        'key': 'addStudents',
        'title': '${context.tr.students} (${context.tr.add})',
        'subtitle': context.tr.addStudents,
        'type': 'add',
        'color': Colors.orange,
      },
      {
        'key': 'activities',
        'title': context.tr.activities,
        'subtitle': context.tr.activities,
        'type': 'single',
      },
      {
        'key': 'sessions',
        'title': context.tr.sessions,
        'subtitle': context.tr.sessions,
        'type': 'single',
      },
      {
        'key': 'events',
        'title': '${context.tr.events} (${context.tr.view})',
        'subtitle': context.tr.eventThisMonth,
        'type': 'view',
        'color': Colors.green,
      },
      {
        'key': 'addEvents',
        'title': '${context.tr.events} (${context.tr.add})',
        'subtitle': context.tr.addEvents,
        'type': 'add',
        'color': Colors.orange,
      },
      {
        'key': 'attendance',
        'title': '${context.tr.attendance} (${context.tr.view})',
        'subtitle': '',
        'type': 'view',
        'color': Colors.green,
      },
      {
        'key': 'addAttendance',
        'title': '${context.tr.attendance} ${context.tr.add}',
        'subtitle': '',
        'type': 'add',
        'color': Colors.orange,
      },
      {
        'key': 'financial',
        'title': context.tr.financial,
        'subtitle': '',
        'type': 'single',
      },
      {
        'key': 'emergency',
        'title': context.tr.emergency,
        'subtitle': '',
        'type': 'single',
      },
      {
        'key': 'supplies',
        'title': context.tr.supplies,
        'subtitle': '',
        'type': 'single',
      },
      {
        'key': 'exams',
        'title': context.tr.exams,
        'subtitle': '',
        'type': 'single',
      },
      {
        'key': 'addExams',
        'title': context.tr.addExams,
        'subtitle': '',
        'type': 'single',
      },
      {
        'key': 'addResults',
        'title': context.tr.addResults,
        'subtitle': '',
        'type': 'single',
      },
      {
        'key': 'meals',
        'title': context.tr.meals,
        'subtitle': '',
        'type': 'single',
      },
      {
        'key': 'pickups',
        'title': context.tr.pickups,
        'subtitle': '',
        'type': 'single',
      },
      {
        'key': 'history',
        'title': context.tr.history,
        'subtitle': '',
        'type': 'single',
      },
      {
        'key': 'announcements',
        'title': '${context.tr.announcements} (${context.tr.view})',
        'subtitle': '',
        'type': 'view',
        'color': Colors.green,
      },
      {
        'key': 'addAnnouncements',
        'title': '${context.tr.announcements} (${context.tr.add})',
        'subtitle': context.tr.addAnnouncements,
        'type': 'add',
        'color': Colors.orange,
      },
      {
        'key': 'plans',
        'title': '${context.tr.plans} (${context.tr.view})',
        'subtitle': '',
        'type': 'view',
        'color': Colors.green,
      },
      {
        'key': 'addPlans',
        'title': '${context.tr.plans} (${context.tr.add})',
        'subtitle': context.tr.addPlans,
        'type': 'add',
        'color': Colors.orange,
      },
      {
        'key': 'evaluations',
        'title': '${context.tr.evaluations} (${context.tr.view})',
        'subtitle': '',
        'type': 'view',
        'color': Colors.green,
      },
      {
        'key': 'addEvaluations',
        'title': '${context.tr.evaluations} (${context.tr.add})',
        'subtitle': context.tr.addEvaluation,
        'type': 'add',
        'color': Colors.orange,
      },
      {
        'key': 'jobs',
        'title': context.tr.jobs,
        'subtitle': '',
        'type': 'single',
      },
    ];
  }

  void _savePermissions(
    BuildContext context,
    WidgetRef ref,
    Map<String, bool> permissions,
    ValueNotifier<bool> isLoading,
  ) async {
    isLoading.value = true;

    try {
      final staffMembersController =
          ref.read(staffMembersControllerProvider(context));

      // Get only the permissions that are enabled
      final enabledPermissions = permissions.entries
          .where((entry) => entry.value)
          .map((entry) => entry.key)
          .toList();

      log('asfasf ${staffMember.id}');

      if (staffMember.id != null) {
        await staffMembersController.updateAccessPermissions(
          staffMember.id!,
          enabledPermissions,
        );
      }
    } finally {
      isLoading.value = false;
    }
  }
}
