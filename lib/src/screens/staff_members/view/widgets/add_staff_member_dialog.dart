import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/staff_members/controllers/staff_members_controller.dart';
import 'package:connectify_app/src/screens/staff_members/view/widgets/add_staff_member_fields.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../generated/assets.dart';
import '../../../../shared/data/remote/api_strings.dart';
import '../../../../shared/services/media/controller/media_controller.dart';
import '../../../../shared/widgets/shared_widgets.dart';
import '../../../class/models/class_model.dart';

Future<void> showAddStaffMemberDialog(
  BuildContext context, {
  UserModel? staffMember,
  UserTypeEnum? preSelectedRole,
}) async {
  return showDialog(
    context: context,
    builder: (context) {
      return HookConsumer(
        builder: (context, ref, child) {
          final controllers = {
            ApiStrings.name: useTextEditingController(text: staffMember?.name),
            ApiStrings.jobTitle:
                useTextEditingController(text: staffMember?.description),
            ApiStrings.phone:
                useTextEditingController(text: staffMember?.phone),
            ApiStrings.email:
                useTextEditingController(text: staffMember?.email),
            ApiStrings.password: useTextEditingController(),
          };

          final isEdit = staffMember != null;
          final selectedUserType = useState<UserTypeEnum>(
            staffMember?.userType ?? preSelectedRole ?? UserTypeEnum.teacher,
          );
          final selectedClasses = useState<List<ClassModel>>(
            staffMember?.classes ?? [],
          );
          final formKey = useState(GlobalKey<FormState>());
          final staffMembersController =
              ref.watch(staffMembersControllerProvider(context));
          final filePath = ref.watch(mediaPickerControllerProvider).filePath;

          Future<void> addEditStaffMember() async {
            if (isEdit) {
              await staffMembersController.editStaffMember(
                controllers: controllers,
                selectedClasses: selectedClasses.value,
                id: staffMember!.id!,
                pickedImage: filePath,
                userType: selectedUserType.value,
              );
            } else {
              await staffMembersController.addStaffMember(
                controllers: controllers,
                selectedClasses: selectedClasses.value,
                pickedImage: filePath,
                userType: selectedUserType.value,
              );
            }
          }

          return AlertDialogWidget(
            networkImage: staffMember?.image?.url ?? '',
            iconPath: Assets.iconsAdd,
            isImage: true,
            header:
                isEdit ? context.tr.editStaffMember : context.tr.addStaffMember,
            isLoading: staffMembersController.isLoading,
            child: Form(
              key: formKey.value,
              child: AddStaffMemberFields(
                controllers: controllers,
                selectedUserType: selectedUserType,
                selectedClasses: selectedClasses,
                classIds: staffMember?.classes?.map((e) => e.id).toList(),
                isEdit: isEdit,
                isRolePreSelected: preSelectedRole != null,
              ),
            ),
            onConfirm: () async {
              if (!formKey.value.currentState!.validate()) {
                return;
              }
              await addEditStaffMember();
            },
          );
        },
      );
    },
  ).then((value) {
    final mediaController =
        ProviderScope.containerOf(context).read(mediaPickerControllerProvider);
    mediaController.clearFiles();
  });
}
