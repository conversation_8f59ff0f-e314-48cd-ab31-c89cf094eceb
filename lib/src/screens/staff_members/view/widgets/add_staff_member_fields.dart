import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/widgets/drop_downs/class_drop_down.dart';
import '../../../class/models/class_model.dart';

class AddStaffMemberFields extends HookWidget {
  final Map<String, TextEditingController> controllers;
  final ValueNotifier<UserTypeEnum> selectedUserType;
  final ValueNotifier<List<ClassModel>> selectedClasses;
  final List<int?>? classIds;
  final bool isEdit;
  final bool isRolePreSelected;

  const AddStaffMemberFields({
    super.key,
    required this.controllers,
    required this.selectedUserType,
    required this.selectedClasses,
    this.classIds,
    required this.isEdit,
    this.isRolePreSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Name Field
        BaseTextField(
          controller: controllers[ApiStrings.name]!,
          label: context.tr.name,
          hint: context.tr.enterName,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return context.tr.nameRequired;
            }
            return null;
          },
        ),

        // context.mediumGap,
        //
        // // Email Field
        // BaseTextField(
        //   controller: controllers[ApiStrings.email]!,
        //   label: context.tr.email,
        //   hint: context.tr.enterEmail,
        //   validator: (value) {
        //     if (value?.isEmpty ?? true) {
        //       return context.tr.emailRequired;
        //     }
        //     if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
        //         .hasMatch(value!)) {
        //       return context.tr.invalidEmail;
        //     }
        //     return null;
        //   },
        // ),

        context.mediumGap,

        // Phone Field
        BaseTextField(
          controller: controllers[ApiStrings.phone]!,
          textInputType: TextInputType.phone,
          label: context.tr.phoneNumber,
          hint: context.tr.enterPhoneNumber,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return context.tr.phoneRequired;
            }
            return null;
          },
        ),

        context.mediumGap,

        if (selectedUserType.value == UserTypeEnum.teacher) ...[
          // Job Title Field
          BaseTextField(
            controller: controllers[ApiStrings.jobTitle]!,
            label: context.tr.jobTitle,
            hint: context.tr.enterJobTitle,
          ),

          context.mediumGap,
        ],
        // Password Field (only for new staff members)
        if (!isEdit)
          BaseTextField(
            controller: controllers[ApiStrings.password]!,
            label: context.tr.password,
            hint: context.tr.enterPassword,
            isObscure: true,
            validator: (value) {
              if (value?.isEmpty ?? true) {
                return context.tr.passwordRequired;
              }
              if (value!.length < 6) {
                return context.tr.passwordTooShort;
              }
              return null;
            },
          ),

        if (!isEdit) context.mediumGap,

        // Class Selection (only for teachers and supervisors)
        ValueListenableBuilder<UserTypeEnum>(
          valueListenable: selectedUserType,
          builder: (context, userType, child) {
            if (userType == UserTypeEnum.teacher ||
                userType == UserTypeEnum.supervisor) {
              return Column(
                children: [
                  MultiClassDropDown(
                    selectedClasses: selectedClasses,
                    classIds: classIds,
                  ),
                  context.mediumGap,
                ],
              );
            }
            return const SizedBox.shrink();
          },
        ),

        // User Type Selection (only show if role is not pre-selected)
        // if (!isRolePreSelected)
        //   _buildUserTypeSelection(context, selectedUserType)
        // else
        if (!isEdit) _buildSelectedRoleDisplay(context, selectedUserType),
      ],
    );
  }

  Widget _buildUserTypeSelection(
    BuildContext context,
    ValueNotifier<UserTypeEnum> selectedUserType,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.tr.selectUserType,
          style: context.body.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        ...UserTypeEnum.values
            .where((type) =>
                type != UserTypeEnum.admin && type != UserTypeEnum.parent)
            .map((type) => RadioListTile<UserTypeEnum>(
                  title: Text(_getUserTypeLabel(context, type)),
                  value: type,
                  groupValue: selectedUserType.value,
                  onChanged: (value) {
                    if (value != null) {
                      selectedUserType.value = value;
                    }
                  },
                  contentPadding: EdgeInsets.zero,
                )),
      ],
    );
  }

  Widget _buildSelectedRoleDisplay(
    BuildContext context,
    ValueNotifier<UserTypeEnum> selectedUserType,
  ) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          const SizedBox(width: 8),
          Icon(
            _getRoleIcon(selectedUserType.value),
            color: _getRoleColor(selectedUserType.value),
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                context.tr.selectedRole,
                style: context.body.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              Text(
                _getUserTypeLabel(context, selectedUserType.value),
                style: context.subTitle.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  IconData _getRoleIcon(UserTypeEnum type) {
    switch (type) {
      case UserTypeEnum.teacher:
        return Icons.school;
      case UserTypeEnum.supervisor:
        return Icons.supervisor_account;
      case UserTypeEnum.accountant:
        return Icons.account_balance;
      default:
        return Icons.person;
    }
  }

  Color _getRoleColor(UserTypeEnum type) {
    switch (type) {
      case UserTypeEnum.teacher:
        return Colors.blue;
      case UserTypeEnum.supervisor:
        return Colors.green;
      case UserTypeEnum.accountant:
        return Colors.pink;
      default:
        return Colors.grey;
    }
  }

  String _getUserTypeLabel(BuildContext context, UserTypeEnum type) {
    switch (type) {
      case UserTypeEnum.teacher:
        return context.tr.teacher;
      case UserTypeEnum.supervisor:
        return context.tr.supervisor;
      case UserTypeEnum.accountant:
        return context.tr.accountant;
      default:
        return type.name;
    }
  }
}
