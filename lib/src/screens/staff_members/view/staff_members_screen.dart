import 'package:connectify_app/src/screens/staff_members/controllers/staff_members_controller.dart';
import 'package:connectify_app/src/screens/staff_members/view/widgets/role_selection_screen.dart';
import 'package:connectify_app/src/screens/staff_members/view/widgets/staff_members_list.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class StaffMembersScreen extends HookConsumerWidget {
  const StaffMembersScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final staffMembersController =
        ref.watch(staffMembersControllerProvider(context));

    final refresh = useCallback(() async {
      await staffMembersController.getStaffMembers();
    }, [staffMembersController]);

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        refresh();
      });
      return null;
    }, []);

    return PopScope(
      canPop: true,
      child: Scaffold(
        appBar: AppBar(
          title: Text(context.tr.staffMembers),
        ),
        body: RefreshIndicator(
          onRefresh: refresh,
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Button(
                  label: context.tr.addStaffMember,
                  onPressed: () {
                    context.to(const RoleSelectionScreen());
                  },
                ),
              ),
              Expanded(
                child: staffMembersController.isLoading
                    ? const Center(child: LoadingWidget())
                    : staffMembersController.staffMembers.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.people_outline,
                                  size: 64,
                                  color: Colors.grey[400],
                                ),
                                context.mediumGap,
                                Text(
                                  context.tr.noStaffMembers,
                                  style: context.body?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                                ),
                                context.mediumGap,
                                SizedBox(
                                  height: 45,
                                  width: 200,
                                  child: Button(
                                      onPressed: () {
                                        context.to(const RoleSelectionScreen());
                                      },
                                      label: context.tr.addStaffMember),
                                ),
                              ],
                            ),
                          )
                        : StaffMembersList(
                            staffMembers: staffMembersController.staffMembers,
                            onRefresh: refresh,
                          ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
