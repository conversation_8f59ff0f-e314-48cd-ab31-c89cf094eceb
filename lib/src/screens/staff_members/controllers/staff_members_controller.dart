import 'dart:developer';

import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/staff_members/repos/staff_members_repo.dart';
import 'package:connectify_app/src/screens/staff_members/view/staff_members_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../shared/data/remote/api_strings.dart';
import '../../class/models/class_model.dart';
import '../view/widgets/access_permissions_screen.dart';

final staffMembersControllerProvider =
    ChangeNotifierProvider.family<StaffMembersController, BuildContext>(
  (ref, context) {
    final staffMembersRepo = ref.watch(staffMembersRepoProvider);
    return StaffMembersController(staffMembersRepo, context);
  },
);

class StaffMembersController extends ChangeNotifier {
  final StaffMembersRepo _staffMembersRepo;
  final BuildContext _context;

  StaffMembersController(this._staffMembersRepo, this._context);

  bool _isLoading = false;

  bool get isLoading => _isLoading;

  List<UserModel> _staffMembers = [];

  List<UserModel> get staffMembers => _staffMembers;

  String? _errorMessage;

  String? get errorMessage => _errorMessage;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  Future<void> getStaffMembers() async {
    try {
      _setLoading(true);
      _setError(null);

      final result = await _staffMembersRepo.getStaffMembers();
      _staffMembers = result;

      Log.i('Staff Members loaded: ${_staffMembers.length}');
    } catch (e, s) {
      Log.e('Error loading staff members: $e $s');
      _setError(e.toString());

      if (_context.mounted) {
        _context.showBarMessage(
          'Error loading staff members: $e',
          isError: true,
        );
      }
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> addStaffMember({
    required Map<String, TextEditingController> controllers,
    required List<ClassModel> selectedClasses,
    required String pickedImage,
    required UserTypeEnum userType,
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      final staffMember = UserModel(
        name: controllers[ApiStrings.name]!.text,
        email: controllers[ApiStrings.email]!.text,
        phone: controllers[ApiStrings.phone]!.text,
        description: controllers[ApiStrings.jobTitle]!.text,
        password: controllers[ApiStrings.password]!.text,
        userType: userType,
        classes: selectedClasses,
        accessPermissions: _getDefaultPermissions(userType),
      );

      final id = await _staffMembersRepo.addStaffMember(
        staffMember: staffMember,
        pickedImage: pickedImage,
      );

      // Refresh the list
      await getStaffMembers();

      if (_context.mounted) {
        _context.back();
        final copiedStaffMember = staffMember.copyWith(id: id);
        log('asffaf ${copiedStaffMember.id}');

        _context.to(AccessPermissionsScreen(
          staffMember: copiedStaffMember,
        ));
        _context.showBarMessage(
          _context.tr.staffMemberAddedSuccessfully,
        );
      }

      return true;
    } catch (e) {
      Log.e('Error adding staff member: $e');
      _setError(e.toString());

      if (_context.mounted) {
        _context.showBarMessage(
          'Error adding staff member: $e',
          isError: true,
        );
      }

      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> editStaffMember({
    required Map<String, TextEditingController> controllers,
    required List<ClassModel> selectedClasses,
    required int id,
    required String pickedImage,
    required UserTypeEnum userType,
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      final staffMember = UserModel(
        id: id,
        name: controllers[ApiStrings.name]!.text,
        email: controllers[ApiStrings.email]!.text,
        phone: controllers[ApiStrings.phone]!.text,
        description: controllers[ApiStrings.jobTitle]!.text,
        userType: userType,
        classes: selectedClasses,
        accessPermissions: _getDefaultPermissions(userType),
      );

      await _staffMembersRepo.editStaffMember(
        staffMember: staffMember,
        pickedImage: pickedImage,
        id: id,
      );

      // Refresh the list
      await getStaffMembers();

      if (_context.mounted) {
        _context.back();
        _context.toReplacement(const StaffMembersScreen());
        _context.showBarMessage(
          _context.tr.staffMemberUpdatedSuccessfully,
          isError: false,
        );
      }

      return true;
    } catch (e) {
      Log.e('Error updating staff member: $e');
      _setError(e.toString());

      if (_context.mounted) {
        _context.showBarMessage(
          'Error updating staff member: $e',
          isError: true,
        );
      }

      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> deleteStaffMember(int staffMemberId) async {
    try {
      _setLoading(true);
      _setError(null);

      await _staffMembersRepo.deleteStaffMember(staffMemberId);

      // Refresh the list

      if (_context.mounted) {
        _context.toReplacement(const StaffMembersScreen());
        _context.showBarMessage(
          _context.tr.staffMemberDeletedSuccessfully,
          isError: false,
        );
      }

      return true;
    } catch (e) {
      Log.e('Error deleting staff member: $e');
      _setError(e.toString());

      if (_context.mounted) {
        _context.showBarMessage(
          'Error deleting staff member: $e',
          isError: true,
        );
      }

      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateAccessPermissions(
      int staffMemberId, List<String> permissions) async {
    try {
      _setLoading(true);
      _setError(null);

      await _staffMembersRepo.updateAccessPermissions(
          staffMemberId, permissions);

      // Refresh the list
      await getStaffMembers();

      if (_context.mounted) {
        _context.back();
        _context.toReplacement(const StaffMembersScreen());

        _context.showBarMessage(
          _context.tr.permissionsUpdatedSuccessfully,
          isError: false,
        );
      }

      return true;
    } catch (e) {
      Log.e('Error updating permissions: $e');
      _setError(e.toString());

      if (_context.mounted) {
        _context.showBarMessage(
          'Error updating permissions: $e',
          isError: true,
        );
      }

      return false;
    } finally {
      _setLoading(false);
    }
  }

  List<String> _getDefaultPermissions(UserTypeEnum userType) {
    switch (userType) {
      case UserTypeEnum.teacher:
        return [
          'students',
          'activities',
          'sessions',
          'events',
          'attendance',
          'addAttendance',
          'emergency',
          'supplies',
          'exams',
          'addExams',
          'addResults',
          'pickups',
          'history',
          'announcements',
          'plans',
        ];
      case UserTypeEnum.supervisor:
        return [
          'classes',
          'students',
          'activities',
          'sessions',
          'events',
          'attendance',
          'addAttendance',
          'emergency',
          'supplies',
          'exams',
          'addExams',
          'addResults',
          'pickups',
          'history',
          'announcements',
          'plans',
        ];
      case UserTypeEnum.accountant:
        return [
          'students',
          'financial',
        ];
      default:
        return [];
    }
  }
}
