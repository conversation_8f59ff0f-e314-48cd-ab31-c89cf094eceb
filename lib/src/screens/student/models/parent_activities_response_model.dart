import 'package:connectify_app/src/screens/activity_level/model/activity_level_model.dart';
import 'package:connectify_app/src/screens/food/model/food_model.dart';
import 'package:connectify_app/src/screens/mood/model/mood_model.dart';
import 'package:connectify_app/src/screens/sleep/model/sleep_model.dart';
import 'package:connectify_app/src/screens/toilet/model/toilet_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';

import '../../activities/models/teacher_activity_model.dart';

/// Response model for the combined parent activities API
class ParentActivitiesResponseModel {
  final List<TeacherActivityModel> teacherActivities;
  final List<FoodModel> foods;
  final List<ToiletModel> toilets;
  final List<SleepModel> sleeps;
  final List<MoodModel> moods;
  final List<ActivityLevelModel> activityLevels;
  final ParentActivitiesMetaModel meta;

  const ParentActivitiesResponseModel({
    this.teacherActivities = const [],
    this.foods = const [],
    this.toilets = const [],
    this.sleeps = const [],
    this.moods = const [],
    this.activityLevels = const [],
    required this.meta,
  });

  factory ParentActivitiesResponseModel.fromJson(Map<String, dynamic> json) {
    final data = json[ApiStrings.data] ?? {};
    final meta = json['meta'] ?? {};

    return ParentActivitiesResponseModel(
      teacherActivities: (data['teacherActivities']['data'] as List?)
              ?.map((e) => TeacherActivityModel.fromJson({
                    'id': e['id'],
                    'attributes': e['attributes'] ?? e,
                  }))
              .toList() ??
          [],
      foods: (data['foods']['data'] as List?)
              ?.map((e) => FoodModel.fromJson({
                    'id': e['id'],
                    'attributes': e['attributes'] ?? e,
                  }))
              .toList() ??
          [],
      toilets: (data['toilets']['data'] as List?)
              ?.map((e) => ToiletModel.fromJson({
                    'id': e['id'],
                    'attributes': e['attributes'] ?? e,
                  }))
              .toList() ??
          [],
      sleeps: (data['sleeps']['data'] as List?)
              ?.map((e) => SleepModel.fromJson({
                    'id': e['id'],
                    'attributes': e['attributes'] ?? e,
                  }))
              .toList() ??
          [],
      moods: (data['moods']['data'] as List?)
              ?.map((e) => MoodModel.fromJson({
                    'id': e['id'],
                    'attributes': e['attributes'] ?? e,
                  }))
              .toList() ??
          [],
      activityLevels: (data['activityLevels']['data'] as List?)
              ?.map((e) => ActivityLevelModel.fromJson({
                    'id': e['id'],
                    'attributes': e['attributes'] ?? e,
                  }))
              .toList() ??
          [],
      meta: ParentActivitiesMetaModel.fromJson(meta),
    );
  }

  /// Get all activities combined and sorted by time
  List<dynamic> getCombinedActivities() {
    final combined = <dynamic>[
      ...teacherActivities,
      ...foods,
      ...toilets,
      ...sleeps,
      ...moods,
      ...activityLevels,
    ];

    // Sort by creation time or activity time
    combined.sort((a, b) {
      DateTime getActivityTime(dynamic activity) {
        if (activity is TeacherActivityModel) {
          // For teacher activities, use the start time if available
          if (activity.startTime.isNotEmpty) {
            try {
              final parts = activity.startTime.split(':');
              final hour = int.parse(parts[0]);
              final minute = int.parse(parts[1]);
              final now = DateTime.now();
              return DateTime(now.year, now.month, now.day, hour, minute);
            } catch (e) {
              return activity.createdAtDate ?? DateTime.now();
            }
          }
          return activity.createdAtDate ?? DateTime.now();
        } else {
          // For other activities, use createdAt
          return (activity.createdAt as DateTime?) ?? DateTime.now();
        }
      }

      return getActivityTime(a).compareTo(getActivityTime(b));
    });

    return combined;
  }

  /// Check if there are any activities
  bool get hasAnyActivities {
    return teacherActivities.isNotEmpty ||
        foods.isNotEmpty ||
        toilets.isNotEmpty ||
        sleeps.isNotEmpty ||
        moods.isNotEmpty ||
        activityLevels.isNotEmpty;
  }

  /// Get total count of all activities
  int get totalActivitiesCount {
    return teacherActivities.length +
        foods.length +
        toilets.length +
        sleeps.length +
        moods.length +
        activityLevels.length;
  }
}

class ParentActivitiesMetaModel {
  final ParentActivitiesCountsModel counts;

  const ParentActivitiesMetaModel({
    required this.counts,
  });

  factory ParentActivitiesMetaModel.fromJson(Map<String, dynamic> json) {
    return ParentActivitiesMetaModel(
      counts: ParentActivitiesCountsModel.fromJson(json['counts'] ?? {}),
    );
  }
}

class ParentActivitiesCountsModel {
  final int teacherActivities;
  final int foods;
  final int toilets;
  final int sleeps;
  final int moods;
  final int activityLevels;
  final int total;

  const ParentActivitiesCountsModel({
    this.teacherActivities = 0,
    this.foods = 0,
    this.toilets = 0,
    this.sleeps = 0,
    this.moods = 0,
    this.activityLevels = 0,
    this.total = 0,
  });

  factory ParentActivitiesCountsModel.fromJson(Map<String, dynamic> json) {
    return ParentActivitiesCountsModel(
      teacherActivities: json['teacherActivities'] ?? 0,
      foods: json['foods'] ?? 0,
      toilets: json['toilets'] ?? 0,
      sleeps: json['sleeps'] ?? 0,
      moods: json['moods'] ?? 0,
      activityLevels: json['activityLevels'] ?? 0,
      total: json['total'] ?? 0,
    );
  }
}
