import 'dart:developer';

import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/shared/shared_models/base_media_model.dart';
import 'package:equatable/equatable.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../shared/data/remote/api_strings.dart';
import '../../../shared/shared_models/base_model.dart';

List<StudentModel> responseToStudentModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final students =
      data.map((student) => StudentModel.fromJson(student)).toList();

  return students;
}

List<StudentModel> responseToSimpleStudentModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final students =
      data.map((student) => StudentModel.fromSimpleJson(student)).toList();

  return students;
}

class StudentModel extends BaseModel {
  final ClassModel? classModel;
  final String? homeAddress;
  final String? motherPhoneNumber;
  final String? parentPhoneNumber;
  final DateTime? birthDate;
  final DateTime? subscriptionDate;
  final bool isActive;
  final String gender;
  final List<SubscriptionModel> subscriptions;
  final num? fees;
  final List<String> pickupPersons;

  const StudentModel({
    super.id,
    super.name,
    super.image,
    super.createdAt,
    this.classModel,
    this.homeAddress,
    this.isActive = true,
    this.motherPhoneNumber,
    this.parentPhoneNumber,
    this.birthDate,
    this.subscriptionDate,
    this.fees,
    this.subscriptions = const [],
    this.pickupPersons = const [],
    this.gender = 'male',
  });

  factory StudentModel.fromSimpleJson(Map<String, dynamic> json) {
    return StudentModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      birthDate: json[ApiStrings.birthDate]?.toString().formatStringToDateTime,
      image:
          json['image'] != null ? BaseMediaModel.fromJson(json['image']) : null,
    );
  }

  factory StudentModel.fromJson(Map<String, dynamic> json) {
    // * -----------------------------------------------

    final attributes =
        json[ApiStrings.attributes] as Map<String, dynamic>? ?? {};

    if (attributes.isEmpty) {
      return const StudentModel();
    }

    // * -----------------------------------------------
    final image = attributes[ApiStrings.image] != null &&
            attributes[ApiStrings.image][ApiStrings.data] != null
        ? BaseMediaModel.fromJson(attributes[ApiStrings.image][ApiStrings.data]
            [ApiStrings.attributes])
        : null;
    //
    // // // * -----------------------------------------------
    final classModel = attributes[ApiStrings.classString] != null &&
            attributes[ApiStrings.classString][ApiStrings.data] != null
        ? ClassModel.fromStudentJson(
            attributes[ApiStrings.classString][ApiStrings.data])
        : null;

    // * -----------------------------------------------

    return StudentModel(
      id: json[ApiStrings.id],
      isActive: attributes[ApiStrings.isActive] ?? true,
      name: attributes[ApiStrings.name] ?? '',
      homeAddress: attributes[ApiStrings.homeAddress] ?? '',
      fees: attributes[ApiStrings.fees],
      image: image,
      classModel: classModel,
      gender: attributes[ApiStrings.gender] ?? 'male',
      motherPhoneNumber: attributes[ApiStrings.motherPhoneNumber] ?? '',
      parentPhoneNumber: attributes[ApiStrings.parentPhoneNumber] ?? '',
      subscriptions: (attributes[ApiStrings.subscriptions] as List? ?? [])
          .map((e) => SubscriptionModel.fromJson(e))
          .toList(),
      birthDate:
          attributes[ApiStrings.birthDate]?.toString().formatStringToDateTime,
      subscriptionDate: attributes[ApiStrings.subscriptionDate]
          ?.toString()
          .formatStringToDateTime,
      pickupPersons: attributes[ApiStrings.pickupPersons] != null
          ? (attributes[ApiStrings.pickupPersons] as List<dynamic>)
              .where((e) => e[ApiStrings.person] != null)
              .map<String>((e) => e[ApiStrings.person])
              .toList()
          : <String>[],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      ApiStrings.name: name,
      ApiStrings.classString: classModel?.id,
      ApiStrings.homeAddress: homeAddress,
      ApiStrings.motherPhoneNumber: motherPhoneNumber,
      ApiStrings.parentPhoneNumber: parentPhoneNumber,
      ApiStrings.birthDate: birthDate?.formatDateToString,
      ApiStrings.subscriptionDate: subscriptionDate?.formatDateToString,
      ApiStrings.isActive: isActive,
      ApiStrings.gender: gender,
      ApiStrings.fees: fees,
      ApiStrings.pickupPersons: pickupPersons
          .map((e) => {
                if (e.isNotEmpty) ApiStrings.person: e,
              })
          .toList(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        image,
        createdAt,
        classModel,
        gender,
        homeAddress,
        motherPhoneNumber,
        parentPhoneNumber,
        birthDate,
        subscriptionDate,
        isActive,
        fees,
      ];

  //   super.id,
  //     super.name,
  //     super.image,
  //     super.createdAt,
  //     this.nursery,
  //     this.classModel,
  //     this.homeAddress,
  //     this.isActive = true,
  //     this.motherPhoneNumber,
  //     this.parentPhoneNumber,
  //     this.birthDate,
  //     this.fees,
  //     this.subscriptions = const [],
  //     this.gender = 'male',
//copyWith method
  StudentModel copyWith({
    int? id,
    String? name,
    BaseMediaModel? image,
    String? createdAt,
    ClassModel? classModel,
    String? homeAddress,
    String? motherPhoneNumber,
    String? parentPhoneNumber,
    DateTime? birthDate,
    DateTime? subscriptionDate,
    bool? isActive,
    num? fees,
    String? gender,
    List<SubscriptionModel>? subscriptions,
    List<String>? pickupPersons,
  }) {
    return StudentModel(
      id: id ?? this.id,
      name: name ?? this.name,
      image: image ?? this.image,
      createdAt: createdAt ?? this.createdAt,
      classModel: classModel ?? this.classModel,
      homeAddress: homeAddress ?? this.homeAddress,
      motherPhoneNumber: motherPhoneNumber ?? this.motherPhoneNumber,
      parentPhoneNumber: parentPhoneNumber ?? this.parentPhoneNumber,
      birthDate: birthDate ?? this.birthDate,
      subscriptionDate: subscriptionDate ?? this.subscriptionDate,
      isActive: isActive ?? this.isActive,
      fees: fees ?? this.fees,
      gender: gender ?? this.gender,
      subscriptions: subscriptions ?? this.subscriptions,
      pickupPersons: pickupPersons ?? this.pickupPersons,
    );
  }
}

class SubscriptionModel extends Equatable {
  final String date;
  final num amount;
  final bool isPaid;
  final BaseMediaModel? paymentScreenshot;
  final String? paymentMethod;
  final bool isApproved;

  const SubscriptionModel({
    this.date = '',
    this.amount = 0,
    this.isPaid = false,
    this.paymentScreenshot,
    this.paymentMethod,
    this.isApproved = false,
  });

  factory SubscriptionModel.fromJson(Map<String, dynamic> attributes) {
    final paymentScreenshotData = attributes[ApiStrings.paymentScreenshot];
    log('asfasfasfa ${attributes}');

    BaseMediaModel? paymentScreenshot;

    if (paymentScreenshotData != null) {
      paymentScreenshot = paymentScreenshotData['data'] != null
          ? BaseMediaModel.fromJson(paymentScreenshotData['data']['attributes'],
              id: attributes['payment_screenshot']['data']['id'])
          : null;
    }

    return SubscriptionModel(
      date: attributes[ApiStrings.date]?.toString() ?? '',
      amount: attributes[ApiStrings.amount] ?? 0,
      isPaid: attributes[ApiStrings.isPaid] ?? false,
      paymentScreenshot: paymentScreenshot,
      paymentMethod: attributes[ApiStrings.paymentMethod],
      isApproved: attributes[ApiStrings.isApproved] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      ApiStrings.date: date,
      ApiStrings.amount: amount,
      ApiStrings.isPaid: isPaid,
      ApiStrings.paymentScreenshot: paymentScreenshot?.toJson(),
      ApiStrings.paymentMethod: paymentMethod,
      ApiStrings.isApproved: isApproved,
    };
  }

  SubscriptionModel copyWith({
    String? date,
    num? amount,
    bool? isPaid,
    BaseMediaModel? paymentScreenshot,
    String? paymentMethod,
    bool? isApproved,
  }) {
    return SubscriptionModel(
      date: date ?? this.date,
      amount: amount ?? this.amount,
      isPaid: isPaid ?? this.isPaid,
      paymentScreenshot: paymentScreenshot ?? this.paymentScreenshot,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      isApproved: isApproved ?? this.isApproved,
    );
  }

  @override
  List<Object?> get props => [
        date,
        amount,
        isPaid,
        paymentScreenshot,
        paymentMethod,
        isApproved,
      ];
}
