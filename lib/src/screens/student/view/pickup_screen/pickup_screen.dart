import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/base_list/base_list.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/consts/app_constants.dart';
import '../../../../shared/widgets/shared_widgets.dart';
import '../student_screen/widgets/add_student.dart';

class PickupScreen extends HookConsumerWidget {
  const PickupScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final getPickupStudentsFuture =
        ref.watch(getActiveStudentsForPersonPickups(context));

    final filteredStudents = useState<List<StudentModel>>([]);

    final searchController = useTextEditingController();

    return Scaffold(
      appBar: MainAppBar(
        title: context.tr.pickups,
        isBackButton: true,
      ),
      body: getPickupStudentsFuture.get(
        data: (data) {
          return HookBuilder(builder: (context) {
            useEffect(() {
              filteredStudents.value = data;
              return () {};
            }, [data]);

            return Column(
              children: [
                BaseTextField(
                  onChanged: (value) {
                    if (value.isEmpty) {
                      filteredStudents.value = data;
                      return;
                    }

                    filteredStudents.value = data
                        .where((student) => student.name
                            .toLowerCase()
                            .contains(value.toLowerCase()))
                        .toList();
                  },
                  hint: context.tr.searchStudent,
                ).paddingAll(AppSpaces.mediumPadding),
                context.smallGap,
                Expanded(
                  child: BaseList(
                    data: filteredStudents.value,
                    separatorGap: const Divider(
                      color: ColorManager.grey,
                    ).paddingSymmetric(horizontal: AppSpaces.largePadding),
                    itemBuilder: (student, index) {
                      return Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: AppSpaces.mediumPadding),
                          child: ExpansionTile(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                            leading: ClipRRect(
                              borderRadius: BorderRadius.circular(
                                  AppRadius.mainContainerRadius),
                              child: BaseCachedImage(student.image?.url ?? '',
                                  height: 40.w,
                                  width: 40.w,
                                  radius: AppRadius.mainContainerRadius,
                                  fit: BoxFit.cover,
                                  errorWidget: const BaseCachedImage(
                                    AppConsts.studentPlaceholder,
                                  )),
                            ),
                            trailing: (const UserModel().isCurrentUserAdmin ||
                                    hasPermission(const UserModel().currentUser,
                                        'addStudents'))
                                ? InkWell(
                                    onTap: () => showAddStudentDialog(context,
                                        ref: ref,
                                        student: student,
                                        navigateWidget: const PickupScreen()),
                                    child: const CircleAvatar(
                                      maxRadius: 17,
                                      backgroundColor: ColorManager.buttonColor,
                                      child: Icon(
                                        Icons.edit,
                                        color: Colors.white,
                                        size: 18,
                                      ),
                                    ),
                                  )
                                : const SizedBox.shrink(),
                            title: Text(
                              '${student.name} (${student.pickupPersons.length} ${context.tr.persons})',
                            ),
                            children: student.pickupPersons.isEmpty
                                ? [
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 4.0),
                                      child: BaseContainer(
                                        padding: AppSpaces.mediumPadding,
                                        margin: AppSpaces.mediumPadding,
                                        child: Text(
                                          context.tr.noPersons,
                                        ),
                                      ),
                                    )
                                  ]
                                : student.pickupPersons
                                    .map((person) => Padding(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 4.0),
                                          child: BaseContainer(
                                            padding: AppSpaces.mediumPadding,
                                            margin: AppSpaces.mediumPadding,
                                            child: Row(
                                              children: [
                                                const Icon(
                                                  CupertinoIcons
                                                      .person_alt_circle,
                                                  color: ColorManager.grey,
                                                ),
                                                context.mediumGap,
                                                Expanded(
                                                  child: Text(
                                                    person,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ))
                                    .toList(),
                          ));
                    },
                  ),
                ),
              ],
            );
          });
        },
      ),
    );
  }
}
