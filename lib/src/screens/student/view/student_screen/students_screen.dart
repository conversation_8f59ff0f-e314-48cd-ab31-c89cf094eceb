import 'package:auto_height_grid_view/auto_height_grid_view.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/student/view/student_screen/widgets/add_student.dart';
import 'package:connectify_app/src/screens/student/view/student_screen/widgets/student_card.dart';
import 'package:connectify_app/src/screens/student/view/student_screen/widgets/students_grid_view.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class StudentsScreen extends HookConsumerWidget {
  const StudentsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final page = useState<int>(1);
    final isLoadingMore = useState<bool>(false);
    final isInitialLoadComplete = useState<bool>(false);
    final students = useState<List<StudentModel>>([]);
    final searchController = useTextEditingController();
    final isSearching = useState<bool>(false);

    final params = (context, page.value, true);

    useEffect(() {
      if (searchController.text.isEmpty) {
        ref.refresh(getStudentDataProviderWithPagination(params));
      }
      return () {};
    }, [page.value, searchController.text]);

    ref.listenPagination<StudentModel>(
      provider: getStudentDataProviderWithPagination(params),
      dataNotifier: students,
      isLoadingNotifier: isLoadingMore,
      isInitialLoadCompleteNotifier: isInitialLoadComplete,
    );

    void handleSearch(String searchText) {
      if (searchText.isEmpty) {
        isSearching.value = false;
        page.value = 1;
        students.value = [];
      } else {
        isSearching.value = true;
        ref
            .read(
                getStudentDataProviderWithFilter((context, searchText)).future)
            .then((filteredStudents) {
          students.value =
              filteredStudents; // Update the list with filtered data
        });
      }
    }

    final isEmptyList = const UserModel().isCurrentUserAdmin
        ? students.value.length == 1
        : students.value.isEmpty;

    final studentCtrl = ref.watch(getActiveStudentsCountProvider(context));

    return WillPopScope(
      onWillPop: () async {
        context.toReplacement(const MainScreen());
        return false;
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(context.tr.students),
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(50),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Container(
                height: 50,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: TextField(
                  controller: searchController,
                  decoration: InputDecoration(
                    hintText: context.tr.searchStudent,
                    prefixIcon: const Icon(Icons.search),
                    border: InputBorder.none,
                  ),
                  onChanged: handleSearch,
                ),
              ),
            ),
          ),
        ),
        body: NotificationListener<ScrollNotification>(
          onNotification: (ScrollNotification scrollInfo) {
            if (!isSearching.value &&
                !isLoadingMore.value &&
                scrollInfo.metrics.pixels ==
                    scrollInfo.metrics.maxScrollExtent) {
              isLoadingMore.value = true;
              page.value += 1;
            }
            return true;
          },
          child: studentCtrl.get(
            data: (activeStudents) {
              StudentController.allActiveStudentsLength.value = activeStudents;

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  context.smallGap,
                  Padding(
                    padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                    child: Text(
                      '${context.tr.activeStudents} ($activeStudents)',
                      style: context.subHeadLine,
                    ),
                  ),
                  Expanded(
                    child: Stack(
                      children: [
                        Builder(builder: (context) {
                          if (!isInitialLoadComplete.value &&
                              !isSearching.value) {
                            return const Center(
                              child: LoadingWidget(),
                            );
                          }

                          return isEmptyList
                              ? const EmptyStudentsList(
                                  navigateWidget: StudentsScreen(),
                                )
                              : AutoHeightGridView(
                                  shrinkWrap: true,
                                  padding: const EdgeInsets.all(
                                      AppSpaces.mediumPadding),
                                  physics:
                                      const AlwaysScrollableScrollPhysics(),
                                  itemCount: students.value.length,
                                  crossAxisCount: 3,
                                  mainAxisSpacing: 15.h,
                                  crossAxisSpacing: 10,
                                  builder: (BuildContext context, int index) {
                                    final student = students.value[index];

                                    if (student.id == 0 &&
                                        (const UserModel().isCurrentUserAdmin ||
                                            hasPermission(
                                                const UserModel().currentUser,
                                                'addStudents'))) {
                                      return const AddStudentDialog(
                                        navigateWidget: StudentsScreen(),
                                      );
                                    }

                                    return WidgetAnimator(
                                      delay: Duration(
                                          milliseconds:
                                              AppConsts.animatedDuration *
                                                  index),
                                      child: StudentCard(
                                        student: student,
                                        navigateWidget: const StudentsScreen(),
                                      ),
                                    );
                                  },
                                );
                        }),
                        if (isLoadingMore.value && !isSearching.value)
                          const Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: LoadingWidget(
                              loadingType: LoadingType.linear,
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
// import 'package:auto_height_grid_view/auto_height_grid_view.dart';
// import 'package:connectify_app/src/screens/auth/models/user_model.dart';
// import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
// import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
// import 'package:connectify_app/src/screens/student/models/student_model.dart';
// import 'package:connectify_app/src/screens/student/view/student_screen/widgets/add_student.dart';
// import 'package:connectify_app/src/screens/student/view/student_screen/widgets/student_card.dart';
// import 'package:connectify_app/src/screens/student/view/student_screen/widgets/students_grid_view.dart';
// import 'package:connectify_app/src/shared/consts/app_constants.dart';
// import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
// import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
// import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:hooks_riverpod/hooks_riverpod.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// class StudentsScreen extends HookConsumerWidget {
//   const StudentsScreen({super.key});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final page = useState<int>(1);
//     final isLoadingMore = useState<bool>(false);
//     final isInitialLoadComplete = useState<bool>(false);
//     final students = useState<List<StudentModel>>([]);
//
//     final params = (context, page.value, true);
//
//     useEffect(() {
//       ref.refresh(getStudentDataProviderWithPagination(params));
//       return () {};
//     }, [page.value]);
//
//     ref.listenPagination<StudentModel>(
//       provider: getStudentDataProviderWithPagination(params),
//       dataNotifier: students,
//       isLoadingNotifier: isLoadingMore,
//       isInitialLoadCompleteNotifier: isInitialLoadComplete,
//     );
//
//     final isEmptyList = const UserModel().isAdmin
//         ? students.value.length == 1
//         : students.value.isEmpty;
//
//     final studentCtrl = ref.watch(getActiveStudentsCountProvider(context));
//
//     return WillPopScope(
//       onWillPop: () async {
//         context.toReplacement(const MainScreen());
//         return false;
//       },
//       child: Scaffold(
//         appBar: AppBar(title: Text(context.tr.students)),
//         body: NotificationListener<ScrollNotification>(
//           onNotification: (ScrollNotification scrollInfo) {
//             if (!isLoadingMore.value &&
//                 scrollInfo.metrics.pixels ==
//                     scrollInfo.metrics.maxScrollExtent) {
//               isLoadingMore.value = true;
//               page.value += 1;
//             }
//             return true;
//           },
//           child: studentCtrl.get(
//             data: (activeStudents) {
//               StudentController.allActiveStudentsLength.value = activeStudents;
//
//               return Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   context.smallGap,
//                   Padding(
//                     padding: const EdgeInsets.all(AppSpaces.mediumPadding),
//                     child: Text(
//                       '${context.tr.activeStudents} ($activeStudents)',
//                       style: context.subHeadLine,
//                     ),
//                   ),
//                   Expanded(
//                     child: Stack(
//                       children: [
//                         Builder(builder: (context) {
//                           if (!isInitialLoadComplete.value) {
//                             return const Center(
//                               child: LoadingWidget(),
//                             );
//                           }
//
//                           return isEmptyList
//                               ? const EmptyStudentsList(
//                                   navigateWidget: StudentsScreen(),
//                                 )
//                               : AutoHeightGridView(
//                                   shrinkWrap: true,
//                                   padding: const EdgeInsets.all(
//                                       AppSpaces.mediumPadding),
//                                   physics:
//                                       const AlwaysScrollableScrollPhysics(),
//                                   itemCount: students.value.length,
//                                   crossAxisCount: 3,
//                                   mainAxisSpacing: 15.h,
//                                   crossAxisSpacing: 10,
//                                   builder: (BuildContext context, int index) {
//                                     final student = students.value[index];
//
//                                     if (student.id == 0 &&
//                                         const UserModel().isAdmin) {
//                                       return const AddStudentDialog(
//                                         navigateWidget: StudentsScreen(),
//                                       );
//                                     }
//
//                                     return WidgetAnimator(
//                                       delay: Duration(
//                                           milliseconds:
//                                               AppConsts.animatedDuration *
//                                                   index),
//                                       child: StudentCard(
//                                         student: student,
//                                         navigateWidget: const StudentsScreen(),
//                                       ),
//                                     );
//                                   },
//                                 );
//                         }),
//                         if (isLoadingMore.value)
//                           const Positioned(
//                             bottom: 0,
//                             left: 0,
//                             right: 0,
//                             child: LoadingWidget(
//                               loadingType: LoadingType.linear,
//                             ),
//                           ),
//                       ],
//                     ),
//                   ),
//                 ],
//               );
//             },
//           ),
//         ),
//       ),
//     );
//   }
// }
// // import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
// // import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
// // import 'package:connectify_app/src/screens/student/view/student_screen/widgets/students_grid_view.dart';
// // import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
// // import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
// // import 'package:flutter/material.dart';
// // import 'package:flutter_riverpod/flutter_riverpod.dart';
// // import 'package:xr_helper/xr_helper.dart';
// //
// // import '../../../home/<USER>/main_screen.dart';
// //
// // class StudentsScreen extends ConsumerWidget {
// //   const StudentsScreen({super.key});
// //
// //   @override
// //   Widget build(BuildContext context, WidgetRef ref) {
// //     final students = ref.watch(getAllStudentsProvider(context));
// //     return WillPopScope(
// //       onWillPop: () async {
// //         context.toReplacement(const MainScreen());
// //         return false;
// //       },
// //       child: SafeArea(
// //         child: Scaffold(
// //           appBar: MainAppBar(
// //             title: context.tr.students,
// //             isBackButton: true,
// //           ),
// //           body: students.get(data: (student) {
// //             return StudentGridView(
// //               navigateWidget: const StudentsScreen(),
// //               students: student,
// //             );
// //           }),
// //         ),
// //       ),
// //     );
// //   }
// // }
