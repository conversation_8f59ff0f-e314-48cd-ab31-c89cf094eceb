import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/emergency/view/widgets/emergency_card_widget.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class StudentDetailsExpansionTile extends StatelessWidget {
  final StudentModel student;

  const StudentDetailsExpansionTile({
    super.key,
    required this.student,
  });

  @override
  Widget build(BuildContext context) {
    final isFatherPhone = student.parentPhoneNumber?.isEmpty ?? true;

    return BaseContainer(
      padding: 0,
      boxShadow: ConstantsWidgets.boxShadow,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
        child: ExpansionTile(
          shape: InputBorder.none,
          title: Row(
            children: [
              const Icon(
                Icons.person_outline,
                color: ColorManager.primaryColor,
                size: 20,
              ),
              context.smallGap,
              Text(
                context.tr.studentDetails,
                style: context.blueHint.copyWith(fontSize: 16),
              ),
            ],
          ),
          tilePadding: const EdgeInsets.symmetric(
            horizontal: AppSpaces.mediumPadding,
          ),
          children: [
            Container(
              padding: const EdgeInsets.all(AppSpaces.mediumPadding),
              child: Column(
                children: [
                  EmergencyTitleAndSubTitleWithIcon(
                    titleAndSubTitle: (
                      context.tr.className,
                      student.classModel?.name ?? ''
                    ),
                  ),
                  context.largeGap,
                  EmergencyTitleAndSubTitleWithIcon(
                    titleAndSubTitle: (
                      context.tr.mother,
                      student.motherPhoneNumber ?? ''
                    ),
                    iconPath: Assets.svgPhoneNumber,
                  ),
                  context.largeGap,
                  if (!isFatherPhone) ...[
                    EmergencyTitleAndSubTitleWithIcon(
                      titleAndSubTitle: (
                        context.tr.father,
                        student.parentPhoneNumber ?? ''
                      ),
                      iconPath: Assets.svgPhoneNumber,
                    ),
                    context.largeGap,
                  ],
                  EmergencyTitleAndSubTitleWithIcon(
                    titleAndSubTitle: (
                      context.tr.address,
                      student.homeAddress ?? ''
                    ),
                    iconPath: Assets.svgLocation,
                    isPhone: false,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
