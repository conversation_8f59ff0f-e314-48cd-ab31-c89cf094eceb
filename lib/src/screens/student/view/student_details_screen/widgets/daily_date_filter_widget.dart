import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:xr_helper/xr_helper.dart';

class DailyDateFilterWidget extends StatelessWidget {
  final Function() onNext;
  final Function() onPrevious;
  final DateTime date;
  final bool canGoNext;
  final bool canGoPrevious;

  const DailyDateFilterWidget({
    super.key,
    required this.onNext,
    required this.onPrevious,
    required this.date,
    this.canGoNext = true,
    this.canGoPrevious = true,
  });

  @override
  Widget build(BuildContext context) {
    final isToday = _isToday(date);
    final formattedDate = _formatDate(date, context);

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpaces.mediumPadding,
        vertical: AppSpaces.smallPadding,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Previous button
          IconButton(
            onPressed: canGoPrevious ? onPrevious : null,
            icon: Icon(
              Icons.arrow_back_ios_rounded,
              color: canGoPrevious 
                  ? ColorManager.primaryColor 
                  : ColorManager.grey,
            ),
          ),
          
          context.mediumGap,
          
          // Date display with today indicator
          Expanded(
            child: Column(
              children: [
                if (isToday) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSpaces.smallPadding,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: ColorManager.primaryColor,
                      borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                    ),
                    child: Text(
                      context.tr.today,
                      style: context.hint.copyWith(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  context.xSmallGap,
                ],
                Text(
                  formattedDate,
                  style: context.greyLabelLarge.copyWith(
                    color: isToday 
                        ? ColorManager.primaryColor 
                        : ColorManager.darkGrey,
                    fontWeight: isToday ? FontWeight.w600 : FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          
          context.mediumGap,
          
          // Next button
          IconButton(
            onPressed: canGoNext ? onNext : null,
            icon: Icon(
              Icons.arrow_forward_ios_rounded,
              color: canGoNext 
                  ? ColorManager.primaryColor 
                  : ColorManager.grey,
            ),
          ),
        ],
      ),
    );
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
           date.month == now.month &&
           date.day == now.day;
  }

  String _formatDate(DateTime date, BuildContext context) {
    // Format: "Thursday, July 31, 2025"
    final dayName = DateFormat('EEEE', Localizations.localeOf(context).toString()).format(date);
    final monthName = DateFormat('MMMM', Localizations.localeOf(context).toString()).format(date);
    final day = date.day;
    final year = date.year;
    
    return '$dayName, $monthName $day, $year';
  }
}
