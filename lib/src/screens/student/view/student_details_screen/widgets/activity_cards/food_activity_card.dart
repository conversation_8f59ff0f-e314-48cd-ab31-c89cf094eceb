import 'package:connectify_app/src/screens/food/model/food_model.dart';
import 'package:connectify_app/src/screens/food/view/widgets/add_food_dialog.dart';
import 'package:connectify_app/src/screens/food/view/widgets/add_food_widgets.dart';
import 'package:connectify_app/src/screens/meals/controller/meal_controller.dart';
import 'package:connectify_app/src/screens/meals/model/meal_model.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:collection/collection.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../toilet/view/widgets/add_toilet_dialog.dart';

class FoodActivityCard extends HookConsumerWidget {
  final List<FoodModel> foods;
  final StudentModel student;
  final DateTime selectedDate;
  final VoidCallback? onRefresh;

  const FoodActivityCard({
    super.key,
    required this.foods,
    required this.student,
    required this.selectedDate,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isToday = _isToday(selectedDate);

    // Fetch meals data
    final mealsCtrl = ref.watch(getMealDataProvider(context));
    final mealsData = mealsCtrl.when(
      data: (data) => data,
      loading: () => <MealModel>[],
      error: (error, stack) => <MealModel>[],
    );

    // Get current day name
    final currentDay = _getCurrentDayName(selectedDate);

    // Process foods with meal names
    final processedFoods = foods.map((food) {
      // Find the corresponding meal from mealsData
      final correspondingMeal = mealsData.firstWhereOrNull(
        (meal) =>
            meal.mealType == food.mealType &&
            meal.day?.name.toLowerCase() == currentDay.toLowerCase(),
      );

      return FoodModel(
        id: food.id,
        mealType: food.mealType,
        mealAmount: food.mealAmount,
        createdAt: food.createdAt,
        student: food.student,
        nursery: food.nursery,
        teacher: food.teacher,
        meal: correspondingMeal?.name?.isNotEmpty == true
            ? correspondingMeal!.name!
            : '-',
      );
    }).toList();

    return BaseContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon and title
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: ColorManager.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                ),
                child: const Text(
                  '🍽️',
                  style: TextStyle(fontSize: 20),
                ),
              ),
              context.mediumGap,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.tr.meals,
                      style: context.boldTitle.copyWith(
                        color: ColorManager.primaryColor,
                      ),
                    ),
                    if (processedFoods.isNotEmpty)
                      Text(
                        '${processedFoods.length} ${context.tr.meals.toLowerCase()}',
                        style: context.hint.copyWith(fontSize: 12),
                      ),
                  ],
                ),
              ),
            ],
          ),

          context.mediumGap,
          // Always show 3 meal types
          _buildMealTypeCard(context, MealTypes.breakFast, processedFoods),
          context.smallGap,
          _buildMealTypeCard(context, MealTypes.snack, processedFoods),
          context.smallGap,
          _buildMealTypeCard(context, MealTypes.lunch, processedFoods),
        ],
      ),
    );
  }

  Widget _buildMealTypeCard(BuildContext context, MealTypes mealType,
      List<FoodModel> processedFoods) {
    final isToday = _isToday(selectedDate);
    final existingMeal =
        processedFoods.where((food) => food.mealType == mealType).firstOrNull;

    return Container(
      padding: const EdgeInsets.all(AppSpaces.mediumPadding),
      decoration: BoxDecoration(
        color: ColorManager.fieldColor.withOpacity(0.3),
        borderRadius: BorderRadius.circular(AppRadius.smallRadius),
        border: Border.all(color: ColorManager.grey.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          // Meal type icon
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: _getMealTypeColor(mealType).withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppRadius.smallRadius),
            ),
            child: Text(
              _getMealTypeEmoji(mealType),
              style: const TextStyle(fontSize: 16),
            ),
          ),
          context.smallGap,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getMealTypeName(context, mealType),
                  style: context.subTitle.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (existingMeal != null)
                  Text(
                    existingMeal.meal ?? '-',
                    style: context.hint.copyWith(fontSize: 12),
                  ),
              ],
            ),
          ),
          // Action button
          if (existingMeal != null) ...[
            // Amount indicator
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppSpaces.smallPadding,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: _getMealAmountColor(existingMeal.mealAmount),
                borderRadius: BorderRadius.circular(AppRadius.smallRadius),
              ),
              child: Text(
                _getMealAmountName(context, existingMeal.mealAmount),
                style: context.hint.copyWith(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            context.xSmallGap,
            if (isToday)
              IconButton(
                onPressed: () => _showEditFoodDialog(context, existingMeal),
                icon: const Icon(
                  CupertinoIcons.square_pencil,
                  color: ColorManager.primaryColor,
                  size: 18,
                ),
              ),
          ] else if (isToday) ...[
            IconButton(
              onPressed: () => _showAddFoodDialog(context, mealType),
              icon: const Icon(
                Icons.add_circle_outline,
                color: ColorManager.primaryColor,
                size: 20,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFoodItem(BuildContext context, FoodModel food) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpaces.smallPadding),
      padding: const EdgeInsets.all(AppSpaces.smallPadding),
      decoration: BoxDecoration(
        color: ColorManager.fieldColor.withOpacity(0.3),
        borderRadius: BorderRadius.circular(AppRadius.smallRadius),
        border: Border.all(color: ColorManager.grey.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          // Meal type icon
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: _getMealTypeColor(food.mealType).withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppRadius.smallRadius),
            ),
            child: Text(
              _getMealTypeEmoji(food.mealType),
              style: const TextStyle(fontSize: 16),
            ),
          ),
          context.smallGap,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getMealTypeName(context, food.mealType),
                  style: context.subTitle.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  food.meal ?? '-',
                  style: context.hint.copyWith(fontSize: 12),
                ),
              ],
            ),
          ),
          // Amount indicator
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppSpaces.smallPadding,
              vertical: 4,
            ),
            decoration: BoxDecoration(
              color: _getMealAmountColor(food.mealAmount),
              borderRadius: BorderRadius.circular(AppRadius.smallRadius),
            ),
            child: Text(
              _getMealAmountName(context, food.mealAmount),
              style: context.hint.copyWith(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  bool _canAddMoreMeals(List<FoodModel> processedFoods) {
    // Check if all meal types are already added (breakfast, snack, lunch)
    final mealTypes = processedFoods.map((f) => f.mealType).toSet();
    return mealTypes.length < 3; // Can add up to 3 meals per day
  }

  String _getCurrentDayName(DateTime date) {
    const days = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday'
    ];
    return days[date.weekday - 1];
  }

  void _showAddFoodDialog(BuildContext context, [MealTypes? mealType]) {
    showAddFoodDialog(context, student: student, mealType: mealType);
  }

  void _showEditFoodDialog(BuildContext context, FoodModel food) {
    showAddFoodDialog(context, student: student, existingFood: food);
  }

  String _getMealTypeEmoji(MealTypes? mealType) {
    switch (mealType) {
      case MealTypes.breakFast:
        return '🥞';
      case MealTypes.snack:
        return '🍪';
      case MealTypes.lunch:
        return '🍽️';
      default:
        return '🍽️';
    }
  }

  String _getMealTypeName(BuildContext context, MealTypes? mealType) {
    switch (mealType) {
      case MealTypes.breakFast:
        return context.tr.breakfast;
      case MealTypes.snack:
        return context.tr.snack;
      case MealTypes.lunch:
        return context.tr.lunch;
      default:
        return context.tr.breakfast;
    }
  }

  String _getMealAmountName(BuildContext context, MealAmount? mealAmount) {
    switch (mealAmount) {
      case MealAmount.all:
        return context.tr.all;
      case MealAmount.more:
        return context.tr.more;
      case MealAmount.some:
        return context.tr.some;
      case MealAmount.none:
        return context.tr.none;
      default:
        return context.tr.some;
    }
  }

  Color _getMealTypeColor(MealTypes? mealType) {
    switch (mealType) {
      case MealTypes.breakFast:
        return Colors.orange;
      case MealTypes.snack:
        return Colors.purple;
      case MealTypes.lunch:
        return Colors.green;
      default:
        return ColorManager.primaryColor;
    }
  }

  Color _getMealAmountColor(MealAmount? mealAmount) {
    switch (mealAmount) {
      case MealAmount.all:
        return Colors.green;
      case MealAmount.more:
        return Colors.blue;
      case MealAmount.some:
        return Colors.orange;
      case MealAmount.none:
        return Colors.red;
      default:
        return ColorManager.grey;
    }
  }
}
