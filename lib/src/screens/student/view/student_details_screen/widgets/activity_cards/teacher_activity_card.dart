import 'package:connectify_app/src/screens/activities/view/teacher_activty/teacher_activty_add_activity/view/teacher_activity_screen.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../activities/models/teacher_activity_model.dart';

class TeacherActivityCard extends HookWidget {
  final List<TeacherActivityModel> activities;
  final StudentModel student;
  final DateTime selectedDate;
  final VoidCallback? onRefresh;

  const TeacherActivityCard({
    super.key,
    required this.activities,
    required this.student,
    required this.selectedDate,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return BaseContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon and title
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: ColorManager.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                ),
                child: const Text(
                  '🎯',
                  style: TextStyle(fontSize: 20),
                ),
              ),
              context.mediumGap,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.tr.activities,
                      style: context.boldTitle.copyWith(
                        color: ColorManager.primaryColor,
                      ),
                    ),
                    if (activities.isNotEmpty)
                      Text(
                        '${activities.length} ${context.tr.activities.toLowerCase()}',
                        style: context.hint.copyWith(fontSize: 12),
                      ),
                  ],
                ),
              ),
              TextButton.icon(
                onPressed: () => _navigateToAssignActivity(context),
                icon: const Icon(
                  Icons.assignment_add,
                  size: 16,
                  color: ColorManager.primaryColor,
                ),
                label: Text(
                  context.tr.assignActivity,
                  style: context.hint.copyWith(
                    color: ColorManager.primaryColor,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),

          if (activities.isEmpty) ...[
            context.mediumGap,
            Center(
              child: Column(
                children: [
                  const Icon(
                    Icons.assignment_outlined,
                    size: 40,
                    color: ColorManager.grey,
                  ),
                  context.smallGap,
                  TextButton(
                    onPressed: () => _navigateToAssignActivity(context),
                    child: Text(context.tr.assignActivity),
                  ),
                ],
              ),
            ),
          ] else ...[
            context.mediumGap,
            ...activities
                .map((activity) => _buildActivityItem(context, activity)),
          ],
        ],
      ),
    );
  }

  Widget _buildActivityItem(
      BuildContext context, TeacherActivityModel activity) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpaces.smallPadding),
      padding: const EdgeInsets.all(AppSpaces.mediumPadding),
      decoration: BoxDecoration(
        color: ColorManager.fieldColor.withOpacity(0.3),
        borderRadius: BorderRadius.circular(AppRadius.smallRadius),
        border: Border.all(color: ColorManager.grey.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // Activity image or icon
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: ColorManager.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                ),
                child: activity.activity?.image?.url != null
                    ? ClipRRect(
                        borderRadius:
                            BorderRadius.circular(AppRadius.smallRadius),
                        child: BaseCachedImage(
                          activity.activity!.image!.url!,
                          fit: BoxFit.cover,
                        ),
                      )
                    : const Icon(
                        Icons.assignment,
                        color: ColorManager.primaryColor,
                        size: 20,
                      ),
              ),
              context.smallGap,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      activity.activity?.name ?? context.tr.activity,
                      style: context.subTitle.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (activity.activity?.description.isNotEmpty == true) ...[
                      context.xSmallGap,
                      Text(
                        activity.activity!.description!,
                        style: context.hint.copyWith(fontSize: 12),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
              // Time indicator
              if (activity.startTime.isNotEmpty && activity.endTime.isNotEmpty)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpaces.smallPadding,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: ColorManager.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                  ),
                  child: Text(
                    '${activity.startTime} - ${activity.endTime}',
                    style: context.hint.copyWith(
                      color: ColorManager.primaryColor,
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ),

          // Activity type indicator
          if (activity.isWeekly) ...[
            context.smallGap,
            Row(
              children: [
                const Icon(
                  Icons.repeat,
                  size: 14,
                  color: Colors.blue,
                ),
                context.xSmallGap,
                Text(
                  context.tr.weekly,
                  style: context.hint.copyWith(
                    fontSize: 11,
                    color: Colors.blue,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (activity.day.isNotEmpty) ...[
                  Text(
                    ' • ${activity.day}',
                    style: context.hint.copyWith(
                      fontSize: 11,
                      color: Colors.blue,
                    ),
                  ),
                ],
              ],
            ),
          ],

          // Teacher info
          if (activity.teacher?.name.isNotEmpty == true) ...[
            context.smallGap,
            Row(
              children: [
                const Icon(
                  Icons.person_outline,
                  size: 14,
                  color: ColorManager.grey,
                ),
                context.xSmallGap,
                Text(
                  activity.teacher!.name!,
                  style: context.hint.copyWith(
                    fontSize: 11,
                    color: ColorManager.grey,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  void _navigateToAssignActivity(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TeacherActivitiesScreen(),
      ),
    ).then((_) {
      // Refresh activities when returning from assign screen
      if (onRefresh != null) {
        onRefresh!();
      }
    });
  }
}
