import 'package:connectify_app/src/screens/mood/model/mood_model.dart';
import 'package:connectify_app/src/screens/mood/view/widgets/add_mood_dialog.dart';
import 'package:connectify_app/src/screens/mood/view/widgets/add_mood_widgets.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

class MoodActivityCard extends HookWidget {
  final List<MoodModel> moods;
  final StudentModel student;
  final DateTime selectedDate;
  final VoidCallback? onRefresh;

  const MoodActivityCard({
    super.key,
    required this.moods,
    required this.student,
    required this.selectedDate,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    final isToday = _isToday(selectedDate);
    final latestMood = moods.isNotEmpty ? moods.first : null;

    return BaseContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon and title
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: ColorManager.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                ),
                child: const Text(
                  '😊',
                  style: TextStyle(fontSize: 20),
                ),
              ),
              context.mediumGap,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.tr.mood,
                      style: context.boldTitle.copyWith(
                        color: ColorManager.primaryColor,
                      ),
                    ),
                    if (latestMood != null)
                      Text(
                        latestMood.getMoodContextLang(context),
                        style: context.hint.copyWith(fontSize: 12),
                      ),
                  ],
                ),
              ),
            ],
          ),

          if (moods.isEmpty) ...[
            context.mediumGap,
            const Center(
              child: Column(
                children: [
                  Icon(
                    Icons.sentiment_neutral_outlined,
                    size: 40,
                    color: ColorManager.grey,
                  ),
                ],
              ),
            ),
          ] else ...[
            context.mediumGap,
            _buildMoodItem(context, latestMood!),
          ],

          // Add button for today if no mood yet (only one mood per day)
          if (isToday && moods.isEmpty) ...[
            context.mediumGap,
            AddRectangleWidget(
              height: 45.h,
              title: context.tr.addMood,
              onTap: () => _showAddMoodDialog(context),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMoodItem(BuildContext context, MoodModel mood) {
    final isToday = _isToday(selectedDate);
    return Container(
      padding: const EdgeInsets.all(AppSpaces.mediumPadding),
      decoration: BoxDecoration(
        color: _getMoodColor(mood.mood).withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppRadius.smallRadius),
        border: Border.all(color: _getMoodColor(mood.mood).withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // Mood emoji
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _getMoodColor(mood.mood).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                ),
                child: Text(
                  _getMoodEmoji(mood.mood),
                  style: const TextStyle(fontSize: 24),
                ),
              ),
              context.mediumGap,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      mood.getMoodContextLang(context),
                      style: context.subTitle.copyWith(
                        fontWeight: FontWeight.w600,
                        color: _getMoodColor(mood.mood),
                      ),
                    ),
                    if (mood.note?.isNotEmpty == true) ...[
                      context.xSmallGap,
                      Text(
                        mood.note!,
                        style: context.hint.copyWith(fontSize: 12),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
              // Edit button for today's mood
              if (isToday) ...[
                context.xSmallGap,
                IconButton(
                  onPressed: () => _showEditMoodDialog(context, mood),
                  icon: const Icon(
                    CupertinoIcons.square_pencil,
                    color: ColorManager.primaryColor,
                    size: 18,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  void _showAddMoodDialog(BuildContext context) {
    showAddMoodDialog(context, student: student);
  }

  void _showEditMoodDialog(BuildContext context, MoodModel mood) {
    showAddMoodDialog(context, student: student, existingMood: mood);
  }

  String _getMoodEmoji(MoodTypes? mood) {
    switch (mood) {
      case MoodTypes.angry:
        return '😠';
      case MoodTypes.calm:
        return '😌';
      case MoodTypes.excited:
        return '🤩';
      case MoodTypes.happy:
        return '😊';
      case MoodTypes.sad:
        return '😢';
      case MoodTypes.sleepy:
        return '😴';
      case MoodTypes.unwell:
        return '🤒';
      case MoodTypes.worried:
        return '😟';
      default:
        return '😊';
    }
  }

  Color _getMoodColor(MoodTypes? mood) {
    switch (mood) {
      case MoodTypes.angry:
        return Colors.red;
      case MoodTypes.calm:
        return Colors.blue;
      case MoodTypes.excited:
        return Colors.orange;
      case MoodTypes.happy:
        return Colors.green;
      case MoodTypes.sad:
        return Colors.indigo;
      case MoodTypes.sleepy:
        return Colors.purple;
      case MoodTypes.unwell:
        return Colors.red.shade300;
      case MoodTypes.worried:
        return Colors.amber;
      default:
        return ColorManager.primaryColor;
    }
  }
}
