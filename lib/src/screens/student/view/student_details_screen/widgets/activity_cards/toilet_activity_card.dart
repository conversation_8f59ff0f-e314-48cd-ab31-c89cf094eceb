import 'package:connectify_app/src/screens/toilet/model/toilet_model.dart';
import 'package:connectify_app/src/screens/toilet/view/widgets/add_toilet_dialog.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

class ToiletActivityCard extends HookWidget {
  final List<ToiletModel> toilets;
  final StudentModel student;
  final DateTime selectedDate;
  final VoidCallback? onRefresh;

  const ToiletActivityCard({
    super.key,
    required this.toilets,
    required this.student,
    required this.selectedDate,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    final isToday = _isToday(selectedDate);

    return BaseContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon and title
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: ColorManager.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                ),
                child: const Text(
                  '🚽',
                  style: TextStyle(fontSize: 20),
                ),
              ),
              context.mediumGap,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.tr.toilet,
                      style: context.boldTitle.copyWith(
                        color: ColorManager.primaryColor,
                      ),
                    ),
                    if (toilets.isNotEmpty)
                      Text(
                        '${toilets.length} ${context.tr.toilet.toLowerCase()}',
                        style: context.hint.copyWith(fontSize: 12),
                      ),
                  ],
                ),
              ),
            ],
          ),

          if (toilets.isEmpty) ...[
            context.mediumGap,
            Center(
              child: Column(
                children: [
                  const Icon(
                    Icons.wc_outlined,
                    size: 40,
                    color: ColorManager.grey,
                  ),
                  if (isToday) ...[
                    context.smallGap,
                    AddRectangleWidget(
                      height: 45.h,
                      title: context.tr.addToilet,
                      onTap: () => _showAddToiletDialog(context),
                    ),
                  ],
                ],
              ),
            ),
          ] else ...[
            context.mediumGap,
            ...toilets.map((toilet) => _buildToiletItem(context, toilet)),

            // Add button for today (can add multiple times per day)
            if (isToday) ...[
              context.mediumGap,
              AddRectangleWidget(
                title: context.tr.addToilet,
                onTap: () => _showAddToiletDialog(context),
              ),
            ],
          ],
        ],
      ),
    );
  }

  Widget _buildToiletItem(BuildContext context, ToiletModel toilet) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpaces.smallPadding),
      padding: const EdgeInsets.all(AppSpaces.smallPadding),
      decoration: BoxDecoration(
        color: ColorManager.fieldColor.withOpacity(0.3),
        borderRadius: BorderRadius.circular(AppRadius.smallRadius),
        border: Border.all(color: ColorManager.grey.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          // Toilet type icon
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: _getToiletTypeColor(toilet.toiletType).withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppRadius.smallRadius),
            ),
            child: Text(
              _getToiletTypeEmoji(toilet.toiletType),
              style: const TextStyle(fontSize: 16),
            ),
          ),
          context.smallGap,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getToiletTypeName(context, toilet.toiletType),
                  style: context.subTitle.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  _getToiletWayName(context, toilet.toiletWay),
                  style: context.hint.copyWith(fontSize: 12),
                ),
              ],
            ),
          ),
          // Edit button for today's toilet
          if (_isToday(selectedDate)) ...[
            IconButton(
              onPressed: () => _showEditToiletDialog(context, toilet),
              icon: const Icon(
                CupertinoIcons.square_pencil,
                color: ColorManager.primaryColor,
                size: 18,
              ),
            ),
          ],
          // Way indicator
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppSpaces.smallPadding,
              vertical: 4,
            ),
            decoration: BoxDecoration(
              color: _getToiletWayColor(toilet.toiletWay),
              borderRadius: BorderRadius.circular(AppRadius.smallRadius),
            ),
            child: Text(
              _getToiletWayName(context, toilet.toiletWay),
              style: context.hint.copyWith(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  void _showAddToiletDialog(BuildContext context) {
    showAddToiletDialog(context, student: student);
  }

  void _showEditToiletDialog(BuildContext context, ToiletModel toilet) {
    showAddToiletDialog(context, student: student, existingToilet: toilet);
  }

  String _getToiletTypeEmoji(ToiletType? toiletType) {
    switch (toiletType) {
      case ToiletType.urine:
        return '💧';
      case ToiletType.stool:
        return '💩';
      default:
        return '🚽';
    }
  }

  String _getToiletTypeName(BuildContext context, ToiletType? toiletType) {
    switch (toiletType) {
      case ToiletType.urine:
        return context.tr.urine;
      case ToiletType.stool:
        return context.tr.stool;
      default:
        return context.tr.urine;
    }
  }

  String _getToiletWayName(BuildContext context, ToiletWay? toiletWay) {
    switch (toiletWay) {
      case ToiletWay.diaper:
        return context.tr.diaper;
      case ToiletWay.clothes:
        return context.tr.clothes;
      case ToiletWay.toilet:
        return context.tr.toiletWay;
      default:
        return context.tr.diaper;
    }
  }

  Color _getToiletTypeColor(ToiletType? toiletType) {
    switch (toiletType) {
      case ToiletType.urine:
        return Colors.blue;
      case ToiletType.stool:
        return Colors.brown;
      default:
        return ColorManager.primaryColor;
    }
  }

  Color _getToiletWayColor(ToiletWay? toiletWay) {
    switch (toiletWay) {
      case ToiletWay.diaper:
        return Colors.orange;
      case ToiletWay.clothes:
        return Colors.red;
      case ToiletWay.toilet:
        return Colors.green;
      default:
        return ColorManager.grey;
    }
  }
}
