import 'package:connectify_app/src/screens/supplies/controller/teacher_supply_controller.dart';
import 'package:connectify_app/src/screens/supplies/model/teacher_supply_model.dart';
import 'package:connectify_app/src/screens/supplies/view/teacher_supplies/widgets/assign_supply_dialog.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/dialogs/base_delete_dialog.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../student_details_screen.dart';

class SuppliesActivityCard extends HookConsumerWidget {
  final List<TeacherSupplyModel> supplies;
  final StudentModel student;
  final DateTime selectedDate;
  final VoidCallback? onRefresh;

  const SuppliesActivityCard({
    super.key,
    required this.supplies,
    required this.student,
    required this.selectedDate,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isToday = _isToday(selectedDate);

    return BaseContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon and title
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: ColorManager.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                ),
                child: const Text(
                  '📦',
                  style: TextStyle(fontSize: 20),
                ),
              ),
              context.mediumGap,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.tr.supplies,
                      style: context.boldTitle.copyWith(
                        color: ColorManager.primaryColor,
                      ),
                    ),
                    if (supplies.isNotEmpty)
                      Text(
                        '${supplies.length} ${context.tr.supplies.toLowerCase()}',
                        style: context.hint.copyWith(fontSize: 12),
                      ),
                  ],
                ),
              ),
            ],
          ),

          if (supplies.isEmpty) ...[
            context.mediumGap,
            Center(
              child: Column(
                children: [
                  const Icon(
                    Icons.inventory_2_outlined,
                    size: 40,
                    color: ColorManager.grey,
                  ),
                  context.smallGap,
                  AddRectangleWidget(
                    height: 45.h,
                    title: context.tr.assignSupplyToStudent,
                    onTap: () => _showAssignSupplyDialog(context),
                  ),
                ],
              ),
            ),
          ] else ...[
            context.mediumGap,
            ...supplies.map((supply) => _buildSupplyItem(context, ref, supply)),

            // Add button for today (can add multiple times per day)
            if (isToday) ...[
              context.mediumGap,
              AddRectangleWidget(
                height: 45.h,
                title: context.tr.assignSupplyToStudent,
                onTap: () => _showAssignSupplyDialog(context),
              ),
            ],
          ],
        ],
      ),
    );
  }

  Widget _buildSupplyItem(
      BuildContext context, WidgetRef ref, TeacherSupplyModel supply) {
    final isToday = _isToday(selectedDate);
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpaces.smallPadding),
      padding: const EdgeInsets.all(AppSpaces.smallPadding),
      decoration: BoxDecoration(
        color: ColorManager.fieldColor.withOpacity(0.3),
        borderRadius: BorderRadius.circular(AppRadius.smallRadius),
        border: Border.all(color: ColorManager.grey.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // Supply icon
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.teal.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                ),
                child: const Icon(
                  Icons.inventory_2,
                  color: Colors.teal,
                  size: 20,
                ),
              ),
              context.smallGap,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.tr.supplies,
                      style: context.subTitle.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (supply.teacher?.name?.isNotEmpty == true) ...[
                      context.xSmallGap,
                      Text(
                        '${context.tr.teacher}: ${supply.teacher!.name}',
                        style: context.hint.copyWith(fontSize: 12),
                      ),
                    ],
                  ],
                ),
              ),
              // Edit button for today's supply
              if (isToday) ...[
                IconButton(
                  onPressed: () => _showEditSupplyDialog(context, supply),
                  icon: const Icon(
                    CupertinoIcons.square_pencil,
                    color: ColorManager.primaryColor,
                    size: 18,
                  ),
                ),
              ],
              // Delete button
              if (isToday) ...[
                IconButton(
                  onPressed: () =>
                      _showDeleteSupplyDialog(context, ref, supply),
                  icon: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: ColorManager.errorColor.withOpacity(0.1),
                      borderRadius:
                          BorderRadius.circular(AppRadius.smallRadius),
                      border: Border.all(
                        color: ColorManager.errorColor.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: const Icon(
                      Icons.delete_outline,
                      color: ColorManager.errorColor,
                      size: 18,
                    ),
                  ),
                ),
              ] else ...[
                // Supply count indicator for non-today items
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpaces.smallPadding,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.teal,
                    borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                  ),
                  child: Text(
                    '${supply.supplies?.length ?? 0}',
                    style: context.hint.copyWith(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ],
          ),

          // Show supply items
          if (supply.supplies?.isNotEmpty == true) ...[
            context.smallGap,
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: supply.supplies!
                  .map((item) => Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.teal.withOpacity(0.1),
                          borderRadius:
                              BorderRadius.circular(AppRadius.smallRadius),
                          border:
                              Border.all(color: Colors.teal.withOpacity(0.3)),
                        ),
                        child: Text(
                          item.name ?? '',
                          style: context.hint.copyWith(
                            fontSize: 11,
                            color: Colors.teal,
                          ),
                        ),
                      ))
                  .toList(),
            ),
          ],
        ],
      ),
    );
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  void _showAssignSupplyDialog(BuildContext context) {
    showAssignSupplyDialog(context, student: student);
  }

  void _showEditSupplyDialog(BuildContext context, TeacherSupplyModel supply) {
    showEditSupplyDialog(context, student: student, existingSupply: supply);
  }

  void _showDeleteSupplyDialog(
      BuildContext context, WidgetRef ref, TeacherSupplyModel supply) {
    showDialog(
      context: context,
      builder: (context) => BaseDeleteDialog(
        description: context.tr.areYouSureToDeleteThisSupply,
        onConfirm: () async {
          final teacherSupplyController =
              ref.read(teacherSupplyControllerProvider(context));

          await teacherSupplyController.deleteTeacherSupply(
            supplyId: supply.id!,
          );

          // Refresh the data if callback is provided
          context.toReplacement(StudentDetailsScreen(student: student));
        },
      ),
    );
  }
}
