import 'package:connectify_app/src/screens/sleep/model/sleep_model.dart';
import 'package:connectify_app/src/screens/sleep/view/widgets/add_sleep_dialog.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

class SleepActivityCard extends HookWidget {
  final List<SleepModel> sleeps;
  final StudentModel student;
  final DateTime selectedDate;
  final VoidCallback? onRefresh;

  const SleepActivityCard({
    super.key,
    required this.sleeps,
    required this.student,
    required this.selectedDate,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    final isToday = _isToday(selectedDate);

    return BaseContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon and title
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: ColorManager.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                ),
                child: const Text(
                  '😴',
                  style: TextStyle(fontSize: 20),
                ),
              ),
              context.mediumGap,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.tr.sleep,
                      style: context.boldTitle.copyWith(
                        color: ColorManager.primaryColor,
                      ),
                    ),
                    if (sleeps.isNotEmpty)
                      Text(
                        '${sleeps.length} ${context.tr.sleep.toLowerCase()}',
                        style: context.hint.copyWith(fontSize: 12),
                      ),
                  ],
                ),
              ),
            ],
          ),

          if (sleeps.isEmpty) ...[
            context.mediumGap,
            Center(
              child: Column(
                children: [
                  const Icon(
                    Icons.bedtime_outlined,
                    size: 40,
                    color: ColorManager.grey,
                  ),
                  if (isToday) ...[
                    context.smallGap,
                    AddRectangleWidget(
                      height: 45.h,
                      title: context.tr.addSleep,
                      onTap: () => _showAddSleepDialog(context),
                    ),
                  ],
                ],
              ),
            ),
          ] else ...[
            context.mediumGap,
            ...sleeps.map((sleep) => _buildSleepItem(context, sleep)),

            // Add button for today (can add multiple times per day)
            if (isToday) ...[
              context.mediumGap,
              AddRectangleWidget(
                height: 45.h,
                title: context.tr.addSleep,
                onTap: () => _showAddSleepDialog(context),
              ),
            ],
          ],
        ],
      ),
    );
  }

  Widget _buildSleepItem(BuildContext context, SleepModel sleep) {
    final duration = _calculateSleepDuration(sleep);

    return Container(
      margin: const EdgeInsets.only(bottom: AppSpaces.smallPadding),
      padding: const EdgeInsets.all(AppSpaces.mediumPadding),
      decoration: BoxDecoration(
        color: Colors.indigo.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppRadius.smallRadius),
        border: Border.all(color: Colors.indigo.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // Sleep icon
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.indigo.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                ),
                child: const Icon(
                  Icons.bedtime,
                  color: Colors.indigo,
                  size: 20,
                ),
              ),
              context.smallGap,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.tr.sleepTime,
                      style: context.subTitle.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Colors.indigo,
                      ),
                    ),
                    if (duration.isNotEmpty) ...[
                      context.xSmallGap,
                      Text(
                        duration,
                        style: context.hint.copyWith(
                          fontSize: 12,
                          color: Colors.indigo.shade300,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              // Edit button for today's sleep
              if (_isToday(selectedDate)) ...[
                IconButton(
                  onPressed: () => _showEditSleepDialog(context, sleep),
                  icon: const Icon(
                    CupertinoIcons.square_pencil,
                    color: ColorManager.primaryColor,
                    size: 18,
                  ),
                ),
              ],
            ],
          ),
          context.smallGap,
          Row(
            children: [
              Expanded(
                child: _buildTimeContainer(
                  context,
                  context.tr.from,
                  sleep.sleepStartTime,
                  Colors.green,
                ),
              ),
              context.smallGap,
              const Icon(
                Icons.arrow_forward,
                color: ColorManager.grey,
                size: 16,
              ),
              context.smallGap,
              Expanded(
                child: _buildTimeContainer(
                  context,
                  context.tr.to,
                  sleep.sleepEndTime,
                  Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTimeContainer(
    BuildContext context,
    String label,
    String time,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppSpaces.smallPadding),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppRadius.smallRadius),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: context.hint.copyWith(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          context.xSmallGap,
          Text(
            time.isNotEmpty ? time : '--:--',
            style: context.subTitle.copyWith(
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  String _calculateSleepDuration(SleepModel sleep) {
    if (sleep.sleepStartTime.isEmpty || sleep.sleepEndTime.isEmpty) {
      return '';
    }

    try {
      // Parse time strings (assuming format HH:mm)
      final startParts = sleep.sleepStartTime.split(':');
      final endParts = sleep.sleepEndTime.split(':');

      if (startParts.length != 2 || endParts.length != 2) return '';

      final startHour = int.parse(startParts[0]);
      final startMinute = int.parse(startParts[1]);
      final endHour = int.parse(endParts[0]);
      final endMinute = int.parse(endParts[1]);

      final startTime = DateTime(2023, 1, 1, startHour, startMinute);
      var endTime = DateTime(2023, 1, 1, endHour, endMinute);

      // If end time is before start time, assume it's next day
      if (endTime.isBefore(startTime)) {
        endTime = endTime.add(const Duration(days: 1));
      }

      final duration = endTime.difference(startTime);
      final hours = duration.inHours;
      final minutes = duration.inMinutes % 60;

      if (hours > 0 && minutes > 0) {
        return '${hours}h ${minutes}m';
      } else if (hours > 0) {
        return '${hours}h';
      } else if (minutes > 0) {
        return '${minutes}m';
      }

      return '';
    } catch (e) {
      return '';
    }
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  void _showAddSleepDialog(BuildContext context) {
    showAddSleepDialog(context, student: student);
  }

  void _showEditSleepDialog(BuildContext context, SleepModel sleep) {
    showAddSleepDialog(context, student: student, existingSleep: sleep);
  }
}
