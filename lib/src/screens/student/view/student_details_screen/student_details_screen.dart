import 'dart:developer';

import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/screens/student/controllers/student_activities_controller.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/student/view/student_details_screen/widgets/activity_cards/food_activity_card.dart';
import 'package:connectify_app/src/screens/student/view/student_details_screen/widgets/activity_cards/mood_activity_card.dart';
import 'package:connectify_app/src/screens/student/view/student_details_screen/widgets/activity_cards/sleep_activity_card.dart';

import 'package:connectify_app/src/screens/student/view/student_details_screen/widgets/activity_cards/supplies_activity_card.dart';
import 'package:connectify_app/src/screens/supplies/model/teacher_supply_model.dart';
import 'package:connectify_app/src/screens/student/view/student_details_screen/widgets/activity_cards/toilet_activity_card.dart';
import 'package:connectify_app/src/screens/student/view/student_details_screen/widgets/daily_date_filter_widget.dart';
import 'package:connectify_app/src/screens/student/view/student_details_screen/widgets/student_details_expansion_tile.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/utils/age_calculator.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/widgets/shared_widgets.dart';
import '../../../home/<USER>/main_screen.dart';
import '../../../supplies/controller/teacher_supply_controller.dart';
import '../../controllers/student_controller.dart';
import '../../models/parent_activities_response_model.dart';
import '../student_screen/students_screen.dart';

class StudentDetailsScreen extends HookConsumerWidget {
  final StudentModel student;

  const StudentDetailsScreen({
    super.key,
    required this.student,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedDate = useState(DateTime.now());

    // Current student state for navigation
    final currentStudent = useState<StudentModel>(student);
    final currentStudentIndex = useState<int>(0);

    // Get all active students for navigation
    final allActiveStudentsAsync =
        ref.watch(getAllActiveStudentsProvider(context));

    // Get day name for teacher activities
    final dayName = DateFormat('EEEE', 'en').format(selectedDate.value);

    // Watch student activities for current student
    final activitiesAsync = ref.watch(
      getStudentActivitiesByDateProvider((
        context,
        currentStudent.value.id!,
        selectedDate.value.toIso8601String(),
        dayName,
      )),
    );

    // Watch teacher supplies for current student with date filtering
    final suppliesAsync = ref.watch(getTeacherSupplyByDateProvider(
        (context, currentStudent.value.id!, selectedDate.value)));

    // Initialize current student index when all students are loaded
    useEffect(() {
      allActiveStudentsAsync.whenData((allStudents) {
        final index = allStudents.indexWhere((s) => s.id == student.id);
        if (index != -1) {
          currentStudentIndex.value = index;
        }
      });
      return null;
    }, [allActiveStudentsAsync]);

    // Navigation functions
    void navigateToNextStudent() {
      allActiveStudentsAsync.whenData((allStudents) {
        final nextIndex = currentStudentIndex.value + 1;

        if (nextIndex < allStudents.length) {
          currentStudentIndex.value = nextIndex;
          final nextSimpleStudent = allStudents[nextIndex];

          // Convert SimpleStudentModel to StudentModel for navigation
          final nextStudent = StudentModel(
            id: nextSimpleStudent.id,
            name: nextSimpleStudent.name,
            birthDate: nextSimpleStudent.birthDate,
            image: nextSimpleStudent.image,
          );

          currentStudent.value = nextStudent;
        } else {
          context.showBarMessage(context.tr.noStudents);
        }
      });
    }

    void navigateToPreviousStudent() {
      allActiveStudentsAsync.whenData((allStudents) {
        final prevIndex = currentStudentIndex.value - 1;

        if (prevIndex >= 0) {
          currentStudentIndex.value = prevIndex;
          final prevSimpleStudent = allStudents[prevIndex];

          // Convert SimpleStudentModel to StudentModel for navigation
          final prevStudent = StudentModel(
            id: prevSimpleStudent.id,
            name: prevSimpleStudent.name,
            birthDate: prevSimpleStudent.birthDate,
            image: prevSimpleStudent.image,
          );

          currentStudent.value = prevStudent;
        } else {
          context.showBarMessage(context.tr.noStudents);
        }
      });
    }

    return WillPopScope(
      onWillPop: () async {
        context.toReplacement(const StudentsScreen());
        return false;
      },
      child: Scaffold(
        appBar: MainAppBar(
          onBackButton: () {
            context.toReplacement(const StudentsScreen());
          },
          isBackButton: true,
          title: context.tr.back,
          iconPath: '',
        ),
        body: ListView(
          children: [
            // Student header section with navigation
            allActiveStudentsAsync.when(
              data: (allStudents) => _buildStudentHeader(
                context,
                currentStudent.value,
                navigateToPreviousStudent,
                navigateToNextStudent,
                currentStudentIndex.value,
                allStudents.length,
              ),
              loading: () => _buildStudentHeader(
                context,
                currentStudent.value,
                () {},
                () {},
                0,
                1,
              ),
              error: (error, stack) => _buildStudentHeader(
                context,
                currentStudent.value,
                () {},
                () {},
                0,
                1,
              ),
            ),

            // Student details expansion tile
            // Padding(
            //   padding: const EdgeInsets.all(AppSpaces.mediumPadding),
            //   child: StudentDetailsExpansionTile(student: student),
            // ),
            context.mediumGap,

            // Date filter
            DailyDateFilterWidget(
              date: selectedDate.value,
              onNext: () => _onNextDate(selectedDate),
              onPrevious: () => _onPreviousDate(selectedDate),
              canGoNext: _canGoNext(selectedDate.value),
              canGoPrevious: true,
            ),

            // Activities section
            activitiesAsync.when(
              data: (activities) => _buildActivitiesSection(
                context,
                ref,
                activities,
                selectedDate.value,
                suppliesAsync,
                currentStudent
                    .value, // Pass current student instead of original
              ),
              loading: () => const LoadingWidget(),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: ColorManager.grey,
                    ),
                    context.mediumGap,
                    Text(
                      context.tr.noData,
                      style: context.hint,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStudentHeader(
    BuildContext context,
    StudentModel currentStudent,
    VoidCallback onPrevious,
    VoidCallback onNext,
    int currentIndex,
    int totalStudents,
  ) {
    log('CurrentFFFStudent: ${totalStudents}');
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        context.mediumGap,

        // Navigation row with student image
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Previous student arrow
            if (totalStudents > 1)
              IconButton(
                onPressed: onPrevious,
                icon: Icon(
                  Icons.arrow_back_ios,
                  color: ColorManager.primaryColor,
                  size: 24.r,
                ),
              )
            else
              SizedBox(width: 48.r), // Placeholder for alignment

            // Student image
            SizedBox(
              height: 100.r,
              width: 100.r,
              child: ClipRRect(
                borderRadius:
                    BorderRadius.circular(AppRadius.mainContainerRadius),
                child: Image.network(
                  currentStudent.image?.url ?? '',
                  errorBuilder: (context, error, stackTrace) => Image.network(
                    AppConsts.studentPlaceholder!,
                  ),
                ),
              ),
            ),

            // Next student arrow
            if (totalStudents > 1)
              IconButton(
                onPressed: onNext,
                icon: Icon(
                  Icons.arrow_forward_ios,
                  color: ColorManager.primaryColor,
                  size: 24.r,
                ),
              )
            else
              SizedBox(width: 48.r), // Placeholder for alignment
          ],
        ),

        context.mediumGap,
        Text(
          currentStudent.name,
          style: context.blueHint,
        ),
        context.xSmallGap,
        Text(
          AgeCalculator.calculateAge(currentStudent.birthDate, context),
          style: context.hint.copyWith(
            color: ColorManager.primaryColor,
            fontWeight: FontWeight.w500,
          ),
        ),

        // Show current position if multiple students
        if (totalStudents > 1) ...[
          context.xSmallGap,
          Text(
            '${currentIndex + 1} / $totalStudents',
            style: context.smallHint,
          ),
        ],
      ],
    );
  }

  Widget _buildActivitiesSection(
    BuildContext context,
    WidgetRef ref,
    ParentActivitiesResponseModel activities,
    DateTime selectedDate,
    AsyncValue<List<TeacherSupplyModel>> suppliesAsync,
    StudentModel currentStudent, // Add current student parameter
  ) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Food card
          FoodActivityCard(
            foods: activities.foods,
            student: currentStudent, // Use current student
            selectedDate: selectedDate,
            onRefresh: () => _refreshActivities(ref, currentStudent),
          ),

          context.mediumGap,

          // Mood card
          MoodActivityCard(
            moods: activities.moods,
            student: currentStudent, // Use current student
            selectedDate: selectedDate,
            onRefresh: () => _refreshActivities(ref, currentStudent),
          ),
          context.mediumGap,

          // Toilet card
          ToiletActivityCard(
            toilets: activities.toilets,
            student: currentStudent, // Use current student
            selectedDate: selectedDate,
            onRefresh: () => _refreshActivities(ref, currentStudent),
          ),
          context.mediumGap,

          // Sleep card
          SleepActivityCard(
            sleeps: activities.sleeps,
            student: currentStudent, // Use current student
            selectedDate: selectedDate,
            onRefresh: () => _refreshActivities(ref, currentStudent),
          ),
          context.mediumGap,

          // Supplies card
          suppliesAsync.when(
            data: (supplies) => SuppliesActivityCard(
              supplies: supplies,
              student: currentStudent, // Use current student
              selectedDate: selectedDate,
              onRefresh: () => _refreshActivities(ref, currentStudent),
            ),
            loading: () => const LoadingWidget(),
            error: (error, stack) => const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  void _onNextDate(ValueNotifier<DateTime> selectedDate) {
    if (_canGoNext(selectedDate.value)) {
      selectedDate.value = selectedDate.value.add(const Duration(days: 1));
    }
  }

  void _onPreviousDate(ValueNotifier<DateTime> selectedDate) {
    selectedDate.value = selectedDate.value.subtract(const Duration(days: 1));
  }

  bool _canGoNext(DateTime date) {
    final today = DateTime.now();
    final tomorrow = DateTime(today.year, today.month, today.day + 1);
    return date.isBefore(tomorrow);
  }

  void _refreshActivities(WidgetRef ref, StudentModel currentStudent) {
    ref
        .read(
          studentActivitiesAutoRefreshProvider(
              (currentStudent.id!, DateTime.now())).notifier,
        )
        .refresh();
  }
}
