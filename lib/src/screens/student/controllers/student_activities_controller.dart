import 'package:connectify_app/src/screens/student/repos/student_activities_repo.dart';
import 'package:connectify_app/src/screens/supplies/model/teacher_supply_model.dart';
import 'package:connectify_app/src/screens/supplies/repo/teacher_supply_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../models/parent_activities_response_model.dart';

class StudentActivitiesController extends BaseVM {
  final StudentActivitiesRepo _studentActivitiesRepo;
  final BuildContext context;

  StudentActivitiesController({
    required StudentActivitiesRepo studentActivitiesRepo,
    required this.context,
  })  : _studentActivitiesRepo = studentActivitiesRepo,
        super();

  Future<ParentActivitiesResponseModel> getStudentActivitiesByDate({
    required int studentId,
    required DateTime date,
    String? dayName,
  }) async {
    return await baseFunction(context, () async {
      final studentActivitiesData =
          await _studentActivitiesRepo.getStudentActivitiesByDate(
        studentId: studentId,
        date: date,
        dayName: dayName,
      );

      return studentActivitiesData;
    });
  }
}

// Provider for StudentActivitiesController
final studentActivitiesControllerProvider =
    Provider.family<StudentActivitiesController, BuildContext>((ref, context) {
  final studentActivitiesRepo = ref.watch(studentActivitiesRepoProvider);

  return StudentActivitiesController(
    studentActivitiesRepo: studentActivitiesRepo,
    context: context,
  );
});

// Provider for fetching student activities by date
final getStudentActivitiesByDateProvider = FutureProvider.family<
    ParentActivitiesResponseModel,
    (BuildContext, int, String, String)>((ref, params) {
  final (context, studentId, dateString, dayName) = params;
  final studentActivitiesRepo = ref.watch(studentActivitiesRepoProvider);

  final controller = StudentActivitiesController(
    studentActivitiesRepo: studentActivitiesRepo,
    context: context,
  );

  final date = DateTime.parse(dateString);

  return controller.getStudentActivitiesByDate(
    studentId: studentId,
    date: date,
    dayName: dayName,
  );
});

// Provider for auto-refresh functionality
final studentActivitiesAutoRefreshProvider = StateNotifierProvider.family<
    StudentActivitiesAutoRefreshNotifier, int, (int, DateTime)>((ref, params) {
  final (studentId, date) = params;
  return StudentActivitiesAutoRefreshNotifier(studentId, date);
});

class StudentActivitiesAutoRefreshNotifier extends StateNotifier<int> {
  final int studentId;
  final DateTime date;

  StudentActivitiesAutoRefreshNotifier(this.studentId, this.date) : super(0);

  void refresh() {
    state = state + 1;
  }
}
