import 'package:connectify_app/src/screens/supplies/model/supply_model.dart';
import 'package:connectify_app/src/screens/supplies/model/teacher_supply_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../history/model/history_model.dart';
import '../../history/repo/history_repo.dart';

final teacherSupplyRepoProvider = Provider((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);

  return TeacherSupplyRepo(networkApiServices);
});

class TeacherSupplyRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  TeacherSupplyRepo(this._networkApiServices);

  Future<void> addSupply({required TeacherSupplyModel teacherSupply}) async {
    return await baseFunction(() async {
      await _networkApiServices.postResponse(
          ApiEndpoints.editDeleteTeacherSupply,
          body: teacherSupply.toJson());

      for (var supply in teacherSupply.supplies ?? <SupplyModel>[]) {
        addNewHistory(
            historyModel: HistoryModel(
          historyType: HistoryType.supply,
          supply: supply,
          student: teacherSupply.student,
        ));
      }
    });
  }

  // get Teacher Supply Data
  Future<List<TeacherSupplyModel>> getTeacherSupplyData(
      {int? studentId}) async {
    return await baseFunction(() async {
      final response = await _networkApiServices
          .getResponse(ApiEndpoints.teacherSupply(studentId: studentId));

      final teacherSupplyData = List.from(response[ApiStrings.data])
          .map((e) => TeacherSupplyModel.fromJson(e))
          .toList();

      return teacherSupplyData;
    });
  }

  // get Teacher Supply Data by Date
  Future<List<TeacherSupplyModel>> getTeacherSupplyDataByDate({
    int? studentId,
    required DateTime date,
  }) async {
    return await baseFunction(() async {
      // Create date range for the specific day
      final startOfDay = DateTime(date.year, date.month, date.day, 0, 0, 0);
      final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);

      // Build the endpoint with date filters
      final endpoint = ApiEndpoints.teacherSupplyWithDateFilter(
        studentId: studentId,
        startDate: startOfDay,
        endDate: endOfDay,
      );

      final response = await _networkApiServices.getResponse(endpoint);

      final teacherSupplyData = List.from(response[ApiStrings.data])
          .map((e) => TeacherSupplyModel.fromJson(e))
          .toList();

      return teacherSupplyData;
    });
  }

  //? Update Supply ========================================================
  Future<void> updateSupply({required TeacherSupplyModel teacherSupply}) async {
    return await baseFunction(() async {
      await _networkApiServices.putResponse(
        '${ApiEndpoints.editDeleteTeacherSupply}/${teacherSupply.id}',
        data: teacherSupply.toJson(),
      );

      for (var supply in teacherSupply.supplies ?? <SupplyModel>[]) {
        addNewHistory(
            historyModel: HistoryModel(
          historyType: HistoryType.supply,
          supply: supply,
          student: teacherSupply.student,
        ));
      }
    });
  }

  //? Delete Supply ========================================================
  Future<void> deleteSupply({required int id}) async {
    return await baseFunction(() async {
      await _networkApiServices
          .deleteResponse('${ApiEndpoints.editDeleteTeacherSupply}/$id');
    });
  }
}
