import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/supplies/controller/supply_controller.dart';
import 'package:connectify_app/src/screens/supplies/model/supply_model.dart';
import 'package:connectify_app/src/screens/supplies/view/admin_supplies/supplies_screen.dart';
import 'package:connectify_app/src/screens/supplies/view/admin_supplies/widgets/supply_card.dart';
import 'package:connectify_app/src/screens/supplies/view/teacher_supplies/widgets/assign_supply_dialog.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_list/base_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import 'add_supply_dialog.dart';

class SuppliesList extends HookConsumerWidget {
  const SuppliesList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final page = useState<int>(1);
    final isLoadingMore = useState<bool>(false);
    final isInitialLoadComplete = useState<bool>(false);
    final supplies = useState<List<SupplyModel>>([]);

    final params = (context, page.value);

    useEffect(() {
      ref.refresh(getSupplyDataProviderWithPagination(params));
      return () {};
    }, [page.value]);

    ref.listenPagination<SupplyModel>(
      provider: getSupplyDataProviderWithPagination(params),
      dataNotifier: supplies,
      isLoadingNotifier: isLoadingMore,
      isInitialLoadCompleteNotifier: isInitialLoadComplete,
    );

    final isTeacher = const UserModel().currentUser.isCurrentUserTeacher;

    return Column(
      children: [
        if (isTeacher) ...[
          const AddSupplyDialog(
            navigateWidget: SuppliesScreen(),
          ).paddingAll(AppSpaces.mediumPadding),
          const AssignSupplyDialog().paddingOnly(
            left: AppSpaces.mediumPadding,
            right: AppSpaces.mediumPadding,
            bottom: AppSpaces.mediumPadding,
          ),
        ] else ...[
          const AddSupplyDialog(
            navigateWidget: SuppliesScreen(),
          ).paddingAll(AppSpaces.mediumPadding),
        ],
        Expanded(
          child: BaseList(
            page: page,
            padding: const EdgeInsets.all(AppSpaces.mediumPadding),
            isLoading: !isInitialLoadComplete.value,
            isLoadingMore: isLoadingMore,
            data: supplies.value,
            itemBuilder: (supplyModel, index) =>
                SupplyCard(supply: supplyModel),
          ),
        ),
      ],
    );
  }
}
// import 'package:connectify_app/src/screens/auth/models/user_model.dart';
// import 'package:connectify_app/src/screens/supplies/controller/meal_controller.dart';
// import 'package:connectify_app/src/screens/supplies/model/meal_model.dart';
// import 'package:connectify_app/src/screens/supplies/view/admin_supplies/meals_screen.dart';
// import 'package:connectify_app/src/screens/supplies/view/admin_supplies/widgets/meal_card.dart';
// import 'package:connectify_app/src/screens/supplies/view/teacher_supplies/widgets/assign_supply_dialog.dart';
// import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
// import 'package:flutter/material.dart';
// import 'package:hooks_riverpod/hooks_riverpod.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// import 'add_meal_dialog.dart';
//
// class SuppliesList extends ConsumerWidget {
//   const SuppliesList({super.key});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final supplyCtrl = ref.watch(getSupplyDataProvider(context));
//     final isTeacher = const UserModel().currentUser.isTeacher;
//     return Column(
//       children: [
//         if (isTeacher) ...[
//           const AssignSupplyDialog().paddingAll(AppSpaces.mediumPadding)
//         ] else ...[
//           const AddSupplyDialog(
//             navigateWidget: SuppliesScreen(),
//           ).paddingAll(AppSpaces.mediumPadding),
//         ],
//         Expanded(
//           child: supplyCtrl.get(
//             data: (supplies) => ListView.separated(
//                 padding: const EdgeInsets.all(AppSpaces.mediumPadding),
//                 itemBuilder: (context, index) =>
//                     SupplyCard(supply: supplies[index]),
//                 separatorBuilder: (context, index) => context.mediumGap,
//                 itemCount: supplies.length),
//           ),
//         ),
//       ],
//     );
//   }
// }
