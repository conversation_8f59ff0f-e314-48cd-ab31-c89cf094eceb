import 'package:connectify_app/src/screens/notification/model/notification_model.dart';
import 'package:connectify_app/src/screens/notification/repo/notification_repo.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/supplies/model/teacher_supply_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../model/supply_model.dart';
import '../repo/teacher_supply_repo.dart';

final teacherSupplyControllerProvider =
    Provider.family<TeacherSupplyController, BuildContext>((ref, context) {
  final teacherSupplyRepo = ref.watch(teacherSupplyRepoProvider);

  return TeacherSupplyController(
      teacherSupplyRepo: teacherSupplyRepo, context: context);
});

final teacherSupplyControllerChangeNotifierProvider =
    ChangeNotifierProvider.family<TeacherSupplyController, BuildContext>(
        (ref, context) {
  final teacherSupplyRepo = ref.watch(teacherSupplyRepoProvider);

  return TeacherSupplyController(
      teacherSupplyRepo: teacherSupplyRepo, context: context);
});

// get
final getTeacherSupplyDataProvider = FutureProvider.autoDispose
    .family<List<TeacherSupplyModel>, (BuildContext, int)>((ref, params) {
  final teacherSupplyCtrl =
      ref.watch(teacherSupplyControllerProvider(params.$1));
  return teacherSupplyCtrl.getTeacherSupply(params.$2);
});

// get supplies by date
final getTeacherSupplyByDateProvider = FutureProvider.autoDispose
    .family<List<TeacherSupplyModel>, (BuildContext, int, DateTime)>(
        (ref, params) {
  final teacherSupplyCtrl =
      ref.watch(teacherSupplyControllerProvider(params.$1));
  return teacherSupplyCtrl.getTeacherSupplyByDate(params.$2, params.$3);
});

class TeacherSupplyController extends BaseVM {
  final TeacherSupplyRepo teacherSupplyRepo;
  final BuildContext context;

  TeacherSupplyController(
      {required this.teacherSupplyRepo, required this.context});

  // get
  Future<List<TeacherSupplyModel>> getTeacherSupply(int studentId) async {
    return await baseFunction(context, () async {
      final supplies =
          await teacherSupplyRepo.getTeacherSupplyData(studentId: studentId);
      return supplies;
    });
  }

  // get supplies by date
  Future<List<TeacherSupplyModel>> getTeacherSupplyByDate(
      int studentId, DateTime date) async {
    return await baseFunction(context, () async {
      final supplies = await teacherSupplyRepo.getTeacherSupplyDataByDate(
        studentId: studentId,
        date: date,
      );
      return supplies;
    });
  }

  Future<void> addTeacherSupply(
      {required List<SupplyModel>? supplies,
      required StudentModel student}) async {
    return await baseFunction(context, () async {
      final teacherSupply =
          TeacherSupplyModel(student: student, supplies: supplies);

      await teacherSupplyRepo.addSupply(teacherSupply: teacherSupply);

      NotificationService.sendNotification(
        title: "Supply Request Sent",
        body: "New request for supplies has been submitted.",
        userTokenOrTopic: NurseryModelHelper.parentByStudentTopic(student?.id),
        isTopic: true,
      );

      postNewNotification(
          notificationModel: NotificationModel(
        title: "Supply Request Sent",
        body: "New request for supplies has been submitted.",
        topic: NurseryModelHelper.parentByStudentTopic(student.id),
      ));
    });
  }

  //? Update Teacher Supply ========================================================
  Future<void> updateTeacherSupply({
    required List<SupplyModel>? supplies,
    required StudentModel student,
    required int supplyId,
  }) async {
    return await baseFunction(context, () async {
      final teacherSupply = TeacherSupplyModel(
        id: supplyId,
        student: student,
        supplies: supplies,
      );

      await teacherSupplyRepo.updateSupply(teacherSupply: teacherSupply);

      NotificationService.sendNotification(
        title: "Supply Update",
        body: "Supply request has been updated for ${student.name}.",
        userTokenOrTopic: NurseryModelHelper.parentByStudentTopic(student.id),
        isTopic: true,
      );

      postNewNotification(
          notificationModel: NotificationModel(
        title: "Supply Update",
        body: "Supply request has been updated for ${student.name}.",
        topic: NurseryModelHelper.parentByStudentTopic(student.id),
      ));

      if (!context.mounted) return;
      context.back();
      context.showBarMessage(context.tr.editedSuccessfully);
    });
  }

  //? Delete Teacher Supply ========================================================
  Future<void> deleteTeacherSupply({required int supplyId}) async {
    return await baseFunction(context, () async {
      await teacherSupplyRepo.deleteSupply(id: supplyId);

      if (!context.mounted) return;
      context.back();
      context.showBarMessage(context.tr.deletedSuccessfully);
    });
  }
}
