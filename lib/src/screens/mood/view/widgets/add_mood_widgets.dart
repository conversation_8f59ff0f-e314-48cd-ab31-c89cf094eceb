import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/widgets/base_list/base_list.dart';
import '../../../../shared/widgets/drop_downs/multi_student_drop_down.dart';
import '../../../../shared/widgets/tab_bar_widgets/add_container_widget.dart';

class AddMoodWidgets extends HookWidget {
  final Map<String, ValueNotifier> valueNotifiers;
  final bool hideStudentDropdown;

  const AddMoodWidgets({
    super.key,
    required this.valueNotifiers,
    this.hideStudentDropdown = false,
  });

  @override
  Widget build(BuildContext context) {
    final List<String> moodList = [
      context.tr.angry,
      context.tr.calm,
      context.tr.excited,
      context.tr.happy,
      context.tr.sad,
      context.tr.sleepy,
      context.tr.unwell,
      context.tr.worried,
    ];

    final List<String> moodImages = [
      'assets/svg/angry.png',
      'assets/svg/calm.png',
      'assets/svg/excited.png',
      'assets/svg/happy.png',
      'assets/svg/sad.png',
      'assets/svg/sleepy.png',
      'assets/svg/unwell.png',
      'assets/svg/worried.png',
    ];

    final indexMoodValue = useState<int>(0);
    final noteController = useTextEditingController(
        text: valueNotifiers[ApiStrings.note]?.value ?? '');

    useEffect(() {
      valueNotifiers[ApiStrings.note]?.addListener(() {
        noteController.text = valueNotifiers[ApiStrings.note]?.value ?? '';
      });
      return null;
    }, []);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!hideStudentDropdown) ...[
          MultiStudentDropDown(
            selectedStudents: valueNotifiers[ApiStrings.students]
                as ValueNotifier<List<StudentModel>>,
          ),
          context.mediumGap,
        ],
        Text(
          context.tr.mood,
          style: context.title,
        ),
        context.smallGap,
        BaseList.grid(
          data: moodList,
          crossAxisCount: 3,
          itemBuilder: (context, index) {
            return AddContainerWidget(
              value: index,
              title: moodList[index],
              groupValue: valueNotifiers[ApiStrings.mood] as ValueNotifier<int>,
              imagePath: moodImages[index],
              index: indexMoodValue,
              onTap: () {
                indexMoodValue.value = index;
                valueNotifiers[ApiStrings.mood]?.value = index;
              },
            );
          },
        ),
        context.mediumGap,
        Text(
          context.tr.note,
          style: context.title,
        ),
        context.smallGap,
        BaseTextField(
          isRequired: false,
          controller: noteController,
          label: context.tr.note,
          hint: context.tr.enterNote,
          maxLines: 3,
          onChanged: (value) {
            valueNotifiers[ApiStrings.note]?.value = value;
          },
        ),
      ],
    );
  }
}
