import 'dart:developer';

import 'package:connectify_app/src/screens/mood/controller/mood_controller.dart';
import 'package:connectify_app/src/screens/mood/view/widgets/add_mood_widgets.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/student/view/student_details_screen/student_details_screen.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/widgets/shared_widgets.dart';
import '../../model/mood_model.dart';

Future<void> showAddMoodDialog(context,
    {StudentModel? student, MoodModel? existingMood}) {
  return showDialog(
    context: context,
    builder: (context) {
      return HookConsumer(
        builder: (context, ref, child) {
          final moodChangeNotifierCtrl =
              ref.watch(moodChangeNotifierControllerProvider(context));

          final valueNotifiers = {
            ApiStrings.students:
                useState<List<StudentModel>>(student != null ? [student] : []),
            ApiStrings.mood: useState<int>(existingMood?.mood != null
                ? MoodModel.getMoodValue(value: existingMood!.mood!.index).index
                : 0),
            ApiStrings.note: useState<String>(existingMood?.note ?? ''),
          };

          //!-----------------------------------------------------

          final formKey = useState(GlobalKey<FormState>());

          //!-----------------------------------------------------

          Future<void> addOrUpdateMood() async {
            if (!formKey.value.currentState!.validate()) return;
            final students = valueNotifiers[ApiStrings.students]!.value
                as List<StudentModel>;
            if (students.isEmpty) {
              context.showBarMessage(context.tr.pleaseSelectStudent,
                  isError: true);
              return;
            }

            log('Mood Students: $students');

            if (existingMood != null) {
              // Update existing mood
              await moodChangeNotifierCtrl.updateMood(
                moodValue: valueNotifiers[ApiStrings.mood]?.value as int,
                note: valueNotifiers[ApiStrings.note]?.value as String?,
                student: students.first,
                moodId: existingMood.id!,
              );
            } else {
              // Add new mood
              await Future.forEach(
                students,
                (student) async {
                  await moodChangeNotifierCtrl.addMood(
                    moodValue: valueNotifiers[ApiStrings.mood]?.value as int,
                    note: valueNotifiers[ApiStrings.note]?.value as String?,
                    student: student,
                  );
                },
              );
            }

            if (!context.mounted) return;

            // Navigate back to student details if student was provided
            if (student != null) {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => StudentDetailsScreen(student: student),
                ),
              );
            } else {
              context.back();
            }

            context.showBarMessage(existingMood != null
                ? context.tr.editedSuccessfully
                : context.tr.addedSuccessfully);
          }

          //!-----------------------------------------------------

          return AlertDialogWidget(
              header: existingMood != null
                  ? context.tr.editMood
                  : context.tr.addMood,
              isLoading: moodChangeNotifierCtrl.isLoading,
              isImage: false,
              child: Form(
                key: formKey.value,
                child: AddMoodWidgets(
                  valueNotifiers: valueNotifiers,
                  hideStudentDropdown: student != null,
                ),
              ),
              onConfirm: () async => await addOrUpdateMood());
        },
      );
    },
  );
}
