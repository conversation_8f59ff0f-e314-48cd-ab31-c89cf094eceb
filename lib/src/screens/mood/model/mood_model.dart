import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';

import '../../nursery/models/nursery_model_helper.dart';

List<MoodModel> responseToMoodModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final moodData = data.map((e) => MoodModel.fromJson(e)).toList();

  return moodData;
}

enum MoodTypes { angry, calm, excited, happy, sad, sleepy, unwell, worried }

class MoodModel extends Equatable {
  final int? id;
  final MoodTypes? mood;
  final String? note;
  final StudentModel? student;
  final NurseryModel? nursery;
  final TeacherModel? teacher;

  const MoodModel({
    this.id,
    this.mood,
    this.note,
    this.student,
    this.nursery,
    this.teacher,
  });

  factory MoodModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];
    return MoodModel(
      id: json[ApiStrings.id],
      mood:
          getMoodFromApi(attributes[ApiStrings.mood].toString().toLowerCase()),
      note: attributes[ApiStrings.note],
    );
  }

  static MoodTypes? getMoodFromApi(String? mood) {
    switch (mood?.toLowerCase()) {
      case 'angry':
        return MoodTypes.angry;
      case 'calm':
        return MoodTypes.calm;
      case 'excited':
        return MoodTypes.excited;
      case 'happy':
        return MoodTypes.happy;
      case 'sad':
        return MoodTypes.sad;
      case 'sleepy':
        return MoodTypes.sleepy;
      case 'unwell':
        return MoodTypes.unwell;
      case 'worried':
        return MoodTypes.worried;
      default:
        return null;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.mood: getMood(),
      if (note != null) ApiStrings.note: note,
      ApiStrings.student: student?.id,
      ApiStrings.teacher: const UserModel().currentUser.id,
      ApiStrings.nursery: NurseryModelHelper.currentNurseryId()
    };
  }

  String getMood() {
    switch (mood) {
      case MoodTypes.angry:
        return "angry";
      case MoodTypes.calm:
        return "calm";
      case MoodTypes.excited:
        return "excited";
      case MoodTypes.happy:
        return "happy";
      case MoodTypes.sad:
        return "sad";
      case MoodTypes.sleepy:
        return "sleepy";
      case MoodTypes.unwell:
        return "unwell";
      case MoodTypes.worried:
        return "worried";
      default:
        return 'happy';
    }
  }

  static MoodTypes getMoodValue({required int value}) {
    switch (value) {
      case 0:
        return MoodTypes.angry;
      case 1:
        return MoodTypes.calm;
      case 2:
        return MoodTypes.excited;
      case 3:
        return MoodTypes.happy;
      case 4:
        return MoodTypes.sad;
      case 5:
        return MoodTypes.sleepy;
      case 6:
        return MoodTypes.unwell;
      case 7:
        return MoodTypes.worried;
      default:
        return MoodTypes.happy;
    }
  }

  //getMood context lang
  String getMoodContextLang(BuildContext context) {
    switch (mood) {
      case MoodTypes.angry:
        return context.tr.angry;
      case MoodTypes.calm:
        return context.tr.calm;
      case MoodTypes.excited:
        return context.tr.excited;
      case MoodTypes.happy:
        return context.tr.happy;
      case MoodTypes.sad:
        return context.tr.sad;
      case MoodTypes.sleepy:
        return context.tr.sleepy;
      case MoodTypes.unwell:
        return context.tr.unwell;
      case MoodTypes.worried:
        return context.tr.worried;
      default:
        return context.tr.happy;
    }
  }

  @override
  List<Object?> get props => [id, mood, note, student, nursery, teacher];
}
