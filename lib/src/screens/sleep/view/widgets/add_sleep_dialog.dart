import 'package:connectify_app/src/screens/sleep/controller/sleep_controller.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/student/view/student_details_screen/student_details_screen.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/widgets/shared_widgets.dart';
import 'add_sleep_widgets.dart';
import '../../model/sleep_model.dart';

class AddSleepDialog extends StatelessWidget {
  const AddSleepDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox();
  }
}

Future<void> showAddSleepDialog(context,
    {StudentModel? student, SleepModel? existingSleep}) {
  return showDialog(
    context: context,
    builder: (context) {
      return HookConsumer(
        builder: (context, ref, child) {
          final sleepCtrl =
              ref.watch(sleepChangeNotifierControllerProvider(context));
          //!-----------------------------------------------------

          final formKey = useState(GlobalKey<FormState>());

          //!-----------------------------------------------------

          final studentValue = useState<StudentModel?>(student);

          final controllers = {
            ApiStrings.from: useTextEditingController(
                text: existingSleep?.sleepStartTime ?? ''),
            ApiStrings.to: useTextEditingController(
                text: existingSleep?.sleepEndTime ?? ''),
          };
          //!-----------------------------------------------------

          Future<void> addOrUpdateSleep() async {
            if (!formKey.value.currentState!.validate()) return;

            if (studentValue.value == null) {
              context.showBarMessage(context.tr.pleaseSelectStudent,
                  isError: true);
              return;
            }

            // validate if from time is before to time
            if (controllers[ApiStrings.from]!.text.isNotEmpty &&
                controllers[ApiStrings.to]!.text.isNotEmpty) {
              final fromTime = controllers[ApiStrings.from]!.text;
              final toTime = controllers[ApiStrings.to]!.text;
              if (fromTime.compareTo(toTime) > 0) {
                context.showBarMessage(context.tr.fromTimeShouldBeBeforeToTime,
                    isError: true);
                return;
              }

              if (fromTime.compareTo(toTime) == 0) {
                context.showBarMessage(
                    context.tr.fromTimeShouldNotBeEqualToToTime,
                    isError: true);
                return;
              }
            }

            if (existingSleep != null) {
              // Update existing sleep
              await sleepCtrl.updateSleep(
                controllers: controllers,
                student: studentValue.value,
                sleepId: existingSleep.id!,
              );
            } else {
              // Add new sleep
              await sleepCtrl.addSleep(
                controllers: controllers,
                student: studentValue.value,
              );
            }

            if (!context.mounted) return;

            // Navigate back to student details if student was provided
            if (student != null) {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => StudentDetailsScreen(student: student),
                ),
              );
            } else {
              context.back();
            }
            context.showBarMessage(existingSleep != null
                ? context.tr.editedSuccessfully
                : context.tr.addedSuccessfully);
          }

          return AlertDialogWidget(
            header:
                existingSleep != null ? context.tr.editSleep : context.tr.sleep,
            isLoading: sleepCtrl.isLoading,
            isImage: false,
            child: Form(
              key: formKey.value,
              child: AddSleepWidgets(
                studentValue: studentValue,
                controllers: controllers,
                hideStudentDropdown: student != null,
              ),
            ),
            onConfirm: () async => await addOrUpdateSleep(),
          );
        },
      );
    },
  );
}
