import 'package:connectify_app/src/screens/notification/model/notification_model.dart';
import 'package:connectify_app/src/screens/notification/repo/notification_repo.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/screens/sleep/repo/sleep_repo.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../model/sleep_model.dart';

final sleepControllerProvider =
    Provider.family<SleepController, BuildContext>((ref, context) {
  final sleepRepo = ref.watch(sleepRepoProvider);

  return SleepController(sleepRepo: sleepRepo, context: context);
});
final sleepChangeNotifierControllerProvider =
    ChangeNotifierProvider.family<SleepController, BuildContext>(
        (ref, context) {
  final sleepRepo = ref.watch(sleepRepoProvider);

  return SleepController(sleepRepo: sleepRepo, context: context);
});

final getAllSleepData =
    FutureProvider.family<List<SleepModel>, BuildContext>((ref, context) {
  final sleepRepo = ref.watch(sleepRepoProvider);

  final sleepController =
      SleepController(sleepRepo: sleepRepo, context: context);

  return sleepController.getSleepData();
});

class SleepController extends BaseVM {
  final SleepRepo sleepRepo;
  final BuildContext context;

  SleepController({required this.sleepRepo, required this.context});

  Future<List<SleepModel>> getSleepData() async {
    return await baseFunction(context, () async {
      final sleepData = await sleepRepo.getSleepData();

      return sleepData;
    });
  }

  //? Add Sleep ========================================================
  Future<void> addSleep(
      {required Map<String, TextEditingController> controllers,
      required final StudentModel? student}) async {
    return await baseFunction(context, () async {
      final sleep = SleepModel(
          sleepStartTime: controllers[ApiStrings.from]?.text ?? '',
          sleepEndTime: controllers[ApiStrings.to]?.text ?? '',
          student: student);

      await sleepRepo.addSleep(sleep: sleep);

      NotificationService.sendNotification(
        title: "Sleep Update",
        body:
            "${student?.name} has sleep from ${sleep.sleepStartTime} to ${sleep.sleepEndTime} of sleep.",
        userTokenOrTopic: NurseryModelHelper.parentByStudentTopic(student?.id),
        isTopic: true,
      );

      postNewNotification(
          notificationModel: NotificationModel(
        title: "Sleep Update",
        body:
            "${student?.name} has sleep from ${sleep.sleepStartTime} to ${sleep.sleepEndTime} of sleep.",
        topic: NurseryModelHelper.parentByStudentTopic(student?.id),
      ));

      if (!context.mounted) return;
      context.back();
      context.showBarMessage(context.tr.addedSuccessfully);
    });
  }

  //? Update Sleep ========================================================
  Future<void> updateSleep({
    required Map<String, TextEditingController> controllers,
    required StudentModel? student,
    required int sleepId,
  }) async {
    return await baseFunction(context, () async {
      final sleep = SleepModel(
        id: sleepId,
        sleepStartTime: controllers[ApiStrings.from]?.text ?? '',
        sleepEndTime: controllers[ApiStrings.to]?.text ?? '',
        student: student,
      );

      await sleepRepo.updateSleep(sleep: sleep);

      NotificationService.sendNotification(
        title: "Sleep Update",
        body:
            "${student?.name} sleep record has been updated from ${sleep.sleepStartTime} to ${sleep.sleepEndTime}.",
        userTokenOrTopic: NurseryModelHelper.parentByStudentTopic(student?.id),
        isTopic: true,
      );

      postNewNotification(
          notificationModel: NotificationModel(
        title: "Sleep Update",
        body:
            "${student?.name} sleep record has been updated from ${sleep.sleepStartTime} to ${sleep.sleepEndTime}.",
        topic: NurseryModelHelper.parentByStudentTopic(student?.id),
      ));

      if (!context.mounted) return;
      context.back();
      context.showBarMessage(context.tr.editedSuccessfully);
    });
  }

  //? Delete Sleep ========================================================
  Future<void> deleteSleep({required int sleepId}) async {
    return await baseFunction(context, () async {
      await sleepRepo.deleteSleep(id: sleepId);

      if (!context.mounted) return;
      context.back();
      context.showBarMessage(context.tr.deletedSuccessfully);
    });
  }
}
