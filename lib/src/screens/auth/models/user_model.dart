import 'dart:developer';

import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/shared/shared_models/base_media_model.dart';
import 'package:connectify_app/src/shared/shared_models/base_model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../shared/data/remote/api_strings.dart';

part 'user_model_helper.dart';

List<UserModel> responseToUserModelList(response) {
  final data = (response as List?) ?? [];

  final users = data.map((e) => UserModel.fromJson(e)).toList();

  return users;
}

class UserModel extends BaseModel {
  final String? phone;
  final UserTypeEnum? userType;
  final NurseryModel? nurseryModel;

  //? For Auth
  final String? email;
  final String? password;

  final List<ClassModel>? classes;

  final String? fcmToken;

  final List<(int? studentId, int? classId)> studentIds;

  final List<String> accessPermissions;

  const UserModel({
    super.id,
    super.name,
    super.description,
    super.image,
    this.phone,
    this.classes,
    this.userType,
    this.email,
    this.password,
    this.nurseryModel,
    this.fcmToken,
    this.studentIds = const [],
    this.accessPermissions = const [],
  });

  bool get isTeacher => userType == UserTypeEnum.teacher;

  bool get isAccountant => userType == UserTypeEnum.accountant;

  bool get isSupervisor => userType == UserTypeEnum.supervisor;

  bool get isStaffMember => isTeacher || isAccountant || isSupervisor;

  factory UserModel.fromJson(Map<String, dynamic> json) {
    final image = json[ApiStrings.image] != null
        ? BaseMediaModel.fromJson(json[ApiStrings.image])
        : null;

    // final classModel = List
    // json[ApiStrings.classString] != null
    //     ? ClassModel.fromJsonWithOutAttributes(json[ApiStrings.classString])
    //     : null;

    final nurseryModel = json[ApiStrings.nursery] != null
        ? NurseryModel.fromJson(json[ApiStrings.nursery])
        : null;

    return UserModel(
        id: json[ApiStrings.id],
        name: json[ApiStrings.username] ?? '',
        email: json[ApiStrings.email] ?? '',
        phone: json[ApiStrings.phone] ?? '',
        description: json[ApiStrings.jobTitle] ?? '',
        password: json[ApiStrings.password] ?? '',
        studentIds: json[ApiStrings.studentIds] != null
            ? (json[ApiStrings.studentIds] as List<dynamic>)
                .map<(int? studentId, int? classId)>((e) => (
                      e[ApiStrings.studentId] as int?,
                      e[ApiStrings.classId] as int?
                    ))
                .toList()
            : [],
        classes: json[ApiStrings.teacherClasses] != null
            ? (json[ApiStrings.teacherClasses] as List)
                .map<ClassModel>((e) => ClassModel.fromJsonWithOutAttributes(e))
                .toList()
            : [],
        accessPermissions: json['access_permissions'] != null
            ? (json['access_permissions'] as List)
                .map<String>((e) => e['permission'] as String)
                .toList()
            : [],
        image: image,
        nurseryModel: nurseryModel,
        fcmToken: json[ApiStrings.fcmToken],
        userType: _userTypeCheck(
          json[ApiStrings.type],
        ));
  }

  Map<String, dynamic> toJson({
    //? For Parents
    int? studentId,
    int? classId,
    bool sendUsername = true,
  }) {
    return {
      if (id != null) ApiStrings.id: id,
      if (name.isNotEmpty && sendUsername) ApiStrings.username: name,
      ApiStrings.phone: phone,
      ApiStrings.type: const UserModel().currentUser.isCurrentUserTeacher
          ? ApiStrings.teacher
          : userType?.toString().split('.').last ?? ApiStrings.admin,
      ApiStrings.email: email,
      // '$<EMAIL>',

      if (password != null && password!.isNotEmpty)
        ApiStrings.password: password,

      // '${name.trim()}@1234',
      if (classes != null)
        ApiStrings.teacherClasses: classes?.map((e) => e.toJson()).toList(),
      if (studentId != null) ApiStrings.studentId: studentId,
      if (studentId != null)
        // ApiStrings.studentIds: studentId,
        ApiStrings.studentIds: [
          ...[
            for (var student in studentIds)
              if (student.$1 == studentId)
                {
                  ApiStrings.studentId: studentId,
                  ApiStrings.classId:
                      classId, // Update classId for existing studentId
                }
              else
                {
                  ApiStrings.studentId: student.$1,
                  ApiStrings.classId: student.$2,
                }
          ],
          if (!studentIds.map((e) => e.$1).contains(studentId))
            {
              ApiStrings.studentId: studentId,
              ApiStrings.classId: classId, // Add new studentId with classId
            }
        ],
      if (studentId != null)
        ApiStrings.nurseryId: NurseryModelHelper.currentNurseryId(),
      if (classId != null) ApiStrings.classId: classId,
      if (accessPermissions.isNotEmpty)
        ApiStrings.accessPermissions: accessPermissions
            .map((e) => {
                  ApiStrings.permission: e,
                })
            .toList(),
    };
  }

  //? To Login Json
  Map<String, dynamic> toLoginJson({bool isEdit = false}) {
    return {
      if (email != null && email!.isNotEmpty) ApiStrings.identifier: email,
      if (password != null && password!.isNotEmpty)
        ApiStrings.password: password,
      if (fcmToken != null && fcmToken!.isNotEmpty)
        ApiStrings.fcmToken: fcmToken,
    };
  }

  Map<String, dynamic> toStaffMemberJson({
    bool isEdit = false,
  }) {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.username: name,
      ApiStrings.jobTitle: description,
      ApiStrings.phone: phone,
      ApiStrings.type:
          userType?.toString().split('.').last ?? ApiStrings.teacher,
      // Add classes for teachers and supervisors
      if (classes != null &&
          classes!.isNotEmpty &&
          (userType == UserTypeEnum.teacher ||
              userType == UserTypeEnum.supervisor))
        ApiStrings.teacherClasses: classes!.map((e) => e.id).toList(),
      ApiStrings.email: '$<EMAIL>',
      if (password != null && password!.isNotEmpty)
        ApiStrings.password: password
      else
        ApiStrings.password: '${name.trim()}@1234',
    };
  }

  factory UserModel.empty() => const UserModel();

  UserModel copyWith({
    int? id,
    String? name,
    String? description,
    BaseMediaModel? image,
    String? phone,
    List<ClassModel>? classes,
    UserTypeEnum? userType,
    String? email,
    String? password,
    String? fcmToken,
    List<(int? studentId, int? classId)>? studentIds,
    NurseryModel? nurseryModel,
    List<String>? accessPermissions,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      image: image ?? this.image,
      phone: phone ?? this.phone,
      classes: classes ?? this.classes,
      userType: userType ?? this.userType,
      email: email ?? this.email,
      password: password ?? this.password,
      fcmToken: fcmToken ?? this.fcmToken,
      studentIds: studentIds ?? this.studentIds,
      nurseryModel: nurseryModel ?? this.nurseryModel,
      accessPermissions: accessPermissions ?? this.accessPermissions,
    );
  }

  //? To String
  @override
  String toString() {
    return 'UserModel{id: $id, name: $name, description: $description, image: $image, phone: $phone, userType: ${userType?.name}, email: $email, password: $password, classModel: ${classes?.map((e) => e?.name).toList()}, fcmToken: $fcmToken}';
  }
}
