part of 'user_model.dart';

enum UserTypeEnum { admin, teacher, parent, accountant, supervisor }

bool hasPermission(UserModel user, String permission,
    {bool canViewByDefaultForTeacher = true}) {
  return user.isCurrentUserAdmin ||
      (user.isCurrentUserTeacher &&
          user.accessPermissions.isEmpty &&
          canViewByDefaultForTeacher) ||
      user.accessPermissions.contains(permission);
}

UserTypeEnum _userTypeCheck(String? type) {
  switch (type) {
    case ApiStrings.admin:
      return UserTypeEnum.admin;

    case ApiStrings.teacher:
      return UserTypeEnum.teacher;

    case ApiStrings.parent:
      return UserTypeEnum.parent;

    case 'accountant':
      return UserTypeEnum.accountant;

    case 'supervisor':
      return UserTypeEnum.supervisor;

    default:
      return UserTypeEnum.teacher;
  }
}

//? Get User Data from local

extension UserModelExtensions on UserModel {
  UserModel get currentUser {
    final user = GetStorageService.getLocalData(
      key: LocalKeys.user,
    );

    if (user == null) return UserModel.empty();

    return UserModel.fromJson(user);
  }

  bool get isCurrentUserTeacher => currentUser.userType == UserTypeEnum.teacher;

  bool get isCurrentUserNotAdmin => currentUser.userType != UserTypeEnum.admin;

  bool get isCurrentUserAdmin => currentUser.userType == UserTypeEnum.admin;

  bool get isCurrentUserAccountant =>
      currentUser.userType == UserTypeEnum.accountant;

  bool get isConnectifyAdmin => currentUser.email == '<EMAIL>';

  // String get selectedUserStudentFilter {
  //   if (isTeacher) {
  //     final classId = selectedTeacherClass.value?.id;
  //     // currentUser.classes?.firstOrNull?.id;
  //
  //     return '&filters[class][id]=$classId';
  //   }
  //
  //   return '';
  // }

  String selectedTeacherClassFilter() {
    if (isCurrentUserTeacher) {
      final classId = selectedTeacherClass.value?.id ??
          currentUser.classes?.firstOrNull?.id;

      log('asfasfsafsa ${selectedTeacherClass.value?.id}');

      return '&filters[class][id]=$classId';
    }

    return '';
  }

  String get selectedUserStudentFilterSign {
    if (isCurrentUserTeacher) {
      final classId = selectedTeacherClass.value?.id;
      // currentUser.classes?.firstOrNull?.id;

      return '?filters[class][id]=$classId';
    }

    return '';
  }
}
