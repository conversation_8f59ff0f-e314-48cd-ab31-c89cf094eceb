import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';

import '../../../shared/data/remote/api_strings.dart';
import '../../../shared/shared_models/base_media_model.dart';
import 'payment_methods_model.dart';

class NurseryModel extends UserModel {
  final int maxStudents;
  final num fees;
  final DateTime? endDate;
  final PaymentMethodsModel? paymentMethods;

  const NurseryModel({
    super.id,
    super.name,
    super.image,
    this.maxStudents = AppConsts.maxStudents,
    this.fees = 0,
    this.endDate,
    this.paymentMethods,
  });

  factory NurseryModel.fromAttributesJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];

    final logo = attributes != null &&
            attributes.containsKey(ApiStrings.logo) &&
            attributes[ApiStrings.logo][ApiStrings.data] != null
        ? BaseMediaModel.fromJson(
            attributes[ApiStrings.logo][ApiStrings.data][ApiStrings.attributes])
        : null;

    return NurseryModel(
      id: json[ApiStrings.id],
      name: attributes != null ? (attributes[ApiStrings.name] ?? '') : '',
      maxStudents: attributes[ApiStrings.maxStudents] != null
          ? (attributes[ApiStrings.maxStudents])
          : AppConsts.maxStudents,
      fees: attributes[ApiStrings.fees] ?? 0,
      image: logo,
      endDate: attributes[ApiStrings.endDate] != null
          ? DateTime.parse(attributes[ApiStrings.endDate])
          : null,
      paymentMethods: attributes != null
          ? PaymentMethodsModel.fromJson(attributes[ApiStrings.paymentMethods])
          : null,
    );
  }

  factory NurseryModel.fromJson(Map<String, dynamic> attributes) {
    final logo = attributes[ApiStrings.logo] != null
        ? BaseMediaModel.fromJson(attributes[ApiStrings.logo])
        : null;

    return NurseryModel(
      id: attributes[ApiStrings.id],
      maxStudents: attributes[ApiStrings.maxStudents] ?? AppConsts.maxStudents,
      name: attributes.isNotEmpty ? (attributes[ApiStrings.name] ?? '') : '',
      fees: attributes[ApiStrings.fees] ?? 0,
      image: logo,
      endDate: attributes[ApiStrings.endDate] != null
          ? DateTime.parse(attributes[ApiStrings.endDate])
          : null,
      paymentMethods:
          PaymentMethodsModel.fromJson(attributes[ApiStrings.paymentMethods]),
    );
  }

  // to json
  Map<String, dynamic> toDataJson() {
    return {
      ApiStrings.id: id,
      ApiStrings.name: name,
      ApiStrings.logo: image?.toJson(),
      ApiStrings.maxStudents: maxStudents,
      ApiStrings.fees: fees,
      ApiStrings.paymentMethods: paymentMethods?.toJson(),
    };
  }

  @override
  NurseryModel copyWith({
    int? id,
    String? name,
    String? description,
    BaseMediaModel? image,
    String? phone,
    List<ClassModel>? classes,
    UserTypeEnum? userType,
    String? email,
    String? password,
    String? fcmToken,
    List<(int? studentId, int? classId)>? studentIds,
    NurseryModel? nurseryModel,
    // NurseryModel specific fields
    int? maxStudents,
    num? fees,
    DateTime? endDate,
    PaymentMethodsModel? paymentMethods,
    List<String>? accessPermissions,
  }) {
    return NurseryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      image: image ?? this.image,
      maxStudents: maxStudents ?? this.maxStudents,
      fees: fees ?? this.fees,
      endDate: endDate ?? this.endDate,
      paymentMethods: paymentMethods ?? this.paymentMethods,
    );
  }
}
