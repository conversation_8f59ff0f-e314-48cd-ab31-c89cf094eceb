import 'dart:io';
import 'dart:typed_data';

import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/history/model/history_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:connectify_app/src/shared/widgets/video_player/video_player_screen.dart';
import 'package:flutter/material.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/consts/app_constants.dart';
import '../../../../shared/widgets/base_list/base_list.dart';

class HistoryCard extends StatelessWidget {
  final HistoryModel history;
  final String date;

  const HistoryCard({
    super.key,
    required this.history,
    required this.date,
  });

  @override
  Widget build(BuildContext context) {
    return BaseContainer(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Time created
            Row(
              children: [
                Row(
                  children: [
                    Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      history.createdAt?.formatDateToTime ?? '',
                      style: context.greyLabelLarge.copyWith(fontSize: 17),
                    ),
                  ],
                ),

                const SizedBox(width: 8),

                // History type and relations
                Text(
                  '-  ${_getHistoryTypeTitle(context)}',
                  style: context.subTitle.copyWith(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ],
            ),

            const Divider(
              height: 16,
              thickness: 1,
              color: Colors.grey,
            ),

            const SizedBox(height: 8),

            if (history.student != null)
              _buildInfoRow(context, Icons.person,
                  '${context.tr.student}: ${history.student?.name ?? ''}'),

            // Show specific details based on history type
            ..._buildHistoryDetails(
              context,
            ),
          ],
        ),
      ),
    );
  }

  String _getHistoryTypeTitle(BuildContext context) {
    switch (history.historyType) {
      case HistoryType.activity:
        return context.tr.activity;
      case HistoryType.sleep:
        return context.tr.sleep;
      case HistoryType.food:
        return context.tr.food;
      case HistoryType.supply:
        return context.tr.supply;
      case HistoryType.toilet:
        return context.tr.toilet;
      case HistoryType.activityLevel:
        return context.tr.activityLevel;
      case HistoryType.mood:
        return context.tr.mood;
    }
  }

  List<Widget> _buildHistoryDetails(BuildContext context) {
    switch (history.historyType) {
      case HistoryType.activity:
        return [
          if (history.activity != null)
            Builder(builder: (context) {
              final dayNote = history.activity?.notes.lastWhereOrNull(
                (element) => element.date == date,
              );

              return Column(
                children: [
                  _buildInfoRow(context, Icons.directions_run,
                      '${context.tr.activity}: ${history.activity?.activity?.name ?? ''}'),
                  if (dayNote?.note != null)
                    _buildInfoRow(context, Icons.note_alt_sharp,
                        '${context.tr.note}: ${dayNote?.note ?? ''}'),
                  if (dayNote?.media != null && dayNote!.media.isNotEmpty)
                    BaseList.horizontal(
                      height: 100,
                      data: dayNote.media,
                      separatorGap: context.mediumGap,
                      itemBuilder: (media, index) {
                        final isVideo = _isVideoFile(media.url ?? '');
                        final isNetworkMedia =
                            media.url?.contains('http') ?? false;

                        return GestureDetector(
                          onTap: () {
                            if (isVideo) {
                              // Navigate to video player
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) => VideoPlayerScreen(
                                    videoUrl: media.url ?? '',
                                    isNetworkVideo: isNetworkMedia,
                                  ),
                                ),
                              );
                            } else {
                              // Show image dialog
                              showDialog(
                                context: context,
                                builder: (context) => Dialog(
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(
                                        AppRadius.baseContainerRadius),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(
                                        AppRadius.baseContainerRadius),
                                    child: isNetworkMedia
                                        ? Image.network(
                                            media.url ?? '',
                                            errorBuilder:
                                                (context, error, stackTrace) =>
                                                    const BaseCachedImage(
                                              AppConsts.activityPlaceholder,
                                            ),
                                          )
                                        : Image.file(
                                            File(
                                              media.url ?? '',
                                            ),
                                          ),
                                  ),
                                ),
                              );
                            }
                          },
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: SizedBox(
                              width: 100,
                              height: 50,
                              child: Stack(
                                children: [
                                  if (isVideo)
                                    _buildVideoThumbnail(
                                        media.url ?? '', isNetworkMedia)
                                  else
                                    isNetworkMedia
                                        ? BaseCachedImage(
                                            media.url ?? '',
                                            radius: 8,
                                            width: 100,
                                            height: 100,
                                            fit: BoxFit.cover,
                                          )
                                        : Image.file(
                                            File(media.url ?? ''),
                                            fit: BoxFit.cover,
                                            width: 100,
                                            height: 100,
                                          ),
                                  if (isVideo)
                                    const Center(
                                      child: CircleAvatar(
                                        backgroundColor: Colors.black54,
                                        radius: 16,
                                        child: Icon(
                                          Icons.play_arrow,
                                          color: Colors.white,
                                          size: 20,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                ],
              );
            }),
        ];

      case HistoryType.sleep:
        return [
          if (history.sleep != null)
            _buildInfoRow(context, Icons.nightlight_round,
                '${context.tr.sleep}: (${context.tr.from}: ${history.sleep?.sleepStartTime ?? ''}) - (${context.tr.to}: ${history.sleep?.sleepEndTime ?? ''})'),
        ];

      case HistoryType.food:
        return [
          if (history.food != null)
            _buildInfoRow(context, Icons.fastfood,
                '${context.tr.food}: ${history.food?.getMealType() ?? ''} - ${context.tr.amount}: ${history.food?.getMealAmount() ?? ''}'),
        ];

      case HistoryType.supply:
        return [
          if (history.supply != null)
            _buildInfoRow(context, Icons.inventory,
                '${context.tr.supply}: ${history.supply?.name ?? ''}'),
        ];

      case HistoryType.toilet:
        return [
          if (history.toilet != null)
            _buildInfoRow(context, Icons.wc,
                '${context.tr.description}: ${history.toilet?.toiletType?.name ?? ''} ${history.toilet?.toiletWay?.name ?? ''}'),
        ];
      case HistoryType.activityLevel:
        return [
          if (history.activityLevel != null)
            _buildInfoRow(context, Icons.directions_walk,
                '${context.tr.activityLevel}: ${history.activityLevel?.getActivityLevelContextLang(context) ?? ''}'),
          if (history.activityLevel?.note != null &&
              history.activityLevel!.note!.isNotEmpty)
            _buildInfoRow(context, Icons.note_alt,
                '${context.tr.note}: ${history.activityLevel?.note ?? ''}'),
        ];
      case HistoryType.mood:
        return [
          if (history.mood != null)
            _buildInfoRow(context, Icons.sentiment_satisfied,
                '${context.tr.mood}: ${history.mood?.getMoodContextLang(context) ?? ''}'),
          if (history.mood?.note != null && history.mood!.note!.isNotEmpty)
            _buildInfoRow(context, Icons.note_alt,
                '${context.tr.note}: ${history.mood?.note ?? ''}'),
        ];
    }
  }

  Widget _buildInfoRow(BuildContext context, IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Icon(icon, size: 18, color: Colors.grey[700]),
          const SizedBox(width: 8),
          Expanded(
            child: Text(text),
          ),
        ],
      ),
    );
  }

  bool _isVideoFile(String url) {
    final videoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.m4v'];
    return videoExtensions.any((ext) => url.toLowerCase().endsWith(ext));
  }

  Widget _buildVideoThumbnail(String videoUrl, bool isNetworkVideo) {
    if (isNetworkVideo) {
      return FutureBuilder<Uint8List?>(
        future: _generateNetworkVideoThumbnail(videoUrl),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return _buildVideoPlaceholder(isLoading: true);
          }
          if (snapshot.hasData && snapshot.data != null) {
            return Image.memory(
              snapshot.data!,
              fit: BoxFit.cover,
              width: double.infinity,
              height: double.infinity,
            );
          }
          return _buildVideoPlaceholder();
        },
      );
    }
    return _buildVideoPlaceholder();
  }

  Widget _buildVideoPlaceholder({bool isLoading = false}) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.grey[300],
      child: isLoading
          ? const Center(
              child: CircularProgressIndicator(
                strokeWidth: 2,
              ),
            )
          : const Icon(
              Icons.videocam,
              size: 48,
              color: Colors.grey,
            ),
    );
  }

  Future<Uint8List?> _generateNetworkVideoThumbnail(String videoUrl) async {
    try {
      return await VideoThumbnail.thumbnailData(
        video: videoUrl,
        imageFormat: ImageFormat.JPEG,
        quality: 75,
      );
    } catch (e) {
      Log.e('Error generating network video thumbnail: $e');
      return null;
    }
  }
}
