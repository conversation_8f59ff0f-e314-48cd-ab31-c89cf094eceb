import 'package:connectify_app/src/screens/evaluation/model/evaluation_model.dart';
import 'package:connectify_app/src/screens/evaluation/view/widgets/evaluation_question_card.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class EvaluationQuestionExpansionCard extends StatefulWidget {
  final EvaluationModel evaluation;

  const EvaluationQuestionExpansionCard({
    super.key,
    required this.evaluation,
  });

  @override
  State<EvaluationQuestionExpansionCard> createState() =>
      _EvaluationQuestionExpansionCardState();
}

class _EvaluationQuestionExpansionCardState
    extends State<EvaluationQuestionExpansionCard> {
  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return BaseContainer(
      child: Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          initiallyExpanded: isExpanded,
          onExpansionChanged: (expanded) {
            setState(() {
              isExpanded = expanded;
            });
          },
          tilePadding: EdgeInsets.zero,
          childrenPadding: const EdgeInsets.only(
            top: AppSpaces.smallPadding,
            bottom: AppSpaces.smallPadding,
          ),
          leading: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: ColorManager.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.quiz_outlined,
              color: ColorManager.primaryColor,
              size: 20,
            ),
          ),
          title: Row(
            children: [
              const Icon(
                Icons.calendar_today_outlined,
                size: 16,
                color: ColorManager.darkGrey,
              ),
              context.xSmallGap,
              Text(
                widget.evaluation.createdAt?.formatDateToString ??
                    (widget.evaluation.date.isNotEmpty
                        ? widget.evaluation.date
                        : context.tr.noDate),
                style: context.labelLarge.copyWith(
                  color: ColorManager.black,
                ),
              ),
            ],
          ),
          subtitle: Padding(
            padding: const EdgeInsets.only(top: AppSpaces.smallPadding + 2),
            child: Wrap(
              spacing: 8,
              runSpacing: 4,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: ColorManager.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    '${widget.evaluation.questions.length} ${context.tr.questions}',
                    style: context.labelSmall.copyWith(
                      color: ColorManager.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (widget.evaluation.classes != null &&
                    widget.evaluation.classes!.isNotEmpty) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      widget.evaluation.classes!.length == 1
                          ? widget.evaluation.classes!.first.name
                          : '${widget.evaluation.classes!.length} ${context.tr.classes}',
                      style: context.labelSmall.copyWith(
                        color: Colors.blue,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    widget.evaluation.sendTo == SendTo.allParents
                        ? context.tr.allParents
                        : context.tr.specificClasses,
                    style: context.labelSmall.copyWith(
                      color: Colors.green,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          trailing: Icon(
            isExpanded ? Icons.expand_less : Icons.expand_more,
            color: ColorManager.primaryColor,
          ),
          children: [
            // Classes Information
            if (widget.evaluation.classes != null &&
                widget.evaluation.classes!.isNotEmpty) ...[
              _buildClassesInfo(context),
              context.mediumGap,
            ],

            // Questions Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppSpaces.mediumPadding),
              decoration: BoxDecoration(
                color: ColorManager.grey.withOpacity(0.3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.tr.questions,
                    style: context.labelLarge.copyWith(
                      fontWeight: FontWeight.bold,
                      color: ColorManager.primaryColor,
                    ),
                  ),
                  context.mediumGap,
                  ...widget.evaluation.questions.asMap().entries.map((entry) {
                    final questionIndex = entry.key;
                    final question = entry.value;

                    return Padding(
                      padding: EdgeInsets.only(
                        bottom: questionIndex <
                                widget.evaluation.questions.length - 1
                            ? AppSpaces.mediumPadding
                            : 0,
                      ),
                      child: EvaluationQuestionCard(
                        question: question,
                        questionNumber: questionIndex + 1,
                      ),
                    );
                  }),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
      BuildContext context, String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: ColorManager.darkGrey,
        ),
        context.smallGap,
        Text(
          '$label: ',
          style: context.labelMedium.copyWith(
            fontWeight: FontWeight.w600,
            color: ColorManager.darkGrey,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: context.labelMedium,
          ),
        ),
      ],
    );
  }

  Widget _buildClassesInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(
              Icons.class_outlined,
              size: 16,
              color: ColorManager.darkGrey,
            ),
            context.smallGap,
            Text(
              '${context.tr.classes}: ',
              style: context.labelMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: ColorManager.darkGrey,
              ),
            ),
          ],
        ),
        context.smallGap,
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: widget.evaluation.classes!.map((classModel) {
            return Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                  color: Colors.blue.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Text(
                classModel.name,
                style: context.labelSmall.copyWith(
                  color: Colors.blue,
                  fontWeight: FontWeight.w600,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}
