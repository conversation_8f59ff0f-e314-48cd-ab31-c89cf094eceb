import 'package:connectify_app/src/screens/evaluation/model/evaluation_question_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:xr_helper/xr_helper.dart';

class EvaluationQuestionCard extends StatelessWidget {
  final EvaluationQuestionModel question;
  final int questionNumber;

  const EvaluationQuestionCard({
    super.key,
    required this.question,
    required this.questionNumber,
  });

  @override
  Widget build(BuildContext context) {
    return BaseContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Question Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: ColorManager.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${context.tr.question} $questionNumber',
                  style: context.labelMedium.copyWith(
                    color: ColorManager.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const Spacer(),
              _buildQuestionTypeChip(context),
            ],
          ),

          context.mediumGap,

          // Question Text
          Text(
            question.question,
            style: context.subTitle.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),

          context.mediumGap,
        ],
      ),
    );
  }

  Widget _buildQuestionTypeChip(BuildContext context) {
    final typeText = question.type == QuestionType.rating
        ? context.tr.rating
        : context.tr.text;

    final chipColor =
        question.type == QuestionType.rating ? Colors.orange : Colors.blue;

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: chipColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Text(
        typeText,
        style: context.labelSmall.copyWith(
          color: chipColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
