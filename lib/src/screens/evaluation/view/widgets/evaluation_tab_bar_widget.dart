import 'package:connectify_app/src/screens/evaluation/controller/evaluation_tab_bar_controller.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class EvaluationTabBarWidget extends ConsumerWidget {
  const EvaluationTabBarWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final evaluationTabBarCtrl = ref.watch(evaluationTabBarController);
    final selectedIndex = ref.watch(evaluationTabBarControllerProvider);

    final List<String> tabs = [
      context.tr.answers,
      context.tr.questions,
    ];

    return Container(
      margin: const EdgeInsets.all(AppSpaces.mediumPadding),
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: ColorManager.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: ColorManager.grey.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: tabs.asMap().entries.map((entry) {
          final index = entry.key;
          final tabTitle = entry.value;
          final isSelected = selectedIndex == index;

          return Expanded(
            child: GestureDetector(
              onTap: () => evaluationTabBarCtrl.changeIndex(index),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeInOut,
                padding: EdgeInsets.symmetric(
                  horizontal: AppSpaces.mediumPadding,
                  vertical: 10.h,
                ),
                margin: EdgeInsets.symmetric(
                  horizontal: AppSpaces.mediumPadding,
                  vertical: 2.h,
                ),
                decoration: BoxDecoration(
                  color: isSelected
                      ? ColorManager.primaryColor
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: isSelected
                      ? [
                          BoxShadow(
                            color: ColorManager.primaryColor.withOpacity(0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ]
                      : null,
                ),
                child: Center(
                  child: Text(
                    tabTitle,
                    style: isSelected
                        ? context.whiteLabelLarge.copyWith(
                            fontWeight: FontWeight.w600,
                          )
                        : context.labelLarge.copyWith(
                            color: ColorManager.darkGrey.withOpacity(0.7),
                            fontWeight: FontWeight.w500,
                          ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
