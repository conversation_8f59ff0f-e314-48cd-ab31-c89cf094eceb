import 'package:connectify_app/src/screens/evaluation/model/evaluation_answer_model.dart';
import 'package:connectify_app/src/screens/evaluation/model/evaluation_question_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:xr_helper/xr_helper.dart';

class EvaluationAnswerExpansionCard extends StatefulWidget {
  final EvaluationAnswerModel evaluationAnswer;

  const EvaluationAnswerExpansionCard({
    super.key,
    required this.evaluationAnswer,
  });

  @override
  State<EvaluationAnswerExpansionCard> createState() =>
      _EvaluationAnswerExpansionCardState();
}

class _EvaluationAnswerExpansionCardState
    extends State<EvaluationAnswerExpansionCard> {
  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return BaseContainer(
      child: Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          initiallyExpanded: isExpanded,
          onExpansionChanged: (expanded) {
            setState(() {
              isExpanded = expanded;
            });
          },
          tilePadding: EdgeInsets.zero,
          childrenPadding: const EdgeInsets.only(
            top: AppSpaces.smallPadding,
            bottom: AppSpaces.smallPadding,
          ),
          leading: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: ColorManager.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.person_outline,
              color: ColorManager.primaryColor,
              size: 20,
            ),
          ),
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.evaluationAnswer.studentName.isEmpty
                    ? (widget.evaluationAnswer.parent?.name ?? '')
                    : widget.evaluationAnswer.studentName,
                style: context.subTitle.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              context.xSmallGap,
              Row(
                children: [
                  const Icon(
                    Icons.calendar_today_outlined,
                    size: 14,
                    color: ColorManager.darkGrey,
                  ),
                  context.xSmallGap,
                  Text(
                    widget.evaluationAnswer.createdAt?.formatDateToString ??
                        context.tr.noDate,
                    style: context.labelMedium.copyWith(
                      color: ColorManager.darkGrey,
                    ),
                  ),
                ],
              ),
            ],
          ),
          subtitle: Padding(
            padding: const EdgeInsets.only(top: AppSpaces.smallPadding),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: ColorManager.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    '${widget.evaluationAnswer.answers.length} ${context.tr.answers}',
                    style: context.labelSmall.copyWith(
                      color: ColorManager.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (widget.evaluationAnswer.classModel != null) ...[
                  context.smallGap,
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      widget.evaluationAnswer.classModel!.name,
                      style: context.labelSmall.copyWith(
                        color: Colors.blue,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
          trailing: Icon(
            isExpanded ? Icons.expand_less : Icons.expand_more,
            color: ColorManager.primaryColor,
          ),
          children: [
            // Answers Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppSpaces.mediumPadding),
              decoration: BoxDecoration(
                color: ColorManager.grey.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.tr.answers,
                    style: context.labelLarge.copyWith(
                      fontWeight: FontWeight.bold,
                      color: ColorManager.primaryColor,
                    ),
                  ),
                  context.mediumGap,
                  ...widget.evaluationAnswer.answers.map((questionAnswer) {
                    return _buildQuestionAnswerItem(context, questionAnswer);
                  }),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
      BuildContext context, String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: ColorManager.darkGrey,
        ),
        context.smallGap,
        Text(
          '$label: ',
          style: context.labelMedium.copyWith(
            fontWeight: FontWeight.w600,
            color: ColorManager.darkGrey,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: context.labelMedium,
          ),
        ),
      ],
    );
  }

  Widget _buildQuestionAnswerItem(
      BuildContext context, EvaluationQuestionModel questionAnswer) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpaces.mediumPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Question
          Text(
            questionAnswer.question,
            style: context.subTitle.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          context.smallGap,
          // Answer based on type
          if (questionAnswer.type == QuestionType.rating &&
              questionAnswer.rate != null)
            _buildRatingAnswer(context, questionAnswer.rate!)
          else if (questionAnswer.type == QuestionType.text &&
              questionAnswer.answer != null)
            _buildTextAnswer(context, questionAnswer.answer!),
        ],
      ),
    );
  }

  Widget _buildRatingAnswer(BuildContext context, double rate) {
    return Row(
      children: [
        RatingBar.builder(
          initialRating: rate,
          minRating: 1,
          direction: Axis.horizontal,
          allowHalfRating: false,
          itemCount: 5,
          itemSize: 20,
          ignoreGestures: true,
          itemBuilder: (context, _) => const Icon(
            Icons.star,
            color: Colors.amber,
          ),
          onRatingUpdate: (rating) {},
        ),
        context.smallGap,
        Text(
          '(${rate.toInt()}/5)',
          style: context.labelMedium.copyWith(
            color: ColorManager.darkGrey,
          ),
        ),
      ],
    );
  }

  Widget _buildTextAnswer(BuildContext context, String answer) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppSpaces.mediumPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: ColorManager.grey.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Text(
        answer,
        style: context.labelMedium,
      ),
    );
  }
}
