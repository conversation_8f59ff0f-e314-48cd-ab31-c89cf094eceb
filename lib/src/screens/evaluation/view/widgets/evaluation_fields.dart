import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/evaluation/model/evaluation_model.dart';
import 'package:connectify_app/src/screens/evaluation/model/evaluation_question_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/drop_downs/class_drop_down.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

class EvaluationFields extends HookWidget {
  final ValueNotifier<String?> selectedMonth;
  final ValueNotifier<SendTo> selectedSendTo;
  final ValueNotifier<List<ClassModel>?> selectedClasses;
  final ValueNotifier<List<EvaluationQuestionModel>> questions;

  const EvaluationFields({
    super.key,
    required this.selectedMonth,
    required this.selectedSendTo,
    required this.selectedClasses,
    required this.questions,
  });

  @override
  Widget build(BuildContext context) {
    // Generate months for current year
    final currentYear = DateTime.now().year;
    final months = List.generate(12, (index) {
      final month = index + 1;
      final monthName = _getMonthName(context, month);
      return {
        'value': '$currentYear-${month.toString().padLeft(2, '0')}',
        'name': '$monthName $currentYear',
      };
    });

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Month selection
          Text(
            context.tr.selectMonth,
            style: context.labelLarge,
          ),
          context.smallGap,
          ValueListenableBuilder<String?>(
            valueListenable: selectedMonth,
            builder: (context, month, child) {
              return BaseSearchDropDown(
                label: context.tr.selectMonth,
                data: months,
                itemModelAsName: (item) =>
                    (item as Map<String, String>)['name']!,
                selectedValue: months.firstWhere(
                  (m) => m['value'] == month,
                  orElse: () => months.first,
                ),
                isEng: context.isEng,
                onChanged: (value) {
                  final selected = value as Map<String, String>;
                  selectedMonth.value = selected['value'];
                },
              );
            },
          ),

          context.fieldsGap,

          // Send To selection
          Text(
            context.tr.sendTo,
            style: context.labelLarge,
          ),
          context.smallGap,
          ValueListenableBuilder<SendTo>(
            valueListenable: selectedSendTo,
            builder: (context, sendTo, child) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  BaseDropDown(
                    data: SendTo.values,
                    asString: (sendTo) => context.isEng
                        ? (sendTo as SendTo).displayName
                        : (sendTo as SendTo).displayNameAr,
                    selectedValue: sendTo,
                    onChanged: (value) {
                      selectedSendTo.value = value as SendTo;
                      if (value != SendTo.classes) {
                        selectedClasses.value = null;
                      }
                    },
                  ),

                  // Show class selection when sendTo is class
                  if (sendTo == SendTo.classes) ...[
                    context.mediumGap,
                    MultiClassDropDown(
                      selectedClasses: selectedClasses,
                    ),
                  ],
                ],
              );
            },
          ),

          context.fieldsGap,

          // Questions section
          Text(
            context.tr.evaluationQuestions,
            style: context.labelLarge,
          ),
          context.smallGap,

          ValueListenableBuilder<List<EvaluationQuestionModel>>(
            valueListenable: questions,
            builder: (context, questionsList, child) {
              return Column(
                children: [
                  ...questionsList.asMap().entries.map((entry) {
                    final index = entry.key;
                    final question = entry.value;
                    return _buildQuestionField(
                        context, question, index, questionsList.length);
                  }),

                  context.mediumGap,

                  // Add Question button
                  Button(
                    onPressed: () {
                      final newQuestions =
                          List<EvaluationQuestionModel>.from(questions.value);
                      newQuestions.add(const EvaluationQuestionModel());
                      questions.value = newQuestions;
                    },
                    label: '+ ${context.tr.addQuestion}',
                    color: ColorManager.primaryColor,
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionField(BuildContext context,
      EvaluationQuestionModel question, int index, int totalQuestions) {
    return HookBuilder(
        key: ValueKey(
            'question_$index\_$totalQuestions'), // Add key to force rebuild when list changes
        builder: (context) {
          final questionController =
              useTextEditingController(text: question.question);
          final selectedType = useState<QuestionType>(question.type);

          // Update question when text changes
          useEffect(() {
            void listener() {
              final newQuestions =
                  List<EvaluationQuestionModel>.from(questions.value);
              if (index < newQuestions.length) {
                // Safety check
                newQuestions[index] = question.copyWith(
                  question: questionController.text,
                  type: selectedType.value,
                );
                questions.value = newQuestions;
              }
            }

            questionController.addListener(listener);
            return () => questionController.removeListener(listener);
          }, [questionController, index]); // Add index to dependencies

          return Container(
            margin: const EdgeInsets.only(bottom: AppSpaces.mediumPadding),
            padding: const EdgeInsets.all(AppSpaces.mediumPadding),
            decoration: BoxDecoration(
              border: Border.all(color: ColorManager.grey),
              borderRadius:
                  BorderRadius.circular(AppRadius.baseContainerRadius),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        '${context.tr.question} ${index + 1}',
                        style: context.subTitle,
                      ),
                    ),
                    if (questions.value.length > 1)
                      IconButton(
                        icon: const Icon(Icons.delete, color: Colors.red),
                        onPressed: () {
                          final newQuestions =
                              List<EvaluationQuestionModel>.from(
                                  questions.value);
                          newQuestions.removeAt(index);
                          questions.value = newQuestions;
                        },
                      ),
                  ],
                ),

                context.smallGap,

                // Question text field
                BaseTextField(
                  controller: questionController,
                  title: context.tr.question,
                  textInputType: TextInputType.multiline,
                  maxLines: 3,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return context.tr.thisFieldIsRequired;
                    }
                    return null;
                  },
                ),

                context.smallGap,

                // Question type dropdown
                Text(
                  context.tr.questionType,
                  style: context.labelMedium,
                ),
                context.smallGap,
                ValueListenableBuilder<QuestionType>(
                  valueListenable: selectedType,
                  builder: (context, type, child) {
                    return BaseDropDown(
                      data: QuestionType.values,
                      asString: (type) => context.isEng
                          ? (type as QuestionType).displayName
                          : (type as QuestionType).displayNameAr,
                      selectedValue: type,
                      onChanged: (value) {
                        selectedType.value = value as QuestionType;
                        final newQuestions =
                            List<EvaluationQuestionModel>.from(questions.value);
                        if (index < newQuestions.length) {
                          // Safety check
                          newQuestions[index] = question.copyWith(
                            question: questionController.text,
                            type: value,
                          );
                          questions.value = newQuestions;
                        }
                      },
                    );
                  },
                ),
              ],
            ),
          );
        });
  }

  String _getMonthName(BuildContext context, int month) {
    switch (month) {
      case 1:
        return context.tr.january;
      case 2:
        return context.tr.february;
      case 3:
        return context.tr.march;
      case 4:
        return context.tr.april;
      case 5:
        return context.tr.may;
      case 6:
        return context.tr.june;
      case 7:
        return context.tr.july;
      case 8:
        return context.tr.august;
      case 9:
        return context.tr.september;
      case 10:
        return context.tr.october;
      case 11:
        return context.tr.november;
      case 12:
        return context.tr.december;
      default:
        return '';
    }
  }
}
