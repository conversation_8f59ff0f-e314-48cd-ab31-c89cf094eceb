import 'package:connectify_app/src/screens/evaluation/model/evaluation_model.dart';
import 'package:connectify_app/src/screens/evaluation/view/widgets/evaluation_question_expansion_card.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_list/base_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class EvaluationQuestionsTabScreen extends ConsumerWidget {
  final List<EvaluationModel> evaluationsData;

  const EvaluationQuestionsTabScreen({
    super.key,
    required this.evaluationsData,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return BaseList(
      physics: const NeverScrollableScrollPhysics(),
      data: evaluationsData,
      separatorGap: context.mediumGap,
      itemBuilder: (evaluation, index) {
        return EvaluationQuestionExpansionCard(
          evaluation: evaluation,
        );
      },
      mainAxisSpacing: 15.h,
      crossAxisSpacing: 10,
    );
  }
}
