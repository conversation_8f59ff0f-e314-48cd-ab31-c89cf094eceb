import 'dart:developer';

import 'package:connectify_app/src/screens/evaluation/model/evaluation_answer_model.dart';
import 'package:connectify_app/src/screens/evaluation/model/evaluation_model.dart';
import 'package:connectify_app/src/screens/evaluation/repo/evaluation_repo.dart';
import 'package:connectify_app/src/screens/evaluation/view/evaluations_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../notification/model/notification_model.dart';
import '../../notification/repo/notification_repo.dart';
import '../../nursery/models/nursery_model_helper.dart';
import '../../student/models/student_model.dart';

//? Evaluation Repository Provider ========================================================
final evaluationRepoProvider = Provider<EvaluationRepo>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);
  return EvaluationRepo(networkApiService);
});

//? Evaluation Controller Provider ========================================================
final evaluationChangeNotifierControllerProvider =
    ChangeNotifierProvider.family<EvaluationController, BuildContext>(
        (ref, context) {
  final evaluationRepo = ref.watch(evaluationRepoProvider);
  return EvaluationController(context, evaluationRepo);
});

//? Get Evaluations Data Provider ========================================================
final getEvaluationDataProvider =
    FutureProvider.family<List<EvaluationModel>, BuildContext>((ref, context) {
  final evaluationRepo = ref.watch(evaluationRepoProvider);
  return evaluationRepo.getEvaluations();
});

//? Get Evaluation Answers Data Provider ========================================================
final getEvaluationAnswerDataProvider =
    FutureProvider.family<List<EvaluationAnswerModel>, BuildContext>(
        (ref, context) {
  final evaluationRepo = ref.watch(evaluationRepoProvider);
  return evaluationRepo.getEvaluationAnswers();
});

class EvaluationController extends BaseVM {
  final BuildContext context;
  final EvaluationRepo evaluationRepo;

  EvaluationController(this.context, this.evaluationRepo);

  //? Get Evaluations ========================================================
  Future<void> getEvaluations() async {
    return await baseFunction(context, () async {
      await evaluationRepo.getEvaluations();
    });
  }

  //? Add Evaluation ========================================================
  Future<void> addEvaluation({
    required EvaluationModel evaluation,
    required List<StudentModel> students,
  }) async {
    return await baseFunction(context, () async {

      await evaluationRepo.addEvaluation(evaluation: evaluation);

      if (evaluation.sendTo == SendTo.classes) {
        for (var student in students) {
          NotificationService.sendNotification(
            title: "New Evaluation",
            body: "New evaluation has been added for ${student.name}.",
            userTokenOrTopic:
                NurseryModelHelper.parentByStudentTopic(student.id),
            isTopic: true,
          );

          postNewNotification(
              notificationModel: NotificationModel(
            title: "New Evaluation",
            body: "New evaluation has been added for ${student.name}.",
            topic: NurseryModelHelper.parentByStudentTopic(student.id),
          ));
        }
      } else {
        NotificationService.sendNotification(
          title: "New Evaluation",
          body: "New evaluation has been added.",
          userTokenOrTopic: NurseryModelHelper.allParentTopic(),
          isTopic: true,
        );

        postNewNotification(
            notificationModel: NotificationModel(
          title: "New Evaluation",
          body: "New evaluation has been added.",
          topic: NurseryModelHelper.allParentTopic(),
        ));
      }
      context.showBarMessage(context.tr.addedSuccessfully);
      context.to(const EvaluationsScreen());
    });
  }

  //? Edit Evaluation ========================================================
  Future<void> editEvaluation({
    required int id,
    required EvaluationModel evaluation,
  }) async {
    return await baseFunction(context, () async {
      await evaluationRepo.editEvaluation(id: id, evaluation: evaluation);

      context.showBarMessage(context.tr.editedSuccessfully);
      context.to(const EvaluationsScreen());
    });
  }

  //? Delete Evaluation ========================================================
  Future<void> deleteEvaluation({required int id}) async {
    return await baseFunction(context, () async {
      await evaluationRepo.deleteEvaluation(id: id);

      context.showBarMessage(context.tr.deletedSuccessfully);
    });
  }

  //? Get Evaluation Answers ========================================================
  Future<void> getEvaluationAnswers() async {
    return await baseFunction(context, () async {
      await evaluationRepo.getEvaluationAnswers();
    });
  }

  //? Add Evaluation Answer ========================================================
  Future<void> addEvaluationAnswer({
    required EvaluationAnswerModel evaluationAnswer,
  }) async {
    return await baseFunction(context, () async {
      await evaluationRepo.addEvaluationAnswer(
          evaluationAnswer: evaluationAnswer);

      context.showBarMessage(context.tr.addedSuccessfully);
    });
  }

  //? Edit Evaluation Answer ========================================================
  Future<void> editEvaluationAnswer({
    required int id,
    required EvaluationAnswerModel evaluationAnswer,
  }) async {
    return await baseFunction(context, () async {
      await evaluationRepo.editEvaluationAnswer(
          id: id, evaluationAnswer: evaluationAnswer);

      context.showBarMessage(context.tr.editedSuccessfully);
    });
  }

  //? Delete Evaluation Answer ========================================================
  Future<void> deleteEvaluationAnswer({required int id}) async {
    return await baseFunction(context, () async {
      await evaluationRepo.deleteEvaluationAnswer(id: id);

      context.showBarMessage(context.tr.deletedSuccessfully);
    });
  }
}
