import 'dart:io';

import 'package:connectify_app/src/screens/evaluation/model/evaluation_answer_model.dart';
import 'package:connectify_app/src/screens/evaluation/model/evaluation_question_model.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

import 'package:pdf/pdf.dart';
import 'package:pdf/src/widgets/font.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:flutter/services.dart';

bool isWrittenTextHaveAnyArabicLetter(String? text) {
  if (text == null) return false;

  final arabicRegex = RegExp(r'[\u0600-\u06FF]');
  return arabicRegex.hasMatch(text);
}

baseText(
  String text, {
  required Font arabicFont,
  bool isBold = false,
  bool isLtr = false,
  PdfColor? color,
}) {
  return pw.Padding(
    padding: const pw.EdgeInsets.all(2),
    child: pw.Text(
      text,
      textScaleFactor: 1,
      textAlign: pw.TextAlign.center,
      textDirection: pw.TextDirection.rtl,
      style: pw.TextStyle(
        font: arabicFont,
        fontWeight: isBold ? pw.FontWeight.bold : pw.FontWeight.normal,
        color: color,
      ),
    ),
  );
}

class PdfExportService {
  static Future<void> exportEvaluationReport({
    required List<EvaluationAnswerModel> evaluationAnswers,
    required String title,
  }) async {
    try {
      final pdf = pw.Document();
      final arabicFont = Font.ttf(
        await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf"),
      );

      // Limit the number of answers to prevent memory issues
      const int maxAnswers = 100; // Maximum 100 answers per PDF
      final limitedAnswers = evaluationAnswers.length > maxAnswers
          ? evaluationAnswers.take(maxAnswers).toList()
          : evaluationAnswers;

      // Handle large datasets by creating individual pages
      const int itemsPerPage = 1; // Use 1 item per page for maximum safety

      // Add content using MultiPage for proper pagination
      if (limitedAnswers.isEmpty) {
        // Empty state page
        pdf.addPage(
          pw.Page(
            pageFormat: PdfPageFormat.a4,
            margin: const pw.EdgeInsets.all(32),
            build: (pw.Context context) {
              return pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  _buildHeaderWithTotal(
                      title, evaluationAnswers.length, maxAnswers, arabicFont),
                  pw.SizedBox(height: 40),
                  pw.Expanded(
                    child: pw.Center(
                      child: pw.Text(
                        'No evaluation answers found.',
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        );
      } else {
        // Use MultiPage for automatic pagination with header only on first page
        pdf.addPage(
          pw.MultiPage(
            pageFormat: PdfPageFormat.a4,
            margin: const pw.EdgeInsets.all(32),
            maxPages: 200, // Reasonable limit
            header: (pw.Context context) {
              // Header only on first page
              if (context.pageNumber == 1) {
                return pw.Column(
                  children: [
                    _buildHeaderWithTotal(title, evaluationAnswers.length,
                        maxAnswers, arabicFont),
                    pw.SizedBox(height: 20),
                  ],
                );
              }
              return pw.SizedBox.shrink();
            },
            footer: (pw.Context context) {
              return pw.Container(
                alignment: pw.Alignment.centerRight,
                margin: const pw.EdgeInsets.only(top: 10),
                child: pw.Text(
                  'Page ${context.pageNumber}',
                  style: pw.TextStyle(
                    font: arabicFont,
                    fontSize: 10,
                    color: PdfColors.grey,
                  ),
                ),
              );
            },
            build: (pw.Context context) {
              return limitedAnswers
                  .map((answer) => pw.Container(
                        margin: const pw.EdgeInsets.only(bottom: 20),
                        child: _buildAnswerSection(answer, arabicFont),
                      ))
                  .toList();
            },
          ),
        );
      }

      // Save PDF to device
      final output = await getTemporaryDirectory();
      final file = File(
          '${output.path}/evaluation_report_${DateTime.now().millisecondsSinceEpoch}.pdf');
      await file.writeAsBytes(await pdf.save());

      // Open PDF automatically
      await OpenFilex.open(file.path);
    } catch (e) {
      throw Exception('Failed to export PDF: $e');
    }
  }

  static pw.Widget _buildHeader(String title) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          title,
          style: pw.TextStyle(
            fontSize: 24,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 10),
        pw.Text(
          'Generated on: ${DateTime.now().toString().split('.')[0]}',
          style: const pw.TextStyle(
            fontSize: 12,
            color: PdfColors.grey,
          ),
        ),
        pw.Divider(thickness: 2),
      ],
    );
  }

  static pw.Widget _buildHeaderWithTotal(
      String title, int totalAnswers, int maxAnswers, pw.Font arabicFont) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Title and total responses in same row
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          crossAxisAlignment: pw.CrossAxisAlignment.center,
          children: [
            pw.Expanded(
              child: pw.Text(
                title,
                style: pw.TextStyle(
                  fontSize: 24,
                  fontWeight: pw.FontWeight.bold,
                  font: arabicFont,
                ),
              ),
            ),
            pw.Text(
              'Total Responses: $totalAnswers${totalAnswers > maxAnswers ? ' (Showing first $maxAnswers)' : ''}',
              style: pw.TextStyle(
                font: arabicFont,
                fontSize: 14,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.blue,
              ),
            ),
          ],
        ),
        pw.SizedBox(height: 10),
        pw.Text(
          'Generated on: ${DateTime.now().toString().split('.')[0]}',
          style: pw.TextStyle(
            fontSize: 12,
            color: PdfColors.grey,
            font: arabicFont,
          ),
        ),
        pw.Divider(thickness: 2),
      ],
    );
  }

  static pw.Widget _buildContent(
      List<EvaluationAnswerModel> evaluationAnswers, pw.Font arabicFont) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        if (evaluationAnswers.isEmpty)
          pw.Text(
            'No evaluation answers found.',
            style: const pw.TextStyle(fontSize: 14),
          )
        else
          ...evaluationAnswers
              .map((answer) => _buildAnswerSection(answer, arabicFont)),
      ],
    );
  }

  static pw.Widget _buildAnswerSection(
      EvaluationAnswerModel answer, pw.Font arabicFont) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 20),
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // Header with parent and student info
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'Parent: ${answer.parent?.name ?? 'Unknown'}',
                    style: pw.TextStyle(
                      fontSize: 16,
                      fontWeight: pw.FontWeight.bold,
                      font: arabicFont,
                    ),
                    textDirection:
                        isWrittenTextHaveAnyArabicLetter(answer.parent?.name)
                            ? pw.TextDirection.rtl
                            : pw.TextDirection.ltr,
                    textAlign:
                        isWrittenTextHaveAnyArabicLetter(answer.parent?.name)
                            ? pw.TextAlign.right
                            : pw.TextAlign.left,
                  ),
                  pw.SizedBox(height: 4),
                  pw.Text(
                    'Student: ${answer.studentName}',
                    style: pw.TextStyle(
                      fontSize: 14,
                      color: PdfColors.grey700,
                      font: arabicFont,
                    ),
                    textDirection:
                        isWrittenTextHaveAnyArabicLetter(answer.studentName)
                            ? pw.TextDirection.rtl
                            : pw.TextDirection.ltr,
                    textAlign:
                        isWrittenTextHaveAnyArabicLetter(answer.studentName)
                            ? pw.TextAlign.right
                            : pw.TextAlign.left,
                  ),
                ],
              ),
              if (answer.createdAt != null)
                pw.Text(
                  answer.createdAt!.toString().split(' ')[0],
                  style: const pw.TextStyle(
                    fontSize: 12,
                    color: PdfColors.grey,
                  ),
                ),
            ],
          ),

          pw.SizedBox(height: 16),
          pw.Divider(),
          pw.SizedBox(height: 12),

          // Questions and answers
          ...answer.answers.map((qa) => _buildQuestionAnswer(qa, arabicFont)),
        ],
      ),
    );
  }

  static pw.Widget _buildQuestionAnswer(
      EvaluationQuestionModel questionAnswer, pw.Font arabicFont) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 8),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        mainAxisSize: pw.MainAxisSize.min,
        children: [
          // Question (full text, pagination will handle overflow)
          pw.Text(
            questionAnswer.question,
            style: pw.TextStyle(
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
              font: arabicFont,
            ),
            textDirection:
                isWrittenTextHaveAnyArabicLetter(questionAnswer.question)
                    ? pw.TextDirection.rtl
                    : pw.TextDirection.ltr,
            textAlign: isWrittenTextHaveAnyArabicLetter(questionAnswer.question)
                ? pw.TextAlign.right
                : pw.TextAlign.left,
          ),

          pw.SizedBox(height: 4),

          // Answer based on type (compact)
          if (questionAnswer.type == QuestionType.rating &&
              questionAnswer.rate != null)
            _buildRatingAnswer(questionAnswer.rate!)
          else if (questionAnswer.type == QuestionType.text &&
              questionAnswer.answer != null)
            _buildTextAnswer(questionAnswer.answer!, arabicFont),
        ],
      ),
    );
  }

  static pw.Widget _buildRatingAnswer(double rating) {
    return pw.Row(
      children: [
        pw.Text('Rating: ', style: const pw.TextStyle(fontSize: 12)),
        // ...List.generate(5, (index) {
        //   return pw.Container(
        //     margin: const pw.EdgeInsets.only(right: 2),
        //     child: pw.Text(
        //       index < rating ? '★' : '☆',
        //       style: pw.TextStyle(
        //         fontSize: 16,
        //         color: index < rating ? PdfColors.amber : PdfColors.grey,
        //       ),
        //     ),
        //   );
        // }),
        pw.Text('(${rating.toStringAsFixed(1)}/5)',
            style: const pw.TextStyle(fontSize: 12)),
      ],
    );
  }

  static pw.Widget _buildTextAnswer(String answer, pw.Font arabicFont) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(6),
      ),
      child: pw.Text(
        answer,
        style: pw.TextStyle(
          fontSize: 11,
          font: arabicFont,
        ),
        textDirection: isWrittenTextHaveAnyArabicLetter(answer)
            ? pw.TextDirection.rtl
            : pw.TextDirection.ltr,
        textAlign: isWrittenTextHaveAnyArabicLetter(answer)
            ? pw.TextAlign.right
            : pw.TextAlign.left,
      ),
    );
  }
}
