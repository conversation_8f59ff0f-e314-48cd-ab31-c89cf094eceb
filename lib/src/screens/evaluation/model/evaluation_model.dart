import 'dart:developer';

import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/evaluation/model/evaluation_question_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:equatable/equatable.dart';

List<EvaluationModel> responseToEvaluationModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final evaluations = data.map((e) => EvaluationModel.fromJson(e)).toList();

  return evaluations;
}

enum SendTo {
  allParents,
  classes,
}

extension SendToExtension on SendTo {
  String get value {
    switch (this) {
      case SendTo.allParents:
        return 'all_parents';
      case SendTo.classes:
        return 'class';
    }
  }

  String get displayName {
    switch (this) {
      case SendTo.allParents:
        return 'All Parents';
      case SendTo.classes:
        return 'Specific Classes';
    }
  }

  String get displayNameAr {
    switch (this) {
      case SendTo.allParents:
        return 'جميع الأهالي';
      case SendTo.classes:
        return 'فصول محددة';
    }
  }

  static SendTo fromString(String value) {
    switch (value) {
      case 'all_parents':
        return SendTo.allParents;
      case 'class':
        return SendTo.classes;
      default:
        return SendTo.allParents;
    }
  }
}

class EvaluationModel extends Equatable {
  final int? id;
  final String date;
  final List<ClassModel>? classes;
  final SendTo sendTo;
  final List<EvaluationQuestionModel> questions;
  final DateTime? createdAt;

  const EvaluationModel({
    this.id,
    this.date = '',
    this.classes,
    this.sendTo = SendTo.allParents,
    this.questions = const [],
    this.createdAt,
  });

  factory EvaluationModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];
    log('Evaluation data: ${attributes}');

    List<ClassModel> classesList = [];
    if (attributes.containsKey('classes') &&
        attributes['classes'] != null &&
        attributes['classes'][ApiStrings.data] != null) {
      final classesData = attributes['classes'][ApiStrings.data] as List;
      classesList = classesData.map((e) => ClassModel.fromJson(e)).toList();
    }

    List<EvaluationQuestionModel> questionsList = [];
    if (attributes.containsKey('questions') &&
        attributes['questions'] != null) {
      final questionsData = attributes['questions'] as List;
      questionsList = questionsData
          .map((e) => EvaluationQuestionModel.fromJson(e))
          .toList();
    }

    return EvaluationModel(
      id: json[ApiStrings.id],
      date: attributes['date'] ?? '',
      classes: classesList,
      sendTo:
          SendToExtension.fromString(attributes['send_to'] ?? 'all_parents'),
      questions: questionsList,
      createdAt: attributes[ApiStrings.createdAt] != null
          ? DateTime.parse(attributes[ApiStrings.createdAt]).toLocal()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      'date': date,
      'send_to': sendTo.value,
      'questions': questions.map((q) => q.toJson()).toList(),
      if (classes != null && sendTo == SendTo.classes)
        'classes': classes!.map((c) => c.id).toList(),
      ApiStrings.nursery: NurseryModelHelper.currentNurseryId(),
    };
  }

  EvaluationModel copyWith({
    int? id,
    String? date,
    List<ClassModel>? classes,
    SendTo? sendTo,
    List<EvaluationQuestionModel>? questions,
    DateTime? createdAt,
  }) {
    return EvaluationModel(
      id: id ?? this.id,
      date: date ?? this.date,
      classes: classes ?? this.classes,
      sendTo: sendTo ?? this.sendTo,
      questions: questions ?? this.questions,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        date,
        classes,
        sendTo,
        questions,
        createdAt,
      ];
}
