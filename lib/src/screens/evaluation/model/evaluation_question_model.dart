import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:equatable/equatable.dart';

enum QuestionType {
  text,
  rating,
}

extension QuestionTypeExtension on QuestionType {
  String get value {
    switch (this) {
      case QuestionType.text:
        return 'text';
      case QuestionType.rating:
        return 'rating';
    }
  }

  String get displayName {
    switch (this) {
      case QuestionType.text:
        return 'Text Answer';
      case QuestionType.rating:
        return 'Star Rating (1-5)';
    }
  }

  String get displayNameAr {
    switch (this) {
      case QuestionType.text:
        return 'إجابة نصية';
      case QuestionType.rating:
        return 'تقييم بالنجوم (1-5)';
    }
  }

  static QuestionType fromString(String value) {
    switch (value) {
      case 'text':
        return QuestionType.text;
      case 'rating':
        return QuestionType.rating;
      default:
        return QuestionType.text;
    }
  }
}

class EvaluationQuestionModel extends Equatable {
  final int? id;
  final String question;
  final QuestionType type;
  final String? answer;
  final double? rate;
  final List<int?> parentIds;

  const EvaluationQuestionModel({
    this.id,
    this.question = '',
    this.type = QuestionType.text,
    this.answer,
    this.rate,
    this.parentIds = const [],
  });

  factory EvaluationQuestionModel.fromJson(Map<String, dynamic> json) {
    List<int?> parentIdsList = [];
    if (json['parent_ids'] != null) {
      final parentIdsData = json['parent_ids'] as List;
      parentIdsList =
          parentIdsData.map((item) => item['parent_id'] as int?).toList();
    }

    return EvaluationQuestionModel(
      id: json[ApiStrings.id],
      question: json['question'] ?? '',
      type: QuestionTypeExtension.fromString(json['type'] ?? 'text'),
      answer: json['answer'],
      rate: json['rate']?.toDouble(),
      parentIds: parentIdsList,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      'question': question,
      'type': type.value,
      if (answer != null) 'answer': answer,
      if (rate != null) 'rate': rate,
      'parent_ids': parentIds.map((id) => {'parent_id': id}).toList(),
    };
  }

  EvaluationQuestionModel copyWith({
    int? id,
    String? question,
    QuestionType? type,
    String? answer,
    double? rate,
    List<int?>? parentIds,
  }) {
    return EvaluationQuestionModel(
      id: id ?? this.id,
      question: question ?? this.question,
      type: type ?? this.type,
      answer: answer ?? this.answer,
      rate: rate ?? this.rate,
      parentIds: parentIds ?? this.parentIds,
    );
  }

  @override
  List<Object?> get props => [
        id,
        question,
        type,
        answer,
        rate,
        parentIds,
      ];
}
