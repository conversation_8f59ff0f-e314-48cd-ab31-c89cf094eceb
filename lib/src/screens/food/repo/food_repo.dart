import 'package:connectify_app/src/screens/food/model/food_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../history/model/history_model.dart';
import '../../history/repo/history_repo.dart';

// * =========================================================

final foodRepoProvider = Provider((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);

  return FoodRepo(networkApiServices);
});

//? ========================================================

class FoodRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  FoodRepo(this._networkApiServices);

//? get Food Data ========================================================
  Future<List<FoodModel>> getFoodData() async {
    return await baseFunction(() async {
      final response = _networkApiServices.getResponse(ApiEndpoints.food);

      final foodData = await compute(responseToFoodModelList, response);

      return foodData;
    });
  }

//? Add Food ========================================================

  Future<void> addFood({required FoodModel food}) async {
    return await baseFunction(() async {
      final foodData = await _networkApiServices
          .postResponse(ApiEndpoints.editDeleteFood, body: food.toJson());

      addNewHistory(
          historyModel: HistoryModel(
        historyType: HistoryType.food,
        food: FoodModel(
          id: foodData['data']['id'],
        ),
        student: food.student,
      ));
    });
  }

  //? Edit Food ========================================================
  Future<void> editFood({required FoodModel food}) async {
    return await baseFunction(() async {
      final foodData = await _networkApiServices.putResponse(
        '${ApiEndpoints.editDeleteFood}/${food.id}',
        data: food.toJson(),
      );

      addNewHistory(
        historyModel: HistoryModel(
          historyType: HistoryType.food,
          food: FoodModel(
            id: foodData['data']['id'],
          ),
          student: food.student,
        ),
      );
    });
  }
}
