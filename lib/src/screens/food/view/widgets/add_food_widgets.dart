import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/widgets/drop_downs/multi_student_drop_down.dart';
import '../../../../shared/widgets/tab_bar_widgets/add_container_widget.dart';

class AddFoodWidgets extends HookWidget {
  final Map<String, ValueNotifier> valueNotifiers;
  final bool hideStudentDropdown;
  final bool disableMealTypeSelection;

  const AddFoodWidgets({
    super.key,
    required this.valueNotifiers,
    this.hideStudentDropdown = false,
    this.disableMealTypeSelection = false,
  });

  @override
  Widget build(BuildContext context) {
    final List<List<String>> list = [
      [
        context.tr.breakfast,
        context.tr.snack,
        context.tr.lunch,
      ],
      [
        context.tr.all,
        context.tr.more,
        context.tr.some,
        context.tr.none,
      ]
    ];

    final List<List<String>> foodImages = [
      [
        Assets.svgBreakfast,
        Assets.svgSnack,
        Assets.svgLunch,
      ],
      [
        Assets.svgAll,
        Assets.svgMore,
        Assets.svgSome,
        Assets.svgNone,
      ],
    ];

    final indexFoodValue = useState<int>(0);
    final indexFoodMealValue = useState<int>(0);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!hideStudentDropdown) ...[
          MultiStudentDropDown(
            selectedStudents: valueNotifiers[ApiStrings.students]
                as ValueNotifier<List<StudentModel>>,
          ),
          context.mediumGap,
        ],
        Text(
          context.tr.mealType,
          style: context.title,
        ),
        context.mediumGap,
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: list.first
              .asMap()
              .entries
              .indexed
              .map((e) => SizedBox(
                    width: context.width / 3.6,
                    child: IgnorePointer(
                      ignoring: disableMealTypeSelection,
                      child: Opacity(
                        opacity: disableMealTypeSelection ? 0.5 : 1.0,
                        child: AddContainerWidget(
                          value: e.$1,
                          title: e.$2.value,
                          groupValue: valueNotifiers[ApiStrings.food]
                              as ValueNotifier<int>,
                          imagePath: foodImages.first[e.$1],
                          index: indexFoodValue,
                          onChanged: (value) {
                            indexFoodValue.value = value as int;
                            valueNotifiers[ApiStrings.food]?.value = value;
                          },
                          onTap: () {
                            indexFoodValue.value = e.$1;
                            valueNotifiers[ApiStrings.food]?.value = e.$1;
                          },
                        ),
                      ),
                    ),
                  ))
              .toList(),
        ),
        context.mediumGap,
        Text(
          context.tr.mealAmount,
          style: context.title,
        ),
        context.mediumGap,
        Center(
          child: Wrap(
            spacing: 10,
            runSpacing: 20,
            children: list.last
                .asMap()
                .entries
                .indexed
                .map((e) => SizedBox(
                      width: context.width / 2.5,
                      child: AddContainerWidget(
                        value: e.$1,
                        title: e.$2.value,
                        groupValue: valueNotifiers[ApiStrings.type]
                            as ValueNotifier<int>,
                        imagePath: foodImages.last[e.$1],
                        index: indexFoodMealValue,
                        onChanged: (value) {
                          indexFoodMealValue.value = value as int;
                          // Log.w(value);
                          valueNotifiers[ApiStrings.type]?.value = value;
                        },
                        onTap: () {
                          indexFoodMealValue.value = e.$1;
                          valueNotifiers[ApiStrings.type]?.value = e.$1;
                        },
                      ),
                    ))
                .toList(),
          ),
        ),
      ],
    );
  }
}
