import 'package:connectify_app/src/screens/food/controller/food_controller.dart';
import 'package:connectify_app/src/screens/food/view/widgets/add_food_widgets.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/student/view/student_details_screen/student_details_screen.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/widgets/shared_widgets.dart';
import '../../model/food_model.dart';

Future<void> showAddFoodDialog(
  context, {
  StudentModel? student,
  MealTypes? mealType,
  FoodModel? existingFood,
}) {
  return showDialog(
    context: context,
    builder: (context) {
      return HookConsumer(
        builder: (context, ref, child) {
          final foodChangeNotifierCtrl =
              ref.watch(foodChangeNotifierControllerProvider(context));

          final valueNotifiers = {
            ApiStrings.students:
                useState<List<StudentModel>>(student != null ? [student] : []),
            ApiStrings.food: useState<int>(
                existingFood?.mealType?.index ?? mealType?.index ?? 0),
            ApiStrings.type:
                useState<int>(existingFood?.mealAmount?.index ?? 0),
          };

          //!-----------------------------------------------------

          final formKey = useState(GlobalKey<FormState>());

          //!-----------------------------------------------------

          Future<void> addOrEditFood() async {
            if (!formKey.value.currentState!.validate()) return;
            final students = valueNotifiers[ApiStrings.students]!.value
                as List<StudentModel>;

            if (students.isEmpty) {
              context.showBarMessage(context.tr.pleaseSelectStudent,
                  isError: true);
              return;
            }

            if (existingFood != null) {
              // Edit existing food
              await foodChangeNotifierCtrl.editFood(
                foodId: existingFood.id!,
                mealTypeValue: valueNotifiers[ApiStrings.food]?.value as int,
                mealAmountValue: valueNotifiers[ApiStrings.type]?.value as int,
                student: students.first,
              );
            } else {
              // Add new food
              await Future.forEach(
                students,
                (student) async {
                  await foodChangeNotifierCtrl.addFood(
                    mealTypeValue:
                        valueNotifiers[ApiStrings.food]?.value as int,
                    mealAmountValue:
                        valueNotifiers[ApiStrings.type]?.value as int,
                    student: student,
                  );
                },
              );
            }

            if (!context.mounted) return;
            context.back();
            context.showBarMessage(existingFood != null
                ? context.tr.editedSuccessfully
                : context.tr.addedSuccessfully);

            // Navigate back to student details if student was provided
            if (student != null) {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => StudentDetailsScreen(student: student),
                ),
              );
            }
          }

          return AlertDialogWidget(
              header: existingFood != null
                  ? context.tr.editFood
                  : context.tr.addFood,
              isLoading: foodChangeNotifierCtrl.isLoading,
              isImage: false,
              child: Form(
                key: formKey.value,
                child: AddFoodWidgets(
                  valueNotifiers: valueNotifiers,
                  hideStudentDropdown: student != null,
                  disableMealTypeSelection:
                      mealType != null || existingFood != null,
                ),
              ),
              onConfirm: () async => await addOrEditFood());
        },
      );
    },
  );
}
