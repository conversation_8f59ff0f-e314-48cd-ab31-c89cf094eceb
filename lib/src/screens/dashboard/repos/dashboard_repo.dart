import 'package:connectify_app/src/screens/dashboard/models/nursery_statistics_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod/riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final dashboardRepoProvider = Provider<DashboardRepo>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return DashboardRepo(networkApiService);
});

//? ===========================================================
class DashboardRepo with BaseRepository {
  final BaseApiServices _networkApiService;

  DashboardRepo(this._networkApiService);

  //? Get Nursery Statistics Data ------------------------------------------
  Future<List<NurseryStatisticsModel>> getNurseryStatistics() async {
    return baseFunction(
      () async {
        final response = await _networkApiService.getResponse(
          ApiEndpoints.nurseriesStatistics,
        );

        final nurseryStatistics = compute(responseToNurseryStatisticsModelList, response);

        return nurseryStatistics;
      },
    );
  }
}
