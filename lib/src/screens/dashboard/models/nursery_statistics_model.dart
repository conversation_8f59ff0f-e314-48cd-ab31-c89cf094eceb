import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/shared_models/base_media_model.dart';
import 'package:connectify_app/src/shared/shared_models/base_model.dart';

List<NurseryStatisticsModel> responseToNurseryStatisticsModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final nurseries = data.map((e) => NurseryStatisticsModel.fromJson(e)).toList();

  return nurseries;
}

class NurseryStatisticsModel extends BaseModel {
  final int maxStudents;
  final bool canContactTeacher;
  final bool showNurseryLogoInParentApp;
  final num? fees;
  final DateTime? endDate;
  final int activeStudents;
  final int inactiveStudents;

  const NurseryStatisticsModel({
    super.id,
    super.name,
    super.description,
    super.image,
    super.createdAt,
    this.maxStudents = 0,
    this.canContactTeacher = true,
    this.showNurseryLogoInParentApp = false,
    this.fees,
    this.endDate,
    this.activeStudents = 0,
    this.inactiveStudents = 0,
  });

  factory NurseryStatisticsModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];

    final logo = attributes != null &&
            attributes.containsKey(ApiStrings.logo) &&
            attributes[ApiStrings.logo] != null
        ? BaseMediaModel.fromJson(attributes[ApiStrings.logo])
        : null;

    return NurseryStatisticsModel(
      id: json[ApiStrings.id],
      name: attributes != null ? (attributes[ApiStrings.name] ?? '') : '',
      image: logo,
      createdAt: attributes != null ? attributes[ApiStrings.createdAt] : null,
      maxStudents: attributes != null ? (attributes['max_students'] ?? 0) : 0,
      canContactTeacher: attributes != null ? (attributes['can_contact_teacher'] ?? true) : true,
      showNurseryLogoInParentApp: attributes != null ? (attributes['show_nursery_logo_in_parent_app'] ?? false) : false,
      fees: attributes != null ? attributes[ApiStrings.fees] : null,
      endDate: attributes != null && attributes['end_date'] != null
          ? DateTime.parse(attributes['end_date'])
          : null,
      activeStudents: attributes != null ? (attributes['active_students'] ?? 0) : 0,
      inactiveStudents: attributes != null ? (attributes['inactive_students'] ?? 0) : 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      ApiStrings.id: id,
      ApiStrings.name: name,
      ApiStrings.logo: image?.toJson(),
      ApiStrings.createdAt: createdAt,
      'max_students': maxStudents,
      'can_contact_teacher': canContactTeacher,
      'show_nursery_logo_in_parent_app': showNurseryLogoInParentApp,
      ApiStrings.fees: fees,
      'end_date': endDate?.toIso8601String(),
      'active_students': activeStudents,
      'inactive_students': inactiveStudents,
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        image,
        createdAt,
        maxStudents,
        canContactTeacher,
        showNurseryLogoInParentApp,
        fees,
        endDate,
        activeStudents,
        inactiveStudents,
      ];

  NurseryStatisticsModel copyWith({
    int? id,
    String? name,
    String? description,
    BaseMediaModel? image,
    String? createdAt,
    int? maxStudents,
    bool? canContactTeacher,
    bool? showNurseryLogoInParentApp,
    num? fees,
    DateTime? endDate,
    int? activeStudents,
    int? inactiveStudents,
  }) {
    return NurseryStatisticsModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      image: image ?? this.image,
      createdAt: createdAt ?? this.createdAt,
      maxStudents: maxStudents ?? this.maxStudents,
      canContactTeacher: canContactTeacher ?? this.canContactTeacher,
      showNurseryLogoInParentApp: showNurseryLogoInParentApp ?? this.showNurseryLogoInParentApp,
      fees: fees ?? this.fees,
      endDate: endDate ?? this.endDate,
      activeStudents: activeStudents ?? this.activeStudents,
      inactiveStudents: inactiveStudents ?? this.inactiveStudents,
    );
  }

  factory NurseryStatisticsModel.empty() => const NurseryStatisticsModel(
        id: 0,
        name: '',
        description: '',
        image: null,
        createdAt: null,
      );
}
