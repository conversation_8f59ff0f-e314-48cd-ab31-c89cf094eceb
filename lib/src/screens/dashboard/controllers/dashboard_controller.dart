import 'package:connectify_app/src/screens/dashboard/models/nursery_statistics_model.dart';
import 'package:connectify_app/src/screens/dashboard/repos/dashboard_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

// * Dashboard Provider Controller ========================================
final dashboardProviderController =
    Provider.family<DashboardController, BuildContext>((ref, context) {
  final dashboardRepo = ref.watch(dashboardRepoProvider);

  return DashboardController(context, dashboardRepo: dashboardRepo);
});

// * Get Nursery Statistics Data Provider ========================================
final getNurseryStatisticsDataProvider =
    FutureProvider.family<List<NurseryStatisticsModel>, BuildContext>(
        (ref, context) async {
  final dashboardController = ref.watch(dashboardProviderController(context));

  return await dashboardController.getNurseryStatistics();
});

//? ==========================================================================
class DashboardController extends BaseVM {
  final BuildContext context;
  final DashboardRepo dashboardRepo;

  DashboardController(this.context, {required this.dashboardRepo});

  //? Get Nursery Statistics ----------------------------------------------------
  Future<List<NurseryStatisticsModel>> getNurseryStatistics() async {
    return await baseFunction(
      context,
      () async {
        final nurseryStatistics = await dashboardRepo.getNurseryStatistics();
        return nurseryStatistics;
      },
      isLoading: false,
    );
  }
}
