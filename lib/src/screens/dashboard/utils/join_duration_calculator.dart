import 'package:flutter/material.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';

class JoinDurationCalculator {
  /// Calculates duration since joining and returns formatted string
  /// Returns format like "1 years, 3 months, 13 days"
  static String calculateJoinDuration(String? createdAt, BuildContext context) {
    if (createdAt == null || createdAt.isEmpty) return '';

    try {
      final joinDate = DateTime.parse(createdAt);
      final now = DateTime.now();

      // Calculate the difference
      int years = now.year - joinDate.year;
      int months = now.month - joinDate.month;
      int days = now.day - joinDate.day;

      // Adjust if the current day is before the join day
      if (days < 0) {
        months--;
        // Get the number of days in the previous month
        final previousMonth = DateTime(now.year, now.month, 0);
        days += previousMonth.day;
      }

      // Adjust if months is negative
      if (months < 0) {
        years--;
        months += 12;
      }

      // Build the result string
      List<String> parts = [];

      if (years > 0) {
        if (years == 1) {
          parts.add(context.tr.oneYear);
        } else {
          parts.add(context.tr.yearsOld(years));
        }
      }

      if (months > 0) {
        if (months == 1) {
          parts.add(context.tr.oneMonth);
        } else {
          parts.add(context.tr.monthsOld(months));
        }
      }

      if (days > 0) {
        if (days == 1) {
          parts.add(context.tr.oneDay);
        } else {
          parts.add(context.tr.daysOld(days));
        }
      }

      // If no time has passed, show "Today"
      if (parts.isEmpty) {
        return context.tr.today;
      }

      return parts.join(', ');
    } catch (e) {
      return '';
    }
  }

  /// Returns a short join duration format for display in cards
  /// Returns format like "1y 3m 13d"
  static String calculateShortJoinDuration(
      String? createdAt, BuildContext context) {
    if (createdAt == null || createdAt.isEmpty) return '';

    try {
      final joinDate = DateTime.parse(createdAt);
      final now = DateTime.now();

      // Calculate the difference
      int years = now.year - joinDate.year;
      int months = now.month - joinDate.month;
      int days = now.day - joinDate.day;

      // Adjust if the current day is before the join day
      if (days < 0) {
        months--;
        // Get the number of days in the previous month
        final previousMonth = DateTime(now.year, now.month, 0);
        days += previousMonth.day;
      }

      // Adjust if months is negative
      if (months < 0) {
        years--;
        months += 12;
      }

      // Build the result string
      List<String> parts = [];

      if (years > 0) {
        parts.add('${years}y');
      }

      if (months > 0) {
        parts.add('${months}m');
      }

      if (days > 0) {
        parts.add('${days}d');
      }

      // If no time has passed, show "Today"
      if (parts.isEmpty) {
        return context.tr.today;
      }

      return parts.join(' ');
    } catch (e) {
      return '';
    }
  }
}
