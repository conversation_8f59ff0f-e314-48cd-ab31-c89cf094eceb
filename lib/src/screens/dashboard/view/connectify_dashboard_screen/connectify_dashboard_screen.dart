import 'package:connectify_app/src/screens/dashboard/controllers/dashboard_controller.dart';
import 'package:connectify_app/src/screens/dashboard/models/nursery_statistics_model.dart';
import 'package:connectify_app/src/screens/dashboard/utils/join_duration_calculator.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class ConnectifyDashboardScreen extends HookConsumerWidget {
  const ConnectifyDashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedNursery = useState<NurseryStatisticsModel?>(null);
    final nurseryStatisticsController =
        ref.watch(getNurseryStatisticsDataProvider(context));

    return Scaffold(
      appBar: AppBar(
        title: Text(context.tr.dashboard),
        backgroundColor: ColorManager.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: nurseryStatisticsController.get(
        data: (nurseries) {
          return Padding(
            padding: const EdgeInsets.all(AppSpaces.mediumPadding),
            child: Column(
              children: [
                // Nursery Search Dropdown
                BaseSearchDropDown(
                  label: context.tr.selectNursery,
                  data: nurseries,
                  selectedValue: selectedNursery.value,
                  itemModelAsName: (nursery) =>
                      (nursery as NurseryStatisticsModel).name,
                  isEng: context.isEng,
                  onChanged: (value) {
                    selectedNursery.value = value as NurseryStatisticsModel?;
                  },
                ),

                context.largeGap,

                // Display selected nursery information
                if (selectedNursery.value != null) ...[
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          // Nursery Logo
                          if (selectedNursery.value!.image?.url != null)
                            Container(
                              width: 120,
                              height: 120,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: ColorManager.primaryColor,
                                  width: 3,
                                ),
                              ),
                              child: ClipOval(
                                child: BaseCachedImage(
                                  selectedNursery.value!.image!.url!,
                                  fit: BoxFit.cover,
                                ),
                              ),
                            )
                          else
                            Container(
                              width: 120,
                              height: 120,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color:
                                    ColorManager.primaryColor.withOpacity(0.1),
                                border: Border.all(
                                  color: ColorManager.primaryColor,
                                  width: 3,
                                ),
                              ),
                              child: const Icon(
                                Icons.school,
                                size: 60,
                                color: ColorManager.primaryColor,
                              ),
                            ),

                          context.mediumGap,

                          // Nursery Name
                          Text(
                            selectedNursery.value!.name,
                            style: context.title.copyWith(
                              fontWeight: FontWeight.bold,
                              fontSize: 24,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          context.smallGap,

                          // Join Duration
                          Text(
                            '${context.tr.joinedIn} ${JoinDurationCalculator.calculateJoinDuration(selectedNursery.value!.createdAt, context)}',
                            style: context.subTitle.copyWith(
                              color: ColorManager.grey,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          context.largeGap,

                          // Student Statistics Cards
                          Row(
                            children: [
                              // Active Students Card
                              Expanded(
                                child: BaseContainer(
                                  color: ColorManager.primaryColor
                                      .withOpacity(0.1),
                                  borderColor: ColorManager.primaryColor,
                                  child: Column(
                                    children: [
                                      Icon(
                                        Icons.people,
                                        size: 40,
                                        color: ColorManager.primaryColor,
                                      ),
                                      context.smallGap,
                                      Text(
                                        selectedNursery.value!.activeStudents
                                            .toString(),
                                        style: context.title.copyWith(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 28,
                                          color: ColorManager.primaryColor,
                                        ),
                                      ),
                                      Text(
                                        context.tr.activeStudents,
                                        style: context.subTitle.copyWith(
                                          color: ColorManager.primaryColor,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                              ),

                              context.mediumGap,

                              // Inactive Students Card
                              Expanded(
                                child: BaseContainer(
                                  color:
                                      ColorManager.errorColor.withOpacity(0.1),
                                  borderColor: ColorManager.errorColor,
                                  child: Column(
                                    children: [
                                      Icon(
                                        Icons.people_outline,
                                        size: 40,
                                        color: ColorManager.errorColor,
                                      ),
                                      context.smallGap,
                                      Text(
                                        selectedNursery.value!.inactiveStudents
                                            .toString(),
                                        style: context.title.copyWith(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 28,
                                          color: ColorManager.errorColor,
                                        ),
                                      ),
                                      Text(
                                        context.tr.inactiveStudents,
                                        style: context.subTitle.copyWith(
                                          color: ColorManager.errorColor,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),

                          context.largeGap,

                          // Additional Information
                          BaseContainer(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  context.tr.nurseryInformation,
                                  style: context.title.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                context.mediumGap,
                                _buildInfoRow(
                                  context,
                                  context.tr.maxStudents,
                                  selectedNursery.value!.maxStudents.toString(),
                                ),
                                context.smallGap,
                                if (selectedNursery.value!.fees != null)
                                  _buildInfoRow(
                                    context,
                                    context.tr.fees,
                                    selectedNursery.value!.fees.toString(),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ] else
                  Expanded(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.dashboard,
                            size: 80,
                            color: ColorManager.grey,
                          ),
                          context.mediumGap,
                          Text(
                            context.tr.selectNurseryToViewStatistics,
                            style: context.subTitle.copyWith(
                              color: ColorManager.grey,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: context.subTitle,
        ),
        Text(
          value,
          style: context.subTitle.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
