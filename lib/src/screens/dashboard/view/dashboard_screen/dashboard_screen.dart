import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/class/controllers/class_controller.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/dashboard/view/dashboard_screen/widgets/charts/activity_charts.dart';
import 'package:connectify_app/src/screens/dashboard/view/dashboard_screen/widgets/charts/attendance_charts.dart';
import 'package:connectify_app/src/screens/dashboard/view/dashboard_screen/widgets/charts/bills_charts.dart';
import 'package:connectify_app/src/screens/dashboard/view/dashboard_screen/widgets/charts/invoices_charts.dart';
import 'package:connectify_app/src/screens/financial/models/bill_model.dart';
import 'package:connectify_app/src/screens/financial/models/invoices_model.dart';
import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class DashboardScreen extends HookConsumerWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isAdmin = const UserModel().isCurrentUserAdmin;

    final valueNotifiers = {
      ApiStrings.classes: useState<List<ClassModel>>([]),
      ApiStrings.students: useState<List<StudentModel>>([]),
      // * Attendance
      ApiStrings.selectedAttendanceStudent: useState<StudentModel?>(null),
      ApiStrings.selectedAttendanceMonth: useState<String>('January'),
      // * Activity
      ApiStrings.selectedActivityClass: useState<ClassModel?>(null),
      ApiStrings.selectedActivityMonth: useState<String>('January'),
      // * Bills
      ApiStrings.selectedBillStudent: useState<StudentModel?>(null),
      ApiStrings.selectedBillMonth: useState<String>('January'),
      ApiStrings.isLoadingBill: useState<bool>(false),
      ApiStrings.bills: useState<List<BillModel>>([]),
      // * Invoices
      ApiStrings.selectedInvoiceMonth: useState<String>('January'),
      ApiStrings.isLoadingInvoice: useState<bool>(false),
      ApiStrings.invoices: useState<List<InvoicesModel>>([]),
    };

    final classController = ref.watch(getClassDataProvider(context));
    final studentsController = ref.watch(getActiveStudents(context));

    valueNotifiers[ApiStrings.classes]?.value = classController.when(
      data: (data) {
        return data;
      },
      loading: () {
        return <ClassModel>[];
      },
      error: (error, stack) {
        return <ClassModel>[];
      },
    );

    valueNotifiers[ApiStrings.students]?.value = studentsController.when(
      data: (data) {
        return data;
      },
      loading: () {
        return <StudentModel>[];
      },
      error: (error, stack) {
        return <StudentModel>[];
      },
    );

    return ListView(
      padding: const EdgeInsets.all(AppSpaces.mediumPadding),
      children: [
        AttendanceCharts(
          valueNotifiers: valueNotifiers,
        ),
        context.largeGap,
        ActivityCharts(
          valueNotifiers: valueNotifiers,
        ),
        context.largeGap,
        if (isAdmin) ...[
          BillsCharts(
            valueNotifiers: valueNotifiers,
          ),
          context.largeGap,
          InvoicesCharts(
            valueNotifiers: valueNotifiers,
          ),
        ]
      ],
    );
  }
}

// // * Attendances =======================================================
// final getAttendanceProvider = ref.watch(getAttendanceDataProvider(context));
//
// final selectedStudent = valueNotifiers[ApiStrings.selectedAttendanceStudent]
//     as ValueNotifier<StudentModel?>;
// final attendances = valueNotifiers[ApiStrings.attendances]
//     as ValueNotifier<List<AttendanceModel>>;
// final allAttendancesDates = valueNotifiers[ApiStrings.allAttendancesDates]
//     as ValueNotifier<List<String>>;
// final selectedMonthAttendance =
//     valueNotifiers[ApiStrings.selectedAttendanceMonth]
//         as ValueNotifier<String>;
// final isLoadingAttendance =
//     valueNotifiers[ApiStrings.isLoadingAttendance] as ValueNotifier<bool>;
//
// useEffect(() {
//   WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
//     attendances.value = getAttendanceProvider.when(
//       data: (data) {
//         isLoadingAttendance.value = false;
//
//         allAttendancesDates.value = data
//             .where((element) => element.attendanceDate.isNotEmpty)
//             .map((e) => e.attendanceDate)
//             .toList();
//
//         return data
//             .where((element) =>
//                 selectedStudent.value?.id == element.student?.id)
//             .where((element) =>
//                 element.attendanceDate.formatDateTimeToMonth ==
//                 selectedMonthAttendance.value)
//             .toList();
//       },
//       loading: () {
//         isLoadingAttendance.value = true;
//         return <AttendanceModel>[];
//       },
//       error: (error, stack) {
//         isLoadingAttendance.value = false;
//         return <AttendanceModel>[];
//       },
//     );
//   });
//
//   return () {};
// }, [
//   getAttendanceProvider,
//   selectedStudent.value,
//   selectedMonthAttendance.value
// ]);

// // * Activities =======================================================
// final selectedClassActivity =
//     valueNotifiers[ApiStrings.selectedActivityClass]
//         as ValueNotifier<ClassModel?>;
// final isLoadingActivity =
//     valueNotifiers[ApiStrings.isLoadingActivity] as ValueNotifier<bool>;
// final selectedMonthActivity =
//     valueNotifiers[ApiStrings.selectedActivityMonth]
//         as ValueNotifier<String>;
// final getActivityProvider =
//     ref.watch(getTeacherActivitiesDataProvider(context));
// final allActivities = valueNotifiers[ApiStrings.allActivities]
//     as ValueNotifier<List<TeacherActivityModel>>;
// final activities = valueNotifiers[ApiStrings.activities]
//     as ValueNotifier<List<TeacherActivityModel>>;
//
// useEffect(() {
//   WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
//     activities.value = getActivityProvider.when(
//       data: (data) {
//         allActivities.value = data;
//
//         isLoadingActivity.value = false;
//
//         final filteredData = data
//             .where((element) => const UserModel().isAdmin
//                 ? selectedClassActivity.value?.id == element.classModel?.id
//                 : element.classModel?.id ==
//                     const UserModel().currentUser.classModel?.id)
//             .where((element) =>
//                 element.createdAt?.formatDateTimeToMonth ==
//                 selectedMonthActivity.value)
//             .toList();
//
//         return filteredData;
//       },
//       loading: () {
//         isLoadingActivity.value = true;
//         return <TeacherActivityModel>[];
//       },
//       error: (error, stack) {
//         isLoadingActivity.value = false;
//         return <TeacherActivityModel>[];
//       },
//     );
//   });
//   return () {};
// }, [
//   getActivityProvider,
//   selectedClassActivity.value,
//   selectedMonthActivity.value
// ]);
