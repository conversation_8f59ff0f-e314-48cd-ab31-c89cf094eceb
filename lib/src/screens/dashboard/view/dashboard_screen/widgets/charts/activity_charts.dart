import 'package:connectify_app/src/screens/Activities/models/teacher_activity_model.dart';
import 'package:connectify_app/src/screens/activities/controllers/teacher_activities_controller.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/dashboard/view/dashboard_screen/widgets/dashboard_expansionTile_widget.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/drop_downs/class_drop_down.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:xr_helper/xr_helper.dart';

class ActivityCharts extends HookConsumerWidget {
  final Map<String, ValueNotifier> valueNotifiers;

  const ActivityCharts({super.key, required this.valueNotifiers});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isTeacher = const UserModel().isCurrentUserTeacher;
    final selectedClass = valueNotifiers[ApiStrings.selectedActivityClass]
        as ValueNotifier<ClassModel?>;
    final selectedMonth = valueNotifiers[ApiStrings.selectedActivityMonth]
        as ValueNotifier<String>;

    final classId = const UserModel().isCurrentUserAdmin
        ? selectedClass.value?.id
        : selectedTeacherClass.value?.id;

    final params = (context, classId, selectedMonth.value);

    final getActivityProvider =
        ref.watch(getTeacherActivitiesByClassAndDateProvider(params));

    final activities = getActivityProvider.when(
      data: (data) {
        return data;
      },
      loading: () {
        return <TeacherActivityModel>[];
      },
      error: (error, stack) {
        return <TeacherActivityModel>[];
      },
    );

    final activityCounts = activities.fold<Map<String, int>>(
        {},
        (previousValue, element) => previousValue
          ..update(element.activity?.name ?? '', (value) => value + 1,
              ifAbsent: () => 1));

    final activityPercentages = activityCounts
        .map((name, count) => MapEntry(name, count / activities.length));

    return DashBoardExpansionTileWidget(
      title: context.tr.activityChart,
      children: [
        BaseContainer(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              if (!isTeacher) ...[
                context.mediumGap,

                //! Drop Down Fields
                ClassDropDown(
                  selectedClass: selectedClass,
                  classesData: valueNotifiers[ApiStrings.classes]?.value
                      as List<ClassModel>,
                ),
              ],
              context.mediumGap,
              BaseDropDown(
                onChanged: (value) {
                  selectedMonth.value = value;
                },
                data: AppConsts.months,
                label: context.tr.month,
                icon: const Icon(Icons.calendar_today),
                selectedValue: selectedMonth.value,
              ),
              context.mediumGap,
              if (activityCounts.isEmpty)
                Text(
                  context.tr.noActivitiesFound,
                  style: context.body,
                )
              else
                //! Chart Circle
                SfCircularChart(
                  series: <CircularSeries>[
                    DoughnutSeries<MapEntry<String, double>, String>(
                      dataSource: activityPercentages.entries.toList(),
                      pointColorMapper: (MapEntry<String, double> entry, _) =>
                          ColorManager.purple,
                      xValueMapper: (MapEntry<String, double> entry, _) =>
                          entry.key,
                      yValueMapper: (MapEntry<String, double> entry, _) =>
                          entry.value,
                      dataLabelMapper: (MapEntry<String, double> entry, _) {
                        final valuePercentage =
                            (entry.value * 100).toStringAsFixed(2);

                        return '${entry.key}\n$valuePercentage%';
                      },
                      dataLabelSettings: const DataLabelSettings(
                        isVisible: true,
                        textStyle: TextStyle(
                            color: Colors.black, fontWeight: FontWeight.bold),
                      ),
                    )
                  ],
                ),
            ],
          ),
        ),
      ],
    );
  }
}
