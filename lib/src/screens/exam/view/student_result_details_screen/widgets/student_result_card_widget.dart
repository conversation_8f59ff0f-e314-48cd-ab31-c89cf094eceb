import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/exam/models/exam_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class StudentResultCardWidget extends ConsumerWidget {
  final ExamModel exam;
  final int number;
  final TextEditingController controller;
  final ValueNotifier<num> rating;
  final StudentsResultModel? studentResult;
  final ValueNotifier<bool> canAddNote;
  final Function setState;

  const StudentResultCardWidget({
    super.key,
    required this.exam,
    required this.number,
    required this.controller,
    required this.studentResult,
    required this.rating,
    required this.canAddNote,
    required this.setState,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentUser = const UserModel().currentUser;
    final canAddResults = hasPermission(currentUser, 'addResults');

    return BaseContainer(
        padding: AppSpaces.appbarPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                //! Exam Number
                Text(
                  '$number.',
                  style: context.blueHint.copyWith(fontWeight: FontWeight.bold),
                ),

                context.smallGap,

                //! Exam (Name - Date)
                Text(
                  exam.question,
                  style: context.blueHint.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            context.mediumGap,
            Center(
              child: ValueListenableBuilder<num>(
                valueListenable: rating,
                builder: (context, value, child) {
                  return RatingBar.builder(
                    initialRating: value == 0 ? 1.0 : value.toDouble(),
                    minRating: 1,
                    direction: Axis.horizontal,
                    allowHalfRating: true,
                    ignoreGestures:
                        const UserModel().isCurrentUserAdmin || !canAddResults,
                    itemCount: 5,
                    itemSize: 40,
                    itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
                    itemBuilder: (context, _) => const Icon(
                      Icons.star,
                      color: Colors.amber,
                    ),
                    onRatingUpdate: (ratingData) {
                      rating.value = ratingData;
                    },
                  );
                },
              ),
            ),
            context.mediumGap,
            if (const UserModel().isCurrentUserAdmin || !canAddResults) ...[
              if (studentResult?.note != null && studentResult!.note.isNotEmpty)
                Text(
                  '${context.tr.note}: ${studentResult!.note}',
                  style: context.labelMedium,
                )
            ] else if (canAddNote.value)
              BaseTextField(
                controller: controller,
                hint: context.tr.note,
                maxLines: 3,
                enabled: !const UserModel().isCurrentUserAdmin && canAddResults,
              )
            else
              TextButton(
                onPressed: () {
                  canAddNote.value = true;
                  setState(() {});
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.add,
                      color: ColorManager.primaryColor,
                    ),
                    context.xSmallGap,
                    Text(
                      context.tr.addNote,
                      style: context.whiteHint.copyWith(
                        color: ColorManager.primaryColor,
                      ),
                    ),
                  ],
                ),
              )
          ],
        ));
  }
}
