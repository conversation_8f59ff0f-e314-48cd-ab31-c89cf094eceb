import 'package:connectify_app/src/screens/Activities/models/teacher_activity_model.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import 'package:riverpod/riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../auth/models/user_model.dart';
import '../../history/model/history_model.dart';
import '../../history/repo/history_repo.dart';

final teacherActivityRepoProvider = Provider<TeacherActivityRepo>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return TeacherActivityRepo(networkApiService);
});

class TeacherActivityRepo with BaseRepository {
  final BaseApiServices _networkApiService;

  TeacherActivityRepo(this._networkApiService);

  //? Get Activity Data ------------------------------------------
  Future<List<TeacherActivityModel>> getActivities({int? classId}) async {
    return baseFunction(
      () async {
        final response = await _networkApiService.getResponse(classId != null
            ? ApiEndpoints.teacherActivitiesByClass(classId)
            : ApiEndpoints.teacherActivities);

        final activityData =
            compute(responseToTeacherActivityModelList, response);

        return activityData;
      },
    );
  }

  Future<List<TeacherActivityModel>> getActivitiesByDay() async {
    return baseFunction(
      () async {
        DateTime now = DateTime.now();
        String dayFormat = DateFormat('EEEE').format(now);
        String dateFormat = DateFormat('yyyy-MM-dd').format(now);

        final response = await _networkApiService.getResponse(
          '${ApiEndpoints.teacherActivities}&filters[\$or][0][day]=$dayFormat&filters[\$or][1][date]=$dateFormat',
        );

        final activityData =
            compute(responseToTeacherActivityModelList, response);

        return activityData;
      },
    );
  }

  Future<List<TeacherActivityModel>> getActivitiesByClassAndDate({
    required int? classId,
    required String monthName,
  }) async {
    return baseFunction(
      () async {
        if (classId == null) return [];
        // Parse monthName to a DateTime object
        final dateByMonth = DateFormat.MMMM().parse(monthName);

        // Calculate the first and last day of the given month
        final firstOfTheMonth =
            DateTime(DateTime.now().year, dateByMonth.month, 1);
        final lastOfTheMonth = DateTime(
                DateTime.now().year, dateByMonth.month + 1, 1)
            .subtract(const Duration(seconds: 1)); // Last second of the month

        // Construct the API endpoint with filters
        final response = await _networkApiService.getResponse(
          '${ApiEndpoints.teacherActivities}'
          '&filters[class][id]=$classId'
          '&filters[createdAt][\$gte]=${firstOfTheMonth.toIso8601String()}'
          '&filters[createdAt][\$lte]=${lastOfTheMonth.toIso8601String()}',
        );

        // Process the response using compute for potential offloading
        final activityData =
            compute(responseToTeacherActivityModelList, response);

        return activityData;
      },
    );
  }

  Future<List<TeacherActivityModel>> getActivitiesByDate({
    required String dayFormat,
    required String dateFormat,
    int? classId,
  }) async {
    return baseFunction(
      () async {
        // Construct the API endpoint with filters
        final classParamIfNotAdmin = const UserModel().isCurrentUserAdmin
            ? ''
            : classId != null
                ? '&filters[class][id]=$classId'
                : '&filters[class][id]=${selectedTeacherClass.value?.id}';
        final response = await _networkApiService.getResponse(
          '${ApiEndpoints.teacherActivities}&filters[\$or][0][day]=$dayFormat&filters[\$or][1][date]=$dateFormat$classParamIfNotAdmin',
        );

        // Process the response using compute for potential offloading
        final activityData =
            compute(responseToTeacherActivityModelList, response);

        return activityData;
      },
    );
  }

//? Add Activity Data ------------------------------------------
  Future<dynamic> addActivity(
      {required TeacherActivityModel teacherActivity}) async {
    return await baseFunction(() async {
      return await _networkApiService.postResponse(
        ApiEndpoints.teacherActivities,
        body: teacherActivity.toJson(),
      );
    });
  }

  //? Edit Activity Data ------------------------------------------
  Future<void> editActivity({
    required TeacherActivityModel activity,
    required int id,
  }) async {
    return await baseFunction(() async {
      await _networkApiService.putResponse(
        '${ApiEndpoints.editDeleteTeacherActivities}/$id',
        data: activity.toJson(),
        fieldName: ApiStrings.media,
      );

      addNewHistory(
          historyModel: HistoryModel(
        historyType: HistoryType.activity,
        activity: TeacherActivityModel(
          id: id,
        ),
      ));
    });
  }

  //? Delete Activity Data ------------------------------------------
  Future<void> deleteActivity({required int id}) async {
    return await baseFunction(() async {
      await _networkApiService
          .deleteResponse('${ApiEndpoints.editDeleteTeacherActivities}/$id');
    });
  }
}
