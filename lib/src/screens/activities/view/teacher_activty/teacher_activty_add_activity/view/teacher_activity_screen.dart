import 'package:connectify_app/src/screens/Activities/view/teacher_activty/teacher_activty_add_activity/view/widgets/teacher_activity_add_list.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../home/<USER>/main_screen/widgets/main_app_bar.dart';

class TeacherActivitiesScreen extends ConsumerWidget {
  final bool fromStudentDetails;
  const TeacherActivitiesScreen({super.key, this.fromStudentDetails = false});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return WillPopScope(
      onWillPop: () async {
        if (fromStudentDetails) {
          context.back();
          return false;
        }
        context.toReplacement(const MainScreen());
        return false;
      },
      child: Safe<PERSON><PERSON>(
        child: Scaffold(
          appBar: MainAppBar(
            title: context.tr.dailySchedule,
            isBackButton: true,
          ),
          body: const TeacherActivityAddList()
              .paddingAll(AppSpaces.mediumPadding),
        ),
      ),
    );
  }
}
