import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';

import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:connectify_app/src/shared/services/media/controller/media_controller.dart';
import 'package:connectify_app/src/shared/shared_models/base_media_model.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../../shared/widgets/media/media_upload_progress_widget.dart';
import '../../../../../../../shared/widgets/video_player/video_player_screen.dart';
import '../../../../../controllers/teacher_activities_controller.dart';

class TeacherActivityMediaWidget extends HookConsumerWidget {
  final ValueNotifier<List<BaseMediaModel>> mediaList;
  final Map<String, ValueNotifier<dynamic>>? controllers;
  final Function? onSavedMedia;
  final bool isVideoMode;

  const TeacherActivityMediaWidget({
    super.key,
    required this.mediaList,
    required this.controllers,
    this.onSavedMedia,
    this.isVideoMode = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaController = ref.watch(mediaPickerControllerProvider);

    final networkApiServices = ref.watch(networkServiceProvider);

    final activityController =
        ref.watch(teacherActivityChangeNotifierProvider(context));

    final isLoading = useState(false);

    return StatefulBuilder(builder: (context, setState) {
      return Scaffold(
        body: Padding(
          padding: const EdgeInsets.all(AppSpaces.mediumPadding),
          child: Column(
            children: [
              // if (mediaList.value.isEmpty)
              //   Padding(
              //     padding: const EdgeInsets.only(top: 80),
              //     child: Column(
              //       mainAxisAlignment: MainAxisAlignment.center,
              //       children: [
              //         const Icon(
              //           CupertinoIcons.photo_on_rectangle,
              //           size: 100,
              //         ),
              //         context.mediumGap,
              //         Text(
              //           context.tr.noMedia,
              //           style: context.subHeadLine,
              //         ),
              //       ],
              //     ),
              //   ),

              Expanded(
                flex: 3,
                child: Builder(
                  builder: (context) {
                    // Filter media based on mode
                    final filteredMedia = mediaList.value.where((media) {
                      final isVideo = _isVideoFile(media.url ?? '');
                      return isVideoMode ? isVideo : !isVideo;
                    }).toList();

                    if (filteredMedia.isEmpty) {
                      return Padding(
                        padding: const EdgeInsets.only(top: 80),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              isVideoMode
                                  ? Icons.videocam_outlined
                                  : CupertinoIcons.photo_on_rectangle,
                              size: 100,
                            ),
                            context.mediumGap,
                            Text(
                              isVideoMode
                                  ? context.tr.noVideos
                                  : context.tr.noMedia,
                              style: context.subHeadLine,
                            ),
                          ],
                        ),
                      );
                    }

                    return ListView.separated(
                      itemCount: filteredMedia.length,
                      padding: const EdgeInsets.only(bottom: 20),
                      separatorBuilder: (context, index) => context.mediumGap,
                      itemBuilder: (context, index) {
                        final mediaItem = filteredMedia[index];
                        final originalIndex =
                            mediaList.value.indexOf(mediaItem);

                        final isNetworkMedia =
                            mediaItem.url?.contains('http') ?? false;

                        final isVideo = _isVideoFile(mediaItem.url ?? '');

                        return Row(
                          children: [
                            Expanded(
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(
                                    AppRadius.baseContainerRadius),
                                child: SizedBox(
                                  height: 120,
                                  child: GestureDetector(
                                    onTap: () {
                                      if (isVideo) {
                                        // Navigate to video player
                                        Navigator.of(context).push(
                                          MaterialPageRoute(
                                            builder: (context) =>
                                                VideoPlayerScreen(
                                              videoUrl: mediaItem.url ?? '',
                                              isNetworkVideo: isNetworkMedia,
                                            ),
                                          ),
                                        );
                                      } else {
                                        // Show image dialog
                                        showDialog(
                                          context: context,
                                          builder: (context) => Dialog(
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      AppRadius
                                                          .baseContainerRadius),
                                            ),
                                            child: ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      AppRadius
                                                          .baseContainerRadius),
                                              child: isNetworkMedia
                                                  ? Image.network(
                                                      mediaItem.url ?? '',
                                                      errorBuilder: (context,
                                                              error,
                                                              stackTrace) =>
                                                          const BaseCachedImage(
                                                        AppConsts
                                                            .activityPlaceholder,
                                                      ),
                                                    )
                                                  : Image.file(
                                                      File(mediaItem.url ?? ''),
                                                    ),
                                            ),
                                          ),
                                        );
                                      }
                                    },
                                    child: Stack(
                                      children: [
                                        if (isVideo)
                                          _buildVideoThumbnail(mediaItem,
                                              isNetworkMedia, mediaController)
                                        else
                                          _buildImageWidget(
                                              mediaItem, isNetworkMedia),
                                        if (isVideo)
                                          const Center(
                                            child: CircleAvatar(
                                              backgroundColor: Colors.black54,
                                              child: Icon(
                                                Icons.play_arrow,
                                                color: Colors.white,
                                                size: 32,
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            context.mediumGap,
                            IconButton(
                              icon: const CircleAvatar(
                                backgroundColor: ColorManager.errorColor,
                                child: Icon(
                                  Icons.delete,
                                  color: ColorManager.white,
                                ),
                              ),
                              onPressed: () async {
                                controllers?[ApiStrings.isUpdated]?.value =
                                    true;
                                networkApiServices.deleteImage(
                                    mediaItem.id?.toString() ?? '');

                                mediaList.value.removeAt(originalIndex);

                                controllers![ApiStrings.media]!.value =
                                    mediaList.value;

                                setState(() {});
                              },
                            ),
                          ],
                        );
                      },
                    );
                  },
                ),
              ),

              // * Progress indicators for uploading/compressing files

              Consumer(
                builder: (context, ref, child) {
                  final mediaController =
                      ref.watch(mediaPickerControllerProvider);
                  final activeEntries = mediaController
                      .compressionProgress.entries
                      .where((entry) {
                    final isCompleted =
                        mediaController.isCompleted[entry.key] ?? false;
                    return !isCompleted;
                  }).toList();

                  log('afafsasf ${mediaController.isCompleted}');

                  if (mediaController.compressionProgress.isEmpty ||
                      activeEntries.isEmpty) {
                    return const SizedBox();
                  }

                  return Expanded(
                    flex: 2,
                    child: MediaUploadProgressList(
                      progressMap: mediaController.compressionProgress,
                      isCompressingMap: mediaController.isCompressing,
                      isUploadingMap: mediaController.isUploading,
                      isCompletedMap: mediaController.isCompleted,
                      onCancel: (filePath) {
                        mediaController.removeProgress(filePath);
                      },
                    ),
                  );
                },
              ),

              Padding(
                padding: const EdgeInsets.all(16),
                child: Button(
                  isLoading: activityController.isLoading || isLoading.value,
                  loadingWidget: const Center(child: LinearProgressIndicator()),
                  onPressed: () async {
                    controllers?[ApiStrings.isUpdated]?.value = true;

                    isLoading.value = true;

                    try {
                      final result = await mediaController.pickFile(
                        context,
                        imageUpload: !isVideoMode,
                        videoUpload: isVideoMode,
                        allowMultiple: true,
                        oldFilesLength: mediaList.value.length,
                      );

                      if (result != null) {
                        final files = result.files;

                        // Start upload phase for each file
                        for (var file in files) {
                          if (file.path != null) {
                            mediaController.startUpload(file.path!);
                          }
                        }

                        // Simulate upload progress
                        final uploadProgressTimers = <Timer>[];
                        for (var file in files) {
                          if (file.path != null) {
                            final filePath = file.path!;
                            var uploadProgress = 0.0;
                            final timer = Timer.periodic(
                                const Duration(milliseconds: 100), (timer) {
                              uploadProgress += 0.05;
                              if (uploadProgress >= 0.8) {
                                timer.cancel();
                                uploadProgressTimers.remove(timer);
                              } else {
                                mediaController.updateUploadProgress(
                                    filePath, uploadProgress);
                              }
                            });
                            uploadProgressTimers.add(timer);
                          }
                        }

                        final uploadedMedia =
                            await networkApiServices.uploadFiles(
                          filePaths: files
                              .map(
                                (e) => e.path,
                              )
                              .toList(),
                        );

                        // Cancel any remaining timers
                        for (var timer in uploadProgressTimers) {
                          timer.cancel();
                        }

                        Log.w('Uploaded_Media $uploadedMedia');

                        final uploadedFiles = uploadedMedia
                                ?.map(
                                  (e) => BaseMediaModel(
                                    id: e['id'],
                                    url: e['url'],
                                    type: _isVideoFile(e['url'] ?? '')
                                        ? MediaType.video
                                        : MediaType.image,
                                  ),
                                )
                                .toList() ??
                            <BaseMediaModel>[];

                        controllers![ApiStrings.media]!
                            .value
                            .addAll(uploadedFiles);

                        mediaList.value = controllers![ApiStrings.media]!.value;

                        // Complete upload for all files
                        for (var file in files) {
                          if (file.path != null) {
                            mediaController.completeUpload(file.path!);
                            Log.w('Uploaded_Media3333 ${file.path}');
                          }
                        }

                        if (onSavedMedia != null) {
                          onSavedMedia!();
                        }
                      }
                    } catch (e) {
                      Log.e('Error uploading media: $e');
                      // Show error message to user
                      context.showBarMessage(
                          e.toString().contains('too large')
                              ? context.tr.videoTooLarge
                              : 'Error uploading media: $e',
                          isError: true);
                    } finally {
                      isLoading.value = false;
                    }
                  },
                  label:
                      isVideoMode ? context.tr.addVideo : context.tr.addMedia,
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  bool _isVideoFile(String url) {
    final videoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.m4v'];
    return videoExtensions.any((ext) => url.toLowerCase().endsWith(ext));
  }

  Widget _buildVideoThumbnail(BaseMediaModel mediaItem, bool isNetworkMedia,
      MediaPickerController mediaController) {
    // Try to get thumbnail from controller first
    final thumbnail = mediaController.videoThumbnails[mediaItem.url];

    if (thumbnail != null) {
      return Image.memory(
        thumbnail,
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
      );
    }

    // For network videos, try to generate thumbnail
    if (isNetworkMedia && mediaItem.url != null) {
      return FutureBuilder<Uint8List?>(
        future: _generateNetworkVideoThumbnail(mediaItem.url!),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return _buildVideoPlaceholder(isLoading: true);
          }
          if (snapshot.hasData && snapshot.data != null) {
            return Image.memory(
              snapshot.data!,
              fit: BoxFit.cover,
              width: double.infinity,
              height: double.infinity,
            );
          }
          return _buildVideoPlaceholder();
        },
      );
    }

    // Fallback to a placeholder
    return _buildVideoPlaceholder();
  }

  Widget _buildVideoPlaceholder({bool isLoading = false}) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.grey[300],
      child: isLoading
          ? const Center(
              child: CircularProgressIndicator(
                strokeWidth: 2,
              ),
            )
          : const Icon(
              Icons.videocam,
              size: 48,
              color: Colors.grey,
            ),
    );
  }

  Future<Uint8List?> _generateNetworkVideoThumbnail(String videoUrl) async {
    try {
      return await VideoThumbnail.thumbnailData(
        video: videoUrl,
        imageFormat: ImageFormat.JPEG,
        quality: 75,
      );
    } catch (e) {
      Log.e('Error generating network video thumbnail: $e');
      return null;
    }
  }

  Widget _buildImageWidget(BaseMediaModel mediaItem, bool isNetworkMedia) {
    return isNetworkMedia
        ? Image.network(
            mediaItem.url ?? '',
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
            errorBuilder: (context, error, stackTrace) => const BaseCachedImage(
              AppConsts.activityPlaceholder,
            ),
          )
        : Image.file(
            File(mediaItem.url ?? ''),
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
          );
  }
}
