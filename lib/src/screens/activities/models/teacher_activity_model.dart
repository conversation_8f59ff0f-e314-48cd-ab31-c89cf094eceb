import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/shared/shared_models/base_media_model.dart';
import 'package:connectify_app/src/shared/shared_models/base_model.dart';

import '../../../shared/data/remote/api_strings.dart';
import 'activity_model.dart';

List<TeacherActivityModel> responseToTeacherActivityModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final activities = data.map((e) => TeacherActivityModel.fromJson(e)).toList();

  return activities;
}

class TeacherActivityModel extends BaseModel {
  final ClassModel? classModel;
  final TeacherModel? teacher;
  final ActivityModel? activity;
  final String startTime;
  final String endTime;
  final String day;
  final String date;
  final bool isWeekly;
  final List<ActivityNoteModel> notes;
  final DateTime? createdAtDate;

  const TeacherActivityModel({
    super.id,
    super.image,
    super.createdAt,
    this.createdAtDate,
    this.classModel,
    this.teacher,
    this.activity,
    this.day = '',
    this.date = '',
    this.startTime = '',
    this.endTime = '',
    this.isWeekly = true,
    this.notes = const [],
  });

  //? From Json
  factory TeacherActivityModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];

    final activity = attributes[ApiStrings.activity] == null
        ? null
        : attributes[ApiStrings.activity].containsKey(ApiStrings.data) &&
                attributes[ApiStrings.activity][ApiStrings.data] != null
            ? ActivityModel.fromJson(
                attributes[ApiStrings.activity][ApiStrings.data])
            : ActivityModel.fromJsonWithoutAttributes(
                attributes[ApiStrings.activity]);

    final classModel = attributes[ApiStrings.classString] == null
        ? null
        : attributes[ApiStrings.classString].containsKey(ApiStrings.data) &&
                attributes[ApiStrings.classString][ApiStrings.data] != null
            ? ClassModel.fromJson(
                attributes[ApiStrings.classString][ApiStrings.data])
            : ClassModel.fromJsonWithOutAttributes(
                attributes[ApiStrings.classString]);

    final notes = attributes[ApiStrings.activityNotes] != null
        ? List<ActivityNoteModel>.from(attributes[ApiStrings.activityNotes]
            .map((e) => ActivityNoteModel.fromJson(e)))
        : <ActivityNoteModel>[];

    return TeacherActivityModel(
        id: json[ApiStrings.id],
        day: attributes[ApiStrings.day] ?? '',
        date: attributes[ApiStrings.date] ?? '',
        startTime: attributes[ApiStrings.from] ?? '',
        endTime: attributes[ApiStrings.to] ?? '',
        classModel: classModel,
        createdAt: attributes[ApiStrings.createdAt],
        isWeekly: attributes[ApiStrings.isWeekly] ?? true,
        createdAtDate: DateTime.parse(attributes[ApiStrings.createdAt] ?? ''),
        notes: notes,
        activity: activity);
  }

//? To Json ---------------------------------------------------
  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      if (day.isNotEmpty) ApiStrings.day: day,
      if (date.isNotEmpty) ApiStrings.date: date,
      if (startTime.isNotEmpty) ApiStrings.from: startTime,
      if (endTime.isNotEmpty) ApiStrings.to: endTime,
      ApiStrings.teacher: const UserModel().currentUser.id,
      ApiStrings.classString: selectedTeacherClass.value?.id,
      //     const UserModel().currentUser.classes?.id,
      if (activity != null) ApiStrings.activity: activity?.id,
      ApiStrings.isWeekly: isWeekly,
      ApiStrings.activityNotes: notes.map((e) => e.toJson()).toList(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        image,
        createdAt,
        classModel,
        teacher,
        activity,
        startTime,
        endTime,
        day,
        date,
        isWeekly,
        notes,
      ];
}

class ActivityNoteModel {
  final String date;
  final String note;
  final List<BaseMediaModel> media;

  ActivityNoteModel({
    this.date = '',
    this.note = '',
    this.media = const [],
  });

  factory ActivityNoteModel.fromJson(Map<String, dynamic> json) {
    return ActivityNoteModel(
      date: json[ApiStrings.date] ?? '',
      note: json[ApiStrings.note] ?? '',
      media: json[ApiStrings.media] != null &&
              json[ApiStrings.media].runtimeType != List &&
              json[ApiStrings.media].containsKey(ApiStrings.data) &&
              json[ApiStrings.media][ApiStrings.data] != null
          ? (json[ApiStrings.media][ApiStrings.data] as List)
              .map((e) => BaseMediaModel.fromJson(e[ApiStrings.attributes],
                  id: e[ApiStrings.id]))
              .toList()
          : json[ApiStrings.media] != null
              ? json[ApiStrings.media].runtimeType == List
                  ? (json[ApiStrings.media] as List)
                      .map((e) =>
                          BaseMediaModel.fromJson(e, id: e[ApiStrings.id]))
                      .toList()
                  : <BaseMediaModel>[]
              : <BaseMediaModel>[],
    );
  }

  Map<String, dynamic> toJson() {
    log('FFFFF ${media.map(
      (e) => e.toJson(),
    )}');

    return {
      ApiStrings.date: date,
      ApiStrings.note: note,
      if (media.isNotEmpty) ApiStrings.media: media.map((e) => e.id).toList(),
    };
  }
}
