// * Class Provider Controller ========================================
import 'package:connectify_app/src/screens/Activities/models/activity_model.dart';
import 'package:connectify_app/src/screens/Activities/models/teacher_activity_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:xr_helper/xr_helper.dart';

import '../repos/teacher_activities_repo.dart';
import '../view/activiy/view/widgets/activity_card.dart';

final teacherActivityProviderController =
    Provider.family<TeacherActivityController, BuildContext>((ref, context) {
  final teacherActivityRepo = ref.watch(teacherActivityRepoProvider);

  return TeacherActivityController(
    context,
    teacherActivityRepo: teacherActivityRepo,
  );
});

final teacherActivityChangeNotifierProvider =
    ChangeNotifierProvider.family<TeacherActivityController, BuildContext>(
        (ref, context) {
  final teacherActivityRepo = ref.watch(teacherActivityRepoProvider);

  return TeacherActivityController(context,
      teacherActivityRepo: teacherActivityRepo);
});

// * Get Activities Data ========================================
final getTeacherActivitiesDataProvider =
    FutureProvider.family<List<TeacherActivityModel>, BuildContext>(
        (ref, context) async {
  final activityCtrl = ref.watch(teacherActivityProviderController(context));

  return await activityCtrl.getTeacherActivitiesData();
});

// * Get Teacher Activities By Class ========================================
final getTeacherActivitiesByClassDataProvider = FutureProvider.family<
    List<TeacherActivityModel>,
    (BuildContext, int? classId)>((ref, params) async {
  final activityCtrl = ref.watch(teacherActivityProviderController(params.$1));

  return await activityCtrl.getTeacherActivitiesData(classId: params.$2);
});

// * getTeacherActivitiesByClassAndDate
final getTeacherActivitiesByClassAndDateProvider = FutureProvider.family<
    List<TeacherActivityModel>,
    (BuildContext, int? classId, String monthName)>((ref, params) async {
  final activityCtrl = ref.watch(teacherActivityProviderController(params.$1));

  return await activityCtrl.getTeacherActivitiesByClassAndDate(
    classId: params.$2,
    monthName: params.$3,
  );
});

// * getTeacherActivitiesByDate
final getTeacherActivitiesByDateProvider = FutureProvider.family<
    List<TeacherActivityModel>,
    (BuildContext, String date, String day)>((ref, params) async {
  final activityCtrl = ref.watch(teacherActivityProviderController(params.$1));

  return await activityCtrl.getTeacherActivitiesByDate(
    dateFormat: params.$2,
    dayFormat: params.$3,
  );
});

// * getTeacherActivitiesByDate
final getTeacherActivitiesByDateProviderByClassId = FutureProvider.family<
    List<TeacherActivityModel>,
    (BuildContext, String date, String day, int? classId)>((ref, params) async {
  final activityCtrl = ref.watch(teacherActivityProviderController(params.$1));

  return await activityCtrl.getTeacherActivitiesByDate(
    dateFormat: params.$2,
    dayFormat: params.$3,
    classId: params.$4,
  );
});

final getTeacherActivitiesByTodayDataProvider =
    FutureProvider.family<List<TeacherActivityModel>, BuildContext>(
        (ref, context) async {
  final activityCtrl = ref.watch(teacherActivityProviderController(context));

  final activities = await activityCtrl.getTeacherActivitiesByDay();

  DateTime now = DateTime.now();

  return activities.where((element) {
    final format = DateFormat('HH:mm');
    final startTime = format.parse(element.startTime);
    final endTime = format.parse(element.endTime);

    final startTimeDate = DateTime(
        now.year, now.month, now.day, startTime.hour, startTime.minute);
    final endTimeDate =
        DateTime(now.year, now.month, now.day, endTime.hour, endTime.minute);

    return now.isAfter(startTimeDate) && now.isBefore(endTimeDate);
  }).toList();
});

class TeacherActivityController extends BaseVM {
  final BuildContext context;
  final TeacherActivityRepo teacherActivityRepo;

  TeacherActivityController(this.context, {required this.teacherActivityRepo});

  //? Get Activities Data ------------------------------------------
  Future<List<TeacherActivityModel>> getTeacherActivitiesData(
      {int? classId}) async {
    return await baseFunction(
      context,
      () async {
        final activities =
            await teacherActivityRepo.getActivities(classId: classId);

        return activities;
      },
    );
  }

  //? Get Activities Data ------------------------------------------
  Future<List<TeacherActivityModel>> getTeacherActivitiesByDay() async {
    return await baseFunction(
      context,
      () async {
        final activities = await teacherActivityRepo.getActivitiesByDay();

        return activities;
      },
    );
  }

  Future<List<TeacherActivityModel>> getTeacherActivitiesByClassAndDate({
    required int? classId,
    required String monthName,
  }) async {
    return await baseFunction(
      context,
      () async {
        final activities =
            await teacherActivityRepo.getActivitiesByClassAndDate(
          classId: classId,
          monthName: monthName,
        );

        return activities;
      },
    );
  }

  Future<List<TeacherActivityModel>> getTeacherActivitiesByDate({
    required String dayFormat,
    required String dateFormat,
    int? classId,
  }) async {
    return await baseFunction(
      context,
      () async {
        final activities = await teacherActivityRepo.getActivitiesByDate(
          dateFormat: dateFormat,
          dayFormat: dayFormat,
          classId: classId,
        );

        return activities;
      },
    );
  }

//? Add Activities Data ------------------------------------------
  Future<void> addTeacherActivity({
    required int id,
    required String day,
    String date = '',
    bool isWeekly = true,
  }) async {
    return await baseFunction(context, () async {
      final activity = ActivityModel(
        id: id,
      );

      await teacherActivityRepo.addActivity(
        teacherActivity: TeacherActivityModel(
          activity: activity,
          day: day,
          date: date,
          isWeekly: isWeekly,
          startTime: '00:00',
          endTime: '00:00',
        ),
      );

      // for (var student
      //     in teacherActivityModel?.classModel?.students ?? <StudentModel>[]) {
      //   NotificationService.sendNotification(
      //     title: "Activity Started",
      //     body:
      //         "${activity.name} has started for ${student.name} and other participating students.",
      //     userTokenOrTopic: NurseryModelHelper.parentByStudentTopic(student.id),
      //     isTopic: true,
      //   );
      //
      //   postNewNotification(
      //       notificationModel: NotificationModel(
      //           title: "Activity Started",
      //           body:
      //               "${activity.name} has started for ${student.name} and other participating students.",
      //           topic: NurseryModelHelper.parentByStudentTopic(student.id)));
      // }

      // if (!context.mounted) return;
      //
      // context.back();
      // context.to(const TeacherActivitiesScreen());
      // context.showBarMessage(context.tr.addedSuccessfully);
      // getTeacherActivitiesData();
    });
  }

  //? Edit Activity Data ------------------------------------------
  Future<void> editActivity({
    required TeacherActivityModel teacherActivity,
    required int id,
  }) async {
    return await baseFunction(context, () async {
      await teacherActivityRepo.editActivity(
        activity: teacherActivity,
        id: id,
      );
    });
  }

  //? Delete Activity Data ------------------------------------------
  Future<void> deleteActivity({required int id}) async {
    return await baseFunction(context, () async {
      await teacherActivityRepo.deleteActivity(id: id);

      getTeacherActivitiesData();
      if (!context.mounted) return;
      context.back();
      // context.to(const TeacherActivitiesScreen());
      context.showBarMessage(context.tr.deletedSuccessfully, isError: true);
    });
  }

  void onNext({required ValueNotifier<int> selectedIndex}) {
    selectedIndex.value = (selectedIndex.value + 1) % weekDays.length;
  }

  void onPrev({required ValueNotifier<int> selectedIndex}) {
    selectedIndex.value =
        (selectedIndex.value - 1 + weekDays.length) % weekDays.length;
  }

// List<String> attendanceDates({required List<AttendanceModel> attendances}) =>
//     attendances.map((e) => e.attendanceDate).toList();
}
