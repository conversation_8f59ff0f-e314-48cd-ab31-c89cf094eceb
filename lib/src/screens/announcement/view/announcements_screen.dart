import 'package:connectify_app/src/screens/announcement/controller/announcement_controller.dart';
import 'package:connectify_app/src/screens/announcement/model/announcement_model.dart';
import 'package:connectify_app/src/screens/announcement/view/add_announcement_screen.dart';
import 'package:connectify_app/src/screens/announcement/view/widgets/announcement_card.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_list/base_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../shared/widgets/shared_widgets.dart';
import '../../home/<USER>/main_screen/widgets/main_app_bar.dart';

class AnnouncementsScreen extends HookConsumerWidget {
  const AnnouncementsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final page = useState<int>(1);
    final isLoadingMore = useState<bool>(false);
    final isInitialLoadComplete = useState<bool>(false);
    final announcements = useState<List<AnnouncementModel>>([]);

    final params = (context, page.value);

    useEffect(() {
      ref.refresh(getAnnouncementsDataProviderWithPagination(params));
      return () {};
    }, [page.value]);

    ref.listenPagination<AnnouncementModel>(
      provider: getAnnouncementsDataProviderWithPagination(params),
      dataNotifier: announcements,
      isLoadingNotifier: isLoadingMore,
      isInitialLoadCompleteNotifier: isInitialLoadComplete,
    );

    return WillPopScope(
      onWillPop: () async {
        context.toReplacement(const MainScreen());
        return false;
      },
      child: Scaffold(
        appBar: MainAppBar(
          isBackButton: true,
          title: context.tr.announcements,
          iconPath: '',
        ),
        body: Column(
          children: [
            if (const UserModel().isCurrentUserAdmin ||
                hasPermission(const UserModel().currentUser, 'addAnnouncements'))
              Padding(
                padding: const EdgeInsets.all(AppSpaces.largePadding),
                child: AddRectangleWidget(
                    title: context.tr.addAnnouncement,
                    onTap: () {
                      context.to(const AddAnnouncementScreen());
                    }),
              ),
            Expanded(
              child: BaseList(
                page: page,
                isLoading: !isInitialLoadComplete.value,
                padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                isLoadingMore: isLoadingMore,
                data: announcements.value,
                itemBuilder: (announcement, index) => AnnouncementCard(
                  announcement: announcement,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
