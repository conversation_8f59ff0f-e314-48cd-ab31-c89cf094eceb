import 'package:connectify_app/src/screens/announcement/controller/announcement_controller.dart';
import 'package:connectify_app/src/screens/announcement/model/announcement_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../auth/models/user_model.dart';
import '../add_announcement_screen.dart';

class AnnouncementCard extends HookConsumerWidget {
  final AnnouncementModel announcement;

  const AnnouncementCard({
    super.key,
    required this.announcement,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return BaseContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and actions
          Row(
            children: [
              if (announcement.createdAt != null)
                Expanded(
                  child: Text(
                    announcement.createdAt!.formatDateToTimeAndString,
                    style: context.labelSmall.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              context.mediumGap,

              // Target badge
              Row(
                children: [
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getTargetColor(announcement.target),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      context.isEng
                          ? announcement.target.displayName
                          : announcement.target.displayNameAr,
                      style: context.labelSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),

          context.mediumGap,

          Text(
            announcement.title,
            style: context.title?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          // Footer with admin name and date
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  announcement.description,
                  style: context.labelLarge,
                ),
              ),
              if (const UserModel().isCurrentUserAdmin ||
                  hasPermission(
                      const UserModel().currentUser, 'addAnnouncements')) ...[
                // Delete button
                Row(
                  children: [
                    // IconButton(
                    //   onPressed: () {
                    //     _showDeleteDialog(context, announcementCtrl);
                    //   },
                    //   icon:
                    //       const Icon(CupertinoIcons.delete, color: Colors.red),
                    //   tooltip: context.tr.delete,
                    // ),
                    IconButton(
                      onPressed: () {
                        context.to(AddAnnouncementScreen(
                          announcement: announcement,
                        ));
                      },
                      icon: Row(
                        children: [
                          Text(
                            context.tr.resend,
                            style: context.labelSmall,
                          ),
                          context.mediumGap,
                          const Icon(Icons.send),
                        ],
                      ),
                      tooltip: context.tr.resend,
                    ),
                  ],
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Color _getTargetColor(AnnouncementTarget target) {
    switch (target) {
      case AnnouncementTarget.admins:
        return Colors.red;
      case AnnouncementTarget.teachers:
        return Colors.blue;
      case AnnouncementTarget.parents:
        return Colors.green;
      case AnnouncementTarget.classes:
        return Colors.orange;
      case AnnouncementTarget.all:
        return Colors.purple;
    }
  }

  void _showDeleteDialog(
      BuildContext context, AnnouncementController controller) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(context.tr.deleteAnnouncement),
          content: Text(context.tr.areYouSureYouWantToDeleteThisAnnouncement),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(context.tr.cancel),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await controller.deleteAnnouncement(id: announcement.id!);
              },
              child: Text(
                context.tr.delete,
                style: const TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }
}
