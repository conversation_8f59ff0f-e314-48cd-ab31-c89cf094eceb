import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/screens/messages/controller/messages_controller.dart';
import 'package:connectify_app/src/screens/messages/model/messages_model.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/shared_models/base_media_model.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import 'messages__card_widget.dart';

class MessagesList extends ConsumerWidget {
  const MessagesList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final studentCtrl = ref.watch(getAllMessageData(context));

    return studentCtrl.get(
      data: (messages) {
        final messagesByStudent = groupBy<
            MessageModel,
            (
              int? studentId,
              String? studentName,
              String? studentImage,
              int? classId,
            )>(messages, (message) {
          return (
            message.student?.id,
            message.student?.name,
            message.student?.image?.url,
            message.student?.classModel?.id,
          );
        });

        if (const UserModel().isCurrentUserTeacher) {
          messagesByStudent.removeWhere(
              (key, value) => key.$4 != selectedTeacherClass.value?.id);
        }

        return ListView.separated(
          shrinkWrap: true,
          itemCount: messagesByStudent.length,
          padding: const EdgeInsets.all(AppSpaces.mediumPadding),
          itemBuilder: (context, index) {
            final student = messagesByStudent.keys.elementAt(index);
            List<MessageModel> studentMessages;

            if (const UserModel().isCurrentUserAdmin) {
              studentMessages = messagesByStudent[student]!;
            } else {
              studentMessages = messagesByStudent[student]!
                  .where((element) =>
                      element.admin == null && element.teacher != null)
                  .toList();
            }

            return MessagesCardWidget(
              student: StudentModel(
                  id: student.$1,
                  name: student.$2 ?? '',
                  image: BaseMediaModel(url: student.$3)),
              messages: studentMessages,
            );
          },
          separatorBuilder: (context, index) => context.smallGap,
        );
      },
    );
  }
}
