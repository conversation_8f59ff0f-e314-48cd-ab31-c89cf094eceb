import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';

import '../../nursery/models/nursery_model_helper.dart';

List<ActivityLevelModel> responseToActivityLevelModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final activityLevelData =
      data.map((e) => ActivityLevelModel.fromJson(e)).toList();

  return activityLevelData;
}

enum ActivityLevelTypes { low, medium, high }

class ActivityLevelModel extends Equatable {
  final int? id;
  final ActivityLevelTypes? activityLevel;
  final String? note;
  final StudentModel? student;
  final NurseryModel? nursery;
  final TeacherModel? teacher;

  const ActivityLevelModel({
    this.id,
    this.activityLevel,
    this.note,
    this.student,
    this.nursery,
    this.teacher,
  });

  factory ActivityLevelModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];
    return ActivityLevelModel(
      id: json[ApiStrings.id],
      activityLevel: getActivityLevelFromApi(
          attributes[ApiStrings.activityLevel].toString().toLowerCase()),
      note: attributes[ApiStrings.note],
    );
  }

  static ActivityLevelTypes? getActivityLevelFromApi(String? activityLevel) {
    switch (activityLevel?.toLowerCase()) {
      case 'low':
        return ActivityLevelTypes.low;
      case 'medium':
        return ActivityLevelTypes.medium;
      case 'high':
        return ActivityLevelTypes.high;
      default:
        return null;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.activityLevel: getActivityLevel(),
      if (note != null) ApiStrings.note: note,
      ApiStrings.student: student?.id,
      ApiStrings.teacher: const UserModel().currentUser.id,
      ApiStrings.nursery: NurseryModelHelper.currentNurseryId()
    };
  }

  String getActivityLevel() {
    switch (activityLevel) {
      case ActivityLevelTypes.low:
        return "low";
      case ActivityLevelTypes.medium:
        return "medium";
      case ActivityLevelTypes.high:
        return "high";
      default:
        return 'low';
    }
  }

  //getActivityLevel context lang
  String getActivityLevelContextLang(BuildContext context) {
    switch (activityLevel) {
      case ActivityLevelTypes.low:
        return context.tr.low;
      case ActivityLevelTypes.medium:
        return context.tr.medium;
      case ActivityLevelTypes.high:
        return context.tr.high;
      default:
        return context.tr.low;
    }
  }

  static ActivityLevelTypes getActivityLevelValue({required int value}) {
    switch (value) {
      case 0:
        return ActivityLevelTypes.low;
      case 1:
        return ActivityLevelTypes.medium;
      case 2:
        return ActivityLevelTypes.high;
      default:
        return ActivityLevelTypes.low;
    }
  }

  @override
  List<Object?> get props =>
      [id, activityLevel, note, student, nursery, teacher];
}
