import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/widgets/drop_downs/multi_student_drop_down.dart';
import '../../../../shared/widgets/tab_bar_widgets/add_container_widget.dart';

class AddActivityLevelWidgets extends HookWidget {
  final Map<String, ValueNotifier> valueNotifiers;

  const AddActivityLevelWidgets({super.key, required this.valueNotifiers});

  @override
  Widget build(BuildContext context) {
    final List<String> activityLevelList = [
      context.tr.low,
      context.tr.medium,
      context.tr.high,
    ];

    final List<String> activityLevelImages = [
      'assets/svg/low.png',
      'assets/svg/medium.png',
      'assets/svg/high.png',
    ];

    final indexActivityLevelValue = useState<int>(0);
    final noteController = useTextEditingController();

    useEffect(() {
      valueNotifiers[ApiStrings.note]?.addListener(() {
        noteController.text = valueNotifiers[ApiStrings.note]?.value ?? '';
      });
      return null;
    }, []);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        MultiStudentDropDown(
          selectedStudents: valueNotifiers[ApiStrings.students]
              as ValueNotifier<List<StudentModel>>,
        ),
        context.mediumGap,
        Text(
          context.tr.activityLevel,
          style: context.title,
        ),
        context.smallGap,
        //AddContainerWidget(
        //                 value: index,
        //                 title: activityLevelList[index],
        //                 groupValue: valueNotifiers[ApiStrings.activityLevel]
        //                     as ValueNotifier<int>,
        //                 imagePath: activityLevelImages[index],
        //                 index: indexActivityLevelValue,
        //                 onTap: () {
        //                   indexActivityLevelValue.value = index;
        //                   valueNotifiers[ApiStrings.activityLevel]?.value = index;
        //                 },
        //               )
        SizedBox(
          height: 120.h,
          child: Row(
            children: activityLevelList
                .asMap()
                .entries
                .indexed
                .map((e) => Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: AddContainerWidget(
                          value: e.$1,
                          title: activityLevelList[e.$1],
                          groupValue: valueNotifiers[ApiStrings.activityLevel]
                              as ValueNotifier<int>,
                          imagePath: activityLevelImages[e.$1],
                          index: indexActivityLevelValue,
                          onTap: () {
                            indexActivityLevelValue.value = e.$1;
                            valueNotifiers[ApiStrings.activityLevel]?.value =
                                e.$1;
                          },
                        ),
                      ),
                    ))
                .toList(),
          ),
        ),

        context.mediumGap,
        Text(
          context.tr.note,
          style: context.title,
        ),
        context.smallGap,
        BaseTextField(
          isRequired: false,
          controller: noteController,
          label: context.tr.note,
          hint: context.tr.enterNote,
          maxLines: 3,
          onChanged: (value) {
            valueNotifiers[ApiStrings.note]?.value = value;
          },
        ),
      ],
    );
  }
}
