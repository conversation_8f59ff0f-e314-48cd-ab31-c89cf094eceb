enum MediaType { image, video }

class BaseMediaModel {
  final int? id;
  final String? publicId;
  final String? url;
  final MediaType type;
  final String? thumbnailUrl; // For video thumbnails

  const BaseMediaModel({
    this.id,
    required this.url,
    this.publicId,
    this.type = MediaType.image,
    this.thumbnailUrl,
  });

  factory BaseMediaModel.fromJson(Map<String, dynamic>? json, {int? id}) {
    if (json == null) {
      return BaseMediaModel.empty();
    }

    final publicId = json['provider_metadata'] != null
        ? json['provider_metadata']['public_id']
        : '';

    // Determine media type based on file extension or mime type
    final url = json['url'] ?? '';
    final isVideo = _isVideoFile(url);

    return BaseMediaModel(
      id: id ?? json['id'],
      publicId: publicId,
      url: url,
      type: isVideo ? MediaType.video : MediaType.image,
      thumbnailUrl: json['thumbnail_url'],
    );
  }

  static bool _isVideoFile(String url) {
    final videoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.m4v'];
    return videoExtensions.any((ext) => url.toLowerCase().endsWith(ext));
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      "public_id": publicId,
      "url": url,
      "type": type.name,
      if (thumbnailUrl != null) "thumbnail_url": thumbnailUrl,
    };
  }

  factory BaseMediaModel.empty() => const BaseMediaModel(url: '');

  bool get isVideo => type == MediaType.video;
  bool get isImage => type == MediaType.image;
}
