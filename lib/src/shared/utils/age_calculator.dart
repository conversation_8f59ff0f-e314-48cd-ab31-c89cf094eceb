import 'package:flutter/material.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';

class AgeCalculator {
  /// Calculates age from birth date and returns formatted string
  /// Returns format like "3 years, 2 months" or "8 months" or "2 weeks"
  static String calculateAge(DateTime? birthDate, BuildContext context) {
    if (birthDate == null) return '';

    final now = DateTime.now();
    final difference = now.difference(birthDate);

    // If less than 1 month old, show weeks
    if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      if (weeks == 0) {
        return context.tr.newborn;
      } else if (weeks == 1) {
        return context.tr.oneWeek;
      } else {
        return context.tr.weeksOld(weeks);
      }
    }

    // Calculate years and months
    int years = now.year - birthDate.year;
    int months = now.month - birthDate.month;

    // Adjust if the current day is before the birth day
    if (now.day < birthDate.day) {
      months--;
    }

    // Adjust if months is negative
    if (months < 0) {
      years--;
      months += 12;
    }

    // Format the result
    if (years == 0) {
      if (months == 1) {
        return context.tr.oneMonth;
      } else {
        return context.tr.monthsOld(months);
      }
    } else if (months == 0) {
      if (years == 1) {
        return context.tr.oneYear;
      } else {
        return context.tr.yearsOld(years);
      }
    } else {
      String yearText = years == 1 ? context.tr.oneYear : context.tr.yearsOld(years);
      String monthText = months == 1 ? context.tr.oneMonth : context.tr.monthsOld(months);
      return '$yearText, $monthText';
    }
  }

  /// Returns a short age format for display in cards
  /// Returns format like "3y 2m" or "8m" or "2w"
  static String calculateShortAge(DateTime? birthDate, BuildContext context) {
    if (birthDate == null) return '';

    final now = DateTime.now();
    final difference = now.difference(birthDate);

    // If less than 1 month old, show weeks
    if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      if (weeks == 0) {
        return context.tr.newbornShort;
      } else {
        return context.tr.weeksShort(weeks);
      }
    }

    // Calculate years and months
    int years = now.year - birthDate.year;
    int months = now.month - birthDate.month;

    // Adjust if the current day is before the birth day
    if (now.day < birthDate.day) {
      months--;
    }

    // Adjust if months is negative
    if (months < 0) {
      years--;
      months += 12;
    }

    // Format the result
    if (years == 0) {
      return context.tr.monthsShort(months);
    } else if (months == 0) {
      return context.tr.yearsShort(years);
    } else {
      return context.tr.yearsMonthsShort(years, months);
    }
  }
}
