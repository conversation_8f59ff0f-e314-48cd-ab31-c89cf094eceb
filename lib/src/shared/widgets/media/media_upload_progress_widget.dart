import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class MediaUploadProgressWidget extends StatefulWidget {
  final double progress;
  final bool isCompressing;
  final bool isUploading;
  final bool isCompleted;
  final String fileName;
  final VoidCallback? onCancel;

  const MediaUploadProgressWidget({
    super.key,
    required this.progress,
    required this.isCompressing,
    required this.isUploading,
    required this.isCompleted,
    required this.fileName,
    this.onCancel,
  });

  @override
  State<MediaUploadProgressWidget> createState() =>
      _MediaUploadProgressWidgetState();
}

class _MediaUploadProgressWidgetState extends State<MediaUploadProgressWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.progress,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void didUpdateWidget(MediaUploadProgressWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.progress != widget.progress) {
      _progressAnimation = Tween<double>(
        begin: oldWidget.progress,
        end: widget.progress,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ));
      _animationController.reset();
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // File name and status
          Row(
            children: [
              Icon(
                widget.isCompressing
                    ? Icons.compress
                    : widget.isUploading
                        ? Icons.cloud_upload
                        : widget.isCompleted
                            ? Icons.check_circle
                            : Icons.check_circle,
                color: widget.isCompleted
                    ? Colors.green
                    : ColorManager.primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  widget.fileName,
                  style: context.body?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (widget.onCancel != null)
                IconButton(
                  icon: const Icon(Icons.close, size: 20),
                  onPressed: widget.onCancel,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
            ],
          ),

          // Progress bar
          Row(
            children: [
              Expanded(
                child: AnimatedBuilder(
                  animation: _progressAnimation,
                  builder: (context, child) {
                    return LinearProgressIndicator(
                      value: _progressAnimation.value,
                      backgroundColor: Colors.grey[300],
                      valueColor: AlwaysStoppedAnimation<Color>(
                        widget.isCompleted
                            ? Colors.green
                            : ColorManager.primaryColor,
                      ),
                      minHeight: 6,
                    );
                  },
                ),
              ),
              const SizedBox(width: 12),
              AnimatedBuilder(
                animation: _progressAnimation,
                builder: (context, child) {
                  return Text(
                    '${(_progressAnimation.value * 100).toInt()}%',
                    style: context.body?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: widget.isCompleted
                          ? Colors.green
                          : ColorManager.primaryColor,
                    ),
                  );
                },
              ),
            ],
          ),

          const SizedBox(height: 4),

          // Status text
          Text(
            widget.isCompressing
                ? context.tr.compressing
                : widget.isUploading
                    ? context.tr.uploading
                    : widget.isCompleted
                        ? context.tr.complete
                        : context.tr.complete,
            style: context.body?.copyWith(
              color: widget.isCompleted ? Colors.green : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}

class MediaUploadProgressList extends StatelessWidget {
  final Map<String, double> progressMap;
  final Map<String, bool> isCompressingMap;
  final Map<String, bool> isUploadingMap;
  final Map<String, bool> isCompletedMap;
  final Function(String)? onCancel;

  const MediaUploadProgressList({
    super.key,
    required this.progressMap,
    required this.isCompressingMap,
    required this.isUploadingMap,
    required this.isCompletedMap,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    // Filter out completed items completely
    final activeEntries = progressMap.entries.where((entry) {
      final isCompleted = isCompletedMap[entry.key] ?? false;
      return !isCompleted;
    }).toList();

    if (activeEntries.isEmpty) return const SizedBox.shrink();

    return ListView(
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      children: activeEntries.reversed.map((entry) {
        final fileName = entry.key.split('/').last;
        final progress = entry.value;
        final isCompressing = isCompressingMap[entry.key] ?? false;
        final isUploading = isUploadingMap[entry.key] ?? false;
        final isCompleted = isCompletedMap[entry.key] ?? false;

        return MediaUploadProgressWidget(
          progress: progress,
          isCompressing: isCompressing,
          isUploading: isUploading,
          isCompleted: isCompleted,
          fileName: fileName,
          onCancel: onCancel != null ? () => onCancel!(entry.key) : null,
        );
      }).toList(),
    );
  }
}
