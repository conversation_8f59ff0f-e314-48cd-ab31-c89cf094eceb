import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:xr_helper/xr_helper.dart';

import '../base_container.dart';

class AddContainerWidget extends StatelessWidget {
  final String title;
  final String imagePath;
  final ValueNotifier<int> groupValue;
  final Function(dynamic)? onChanged;
  final Function()? onTap;
  final int value;
  final ValueNotifier<int> index;

  const AddContainerWidget(
      {super.key,
      required this.title,
      required this.imagePath,
      this.onTap,
      required this.groupValue,
      this.onChanged,
      required this.value,
      required this.index});

  @override
  Widget build(BuildContext context) {
    final isSvg = imagePath.endsWith('.svg');
    return InkWell(
      onTap: onTap,
      child: BaseContainer(
        borderColor: value == index.value
            ? ColorManager.primaryColor
            : ColorManager.grey,
        child: Column(
          children: [
            isSvg
                ? SvgPicture.asset(
                    imagePath,
                    width: 50,
                    height: 50,
                  )
                : Image.asset(
                    imagePath,
                    width: 50,
                    height: 50,
                  ),
            context.smallGap,
            Text(title),
            Radio.adaptive(
              activeColor: ColorManager.primaryColor,
              focusColor: ColorManager.primaryColor,
              groupValue: groupValue.value,
              value: value,
              onChanged: onChanged,
            ),
          ],
        ),
      ),
    );
  }
}
