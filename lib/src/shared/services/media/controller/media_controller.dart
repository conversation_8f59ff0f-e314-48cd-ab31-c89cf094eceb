import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:video_compress/video_compress.dart';
import 'package:xr_helper/xr_helper.dart';

import '../repository/local_media_repo.dart';

final mediaPickerControllerProvider =
    ChangeNotifierProvider<MediaPickerController>(
  (ref) {
    final mediaLocalRepoProvider = ref.watch(localMediaRepoProvider);

    return MediaPickerController(mediaLocalRepoProvider);
  },
);

class MediaPickerController extends ChangeNotifier {
  final LocalMediaRepo _mediaRepo;

  MediaPickerController(this._mediaRepo);

  FilePickerResult? _filePickerResult;

  // Track compression/upload progress
  final Map<String, double> _compressionProgress = {};
  final Map<String, bool> _isCompressing = {};
  final Map<String, bool> _isUploading = {};
  final Map<String, bool> _isCompleted = {};
  final Map<String, Uint8List?> _videoThumbnails = {};
  final Map<String, String> _originalPaths = {}; // Maps compressed path to original path

  // Getters for progress tracking
  Map<String, double> get compressionProgress =>
      Map.unmodifiable(_compressionProgress);
  Map<String, bool> get isCompressing => Map.unmodifiable(_isCompressing);
  Map<String, bool> get isUploading => Map.unmodifiable(_isUploading);
  Map<String, bool> get isCompleted => Map.unmodifiable(_isCompleted);
  Map<String, Uint8List?> get videoThumbnails =>
      Map.unmodifiable(_videoThumbnails);

  // Get only active (non-completed) progress items
  Map<String, double> get activeCompressionProgress {
    final activeProgress = <String, double>{};
    for (final entry in _compressionProgress.entries) {
      final isCompleted = _isCompleted[entry.key] ?? false;
      if (!isCompleted) {
        activeProgress[entry.key] = entry.value;
      }
    }
    return Map.unmodifiable(activeProgress);
  }

  // Check if there are any active progress items
  bool get hasActiveProgress {
    return _compressionProgress.entries.any((entry) {
      final isCompleted = _isCompleted[entry.key] ?? false;
      return !isCompleted;
    });
  }

  List<String> get filesPaths =>
      _filePickerResult?.paths.map((e) => e ?? '').toList() ?? [];

  String get filePath => _filePickerResult?.files.firstOrNull?.path ?? '';

  // Map to store section images with their IDs
  final Map<String, String> _sectionImages = {};

  // Getter for section images
  Map<String, String> get sectionImages => _sectionImages;

  // Set an image for a specific section
  void setSectionImage(String sectionId, String path) {
    _sectionImages[sectionId] = path;
    notifyListeners();
  }

  // Remove an image for a specific section
  void removeSectionImage(String sectionId) {
    _sectionImages.remove(sectionId);
    notifyListeners();
  }

  // Clear all section images
  void clearSectionImages() {
    _sectionImages.clear();
    notifyListeners();
  }

  Future<FilePickerResult> compressAndGetFile(
      File file, String targetPath) async {
    var result = await FlutterImageCompress.compressAndGetFile(
      file.absolute.path,
      targetPath,
      quality: 75,
      rotate: 0,
    );

    Log.i('Compressed File: ${result?.path}');

    if (result == null) {
      throw Exception('Failed to compress file');
    }

    final platformFile = PlatformFile(
      name: result.path.split('/').last,
      path: result.path,
      bytes: await result.readAsBytes(),
      size: await result.length(),
    );

    return FilePickerResult([platformFile]);
  }

  Future<FilePickerResult> compressVideo(File file, String filePath) async {
    try {
      _isCompressing[filePath] = true;
      _compressionProgress[filePath] = 0.0;
      _isCompleted[filePath] = false;
      notifyListeners();

      // Set up progress listener
      VideoCompress.setLogLevel(0);

      // Simulate progress updates during compression
      _updateCompressionProgress(filePath);

      var info = await VideoCompress.compressVideo(
        file.path,
        quality: VideoQuality.MediumQuality,
        deleteOrigin: false,
        includeAudio: true,
      );

      if (info == null) {
        throw Exception('Failed to compress video');
      }

      // Check if compressed video is under 50MB
      final compressedFile = File(info.path!);
      final fileSizeInBytes = await compressedFile.length();
      final fileSizeInMB = fileSizeInBytes / (1024 * 1024);

      if (fileSizeInMB > 50) {
        // Try with lower quality
        final lowQualityInfo = await VideoCompress.compressVideo(
          file.path,
          quality: VideoQuality.LowQuality,
          deleteOrigin: false,
          includeAudio: true,
        );

        if (lowQualityInfo != null) {
          final lowQualityFile = File(lowQualityInfo.path!);
          final lowQualitySize = await lowQualityFile.length() / (1024 * 1024);

          if (lowQualitySize <= 50) {
            info = lowQualityInfo;
          } else {
            throw Exception(
                'Video file too large even after compression. Please select a smaller video.');
          }
        }
      }

      // Generate thumbnail
      final thumbnail = await VideoCompress.getByteThumbnail(
        info.path!,
        quality: 50,
        position: -1,
      );

      if (thumbnail != null) {
        _videoThumbnails[filePath] = thumbnail;
      }

      // Map compressed path to original path
      _originalPaths[info.path!] = filePath;
      
      // Transfer progress to compressed path
      _compressionProgress[info.path!] = 1.0;
      _isCompressing[info.path!] = false;
      _isCompleted[info.path!] = false;
      
      // Remove original path tracking
      _compressionProgress.remove(filePath);
      _isCompressing.remove(filePath);
      _isCompleted.remove(filePath);
      
      notifyListeners();

      final platformFile = PlatformFile(
        name: info.path!.split('/').last,
        path: info.path!,
        bytes: await File(info.path!).readAsBytes(),
        size: await File(info.path!).length(),
      );

      return FilePickerResult([platformFile]);
    } catch (e) {
      _isCompressing[filePath] = false;
      _compressionProgress.remove(filePath);
      _isCompleted.remove(filePath);
      notifyListeners();
      rethrow;
    }
  }

  Future<FilePickerResult?> pickFile(
    BuildContext context, {
    bool imageUpload = true,
    bool videoUpload = false,
    bool allowMultiple = false,
    int oldFilesLength = 2,
  }) async {
    try {
      final pickedFiles = await _mediaRepo.pickFiles(
        context,
        imageUpload: imageUpload,
        videoUpload: videoUpload,
        uploadMultiple: allowMultiple,
        oldFilesLength: oldFilesLength,
      );

      if (pickedFiles == null) return null;

      List<PlatformFile> processedFiles = [];

      for (var file in pickedFiles.files) {
        final filePath = file.path!;
        final isVideo = _isVideoFile(filePath);

        // Initialize progress tracking
        updateProgress(filePath, 0.0, isCompressing: true);

        if (isVideo && videoUpload) {
          // Compress video
          var result = await compressVideo(File(filePath), filePath);
          if (result.files.isNotEmpty) {
            processedFiles.add(result.files.first);
          }
        } else if (!isVideo && imageUpload) {
          // Compress image
          updateProgress(filePath, 0.5, isCompressing: true);
          var result = await compressAndGetFile(
              File(filePath), '${filePath}.compressed.jpg');
          if (result.files.isNotEmpty) {
            processedFiles.add(result.files.first);
          }
          updateProgress(filePath, 1.0, isCompressing: false);
        } else {
          // Add file as is
          processedFiles.add(file);
          updateProgress(filePath, 1.0, isCompressing: false);
        }
      }

      _filePickerResult = FilePickerResult(processedFiles);
      notifyListeners();

      return _filePickerResult;
    } on Exception catch (e) {
      Log.e('Error Getting File $e');
      rethrow;
    }
  }

  bool _isVideoFile(String filePath) {
    final videoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.m4v'];
    return videoExtensions.any((ext) => filePath.toLowerCase().endsWith(ext));
  }

  // Simulate compression progress updates
  void _updateCompressionProgress(String filePath) {
    Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (!_isCompressing.containsKey(filePath) || !_isCompressing[filePath]!) {
        timer.cancel();
        return;
      }

      final currentProgress = _compressionProgress[filePath] ?? 0.0;
      if (currentProgress < 0.9) {
        _compressionProgress[filePath] = currentProgress + 0.1;
        notifyListeners();
      } else {
        timer.cancel();
      }
    });
  }

  // Update progress for a specific file
  void updateProgress(String filePath, double progress,
      {bool isCompressing = false, bool isUploading = false}) {
    _compressionProgress[filePath] = progress;
    _isCompressing[filePath] = isCompressing;
    _isUploading[filePath] = isUploading;
    _isCompleted[filePath] = !isCompressing && !isUploading && progress >= 1.0;
    notifyListeners();
  }

  // Start upload phase for a file
  void startUpload(String filePath) {
    _isCompressing[filePath] = false;
    _isUploading[filePath] = true;
    _compressionProgress[filePath] = 0.0;
    _isCompleted[filePath] = false;
    notifyListeners();
  }

  // Update upload progress
  void updateUploadProgress(String filePath, double progress) {
    _compressionProgress[filePath] = progress;
    _isUploading[filePath] = progress < 1.0;
    _isCompleted[filePath] = progress >= 1.0;
    notifyListeners();
  }

  // Complete upload and mark for removal
  void completeUpload(String filePath) {
    // Check if this is a compressed path, get original if exists
    final originalPath = _originalPaths[filePath];
    
    // Remove progress for both paths
    removeProgress(filePath);
    if (originalPath != null) {
      removeProgress(originalPath);
      _originalPaths.remove(filePath);
    }
  }

  // Remove progress for a specific file
  void removeProgress(String filePath) {
    _compressionProgress.remove(filePath);
    _isCompressing.remove(filePath);
    _isUploading.remove(filePath);
    _isCompleted.remove(filePath);
    _videoThumbnails.remove(filePath);
    notifyListeners();
  }

  void clearFiles() {
    _filePickerResult = null;
    _compressionProgress.clear();
    _isCompressing.clear();
    _isUploading.clear();
    _isCompleted.clear();
    _videoThumbnails.clear();
    _originalPaths.clear();

    Log.i('FileClearedSuccessfully');

    notifyListeners();
  }

  void removeFile(int index) {
    _filePickerResult?.files.removeAt(index);
    notifyListeners();
  }
}
