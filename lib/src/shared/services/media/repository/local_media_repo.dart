import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:xr_helper/xr_helper.dart';

final localMediaRepoProvider = Provider<LocalMediaRepo>((ref) {
  return LocalMediaRepo();
});

class LocalMediaRepo {
  Future<FilePickerResult?> pickFiles(
    BuildContext context, {
    bool imageUpload = true,
    bool videoUpload = false,
    bool uploadMultiple = true,
    int oldFilesLength = 2,
  }) async {
    try {
      await _getPermission();

      FileType fileType;
      List<String>? allowedExtensions;

      if (imageUpload && !videoUpload) {
        fileType = FileType.image;
      } else if (videoUpload && !imageUpload) {
        fileType = FileType.video;
      } else if (imageUpload && videoUpload) {
        fileType = FileType.custom;
        allowedExtensions = [
          'jpg',
          'jpeg',
          'png',
          'gif',
          'mp4',
          'mov',
          'avi',
          'mkv',
          'webm',
          'm4v'
        ];
      } else {
        fileType = FileType.any;
      }

      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: fileType,
        allowedExtensions: allowedExtensions,
        allowMultiple: uploadMultiple,
      );

      final totalMaxFilesLength = 13 - oldFilesLength;

      //! Check if any file more than 5 mg for images or 50MB total for videos
      if (!context.mounted) return null;

      final isFileSizeExceeded =
          _checkFilesSize(context, result: result, isVideoUpload: videoUpload);

      if (isFileSizeExceeded) return null;

      //! Check if total files more than 13
      final isMoreThan13Files = _checkFilesLength(
        context,
        result: result,
        totalMaxFilesLength: totalMaxFilesLength,
      );

      if (isMoreThan13Files) return null;

      //! Check video count and total size limits
      final isVideoLimitExceeded = _checkVideoLimits(
        context,
        result: result,
        oldFilesLength: oldFilesLength,
      );

      if (isVideoLimitExceeded) return null;

      return result;
    } catch (e) {
      debugPrint('error $e');
      return null;
    }
  }

  bool _checkFilesSize(
    BuildContext context, {
    required FilePickerResult? result,
    bool isVideoUpload = false,
  }) {
    if (result == null || result.files.isEmpty) return false;

    final maxSizeInBytes = isVideoUpload
        ? 50 * 1024 * 1024
        : 5 * 1024 * 1024; // 50MB for videos, 5MB for images

    final anyFileExceedsSize =
        result.files.any((element) => element.size > maxSizeInBytes);

    if (anyFileExceedsSize) {
      if (context.mounted) {
        final message = isVideoUpload
            ? context.tr.videoTooLarge50MB
            : context.tr.maxUploadFileSizeIsOnly5MB;
        context.showBarMessage(message, isError: true);
      }
      return true;
    }

    return false;
  }

  bool _checkFilesLength(
    BuildContext context, {
    required FilePickerResult? result,
    required int totalMaxFilesLength,
  }) {
    if (result != null &&
        result.files.isNotEmpty &&
        result.files.length > totalMaxFilesLength) {
      if (context.mounted) {
        context.showBarMessage(context.tr.maxUploadFilesIsOnly13, isError: true);
      }
      return true;
    }

    return false;
  }

  bool _checkVideoLimits(
    BuildContext context, {
    required FilePickerResult? result,
    required int oldFilesLength,
  }) {
    if (result == null || result.files.isEmpty) return false;

    // Count video files in current selection
    final videoFiles = result.files.where((file) {
      final extension = file.path?.split('.').last.toLowerCase() ?? '';
      return ['mp4', 'mov', 'avi', 'mkv', 'webm', 'm4v'].contains(extension);
    }).toList();

    // Check if more than 3 videos total (assuming oldFilesLength includes existing videos)
    final maxVideos = 3;
    if (videoFiles.length > maxVideos) {
      if (context.mounted) {
        context.showBarMessage(context.tr.maxUploadVideosIsOnly3, isError: true);
      }
      return true;
    }

    // Check total video size (50MB limit for all videos combined)
    final totalVideoSize = videoFiles.fold<int>(
      0,
      (sum, file) => sum + file.size,
    );

    final maxTotalVideoSize = 50 * 1024 * 1024; // 50MB
    if (totalVideoSize > maxTotalVideoSize) {
      if (context.mounted) {
        context.showBarMessage(context.tr.totalVideoSizeExceeds50MB, isError: true);
      }
      return true;
    }

    return false;
  }

  Future<void> _getPermission() async {
    if (await Permission.storage.isGranted) return;
    try {
      await Permission.storage.request();
    } catch (e) {
      debugPrint('error $e');
    }
  }
}
