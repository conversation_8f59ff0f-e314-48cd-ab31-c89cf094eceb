import 'package:connectify_app/src/screens/admin_setup/view/setup_classes/setup_classes_screen.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/auth/view/sign_in_screen/sign_in_screen.dart';
import 'package:connectify_app/src/screens/splash_screen/view/splash_screen.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:xr_helper/xr_helper.dart';

import 'shared/services/app_settings/controller/settings_controller.dart';

//?====================================================================
//!====================== My App ======================================
// * ==================================================================
class BaseApp extends HookConsumerWidget {
  final bool showSignIn;

  const BaseApp({super.key, this.showSignIn = false});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsController = ref.watch(settingsControllerProvider);
    final isUpdateRequired = useState(false);
    useEffect(() {
      Future<void> checkForUpdate() async {
        try {
          final updateInfo = await InAppUpdate.checkForUpdate();
          if (updateInfo.updateAvailability ==
                  UpdateAvailability.updateAvailable &&
              updateInfo.immediateUpdateAllowed) {
            await InAppUpdate.performImmediateUpdate();
          } else if (updateInfo.updateAvailability ==
              UpdateAvailability.updateAvailable) {
            isUpdateRequired.value = true;
          }
        } catch (e) {
          // Log.e('Error checking for app updates: $e');
        }
      }

      checkForUpdate();
      return null;
    }, []);

    return Builder(builder: (context) {
      ScreenUtil.init(context);

      final user = const UserModel().currentUser;

      final signedUser = user != UserModel.empty();

      final loggedInAndHaveViewedSetupScreens = GetStorageService.getLocalData(
            key: LocalKeys.loggedIn,
          ) ??
          false;

      return MaterialApp(
        debugShowCheckedModeBanner: false,
        debugShowMaterialGrid: false,
        //? Localization
        locale: settingsController.locale,
        supportedLocales: AppConsts.supportedLocales,
        localizationsDelegates: AppConsts.localizationsDelegates,
        //? Theme
        theme: ThemeManager().appTheme(),
        // override safearea
        builder: (context, child) {
          return SafeArea(
            top: false,
            child: child!,
          );
        },
        home: Builder(
          builder: (BuildContext context) {
            if (isUpdateRequired.value) {
              return Scaffold(
                body: Center(
                  child: Text(
                    context.tr.updateRequired,
                    textAlign: TextAlign.center,
                    style: context.title,
                  ),
                ),
              );
            }
            if (showSignIn) {
              return const SignInScreen();
            }

            if (signedUser) {
              if (loggedInAndHaveViewedSetupScreens) {
                return const SplashScreen();
                // return const MainScreen();
              } else {
                return const SetupClassesScreen();
              }
            } else {
              return const SplashScreen();
            }
          },
        ),
      );
    });
  }
}
